# Controller Documentation

## Table of Contents
1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Core Components](#core-components)
4. [Action System](#action-system)
5. [Gaming Controls](#gaming-controls)
6. [Usage Examples](#usage-examples)
7. [API Reference](#api-reference)
8. [Best Practices](#best-practices)
9. [Troubleshooting](#troubleshooting)

## Overview

The Controller module is the core action execution system in VizCheck that handles all browser automation and gaming control operations. It provides a unified interface for executing various actions including web navigation, element interaction, keyboard/mouse input, and Xbox controller simulation.

### Key Features
- **Dynamic Action Registration**: Extensible action system with decorator-based registration
- **Browser Automation**: Complete web browser control and interaction
- **Gaming Controls**: Xbox controller simulation and keyboard/mouse gaming input
- **Context Management**: Generic context support for different execution environments
- **Error Handling**: Robust error handling and recovery mechanisms
- **Telemetry Integration**: Built-in performance monitoring and analytics

### Architecture Overview
```
Controller/
├── service.py          # Main Controller class and action implementations
├── views.py           # Pydantic models for action parameters
└── registry/
    ├── service.py     # Action registration and execution system
    └── views.py       # Registry data models and base classes
```

## Architecture

The Controller module follows a layered architecture pattern with clear separation of concerns and dependency injection. The system is designed for extensibility, type safety, and robust error handling.

### High-Level Architecture

```
┌──────────────────────────────────────────────────────────────┐
│                    VizCheck Application                      │
├──────────────────────────────────────────────────────────────┤
│                     Agent Layer                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐   │
│  │   AI Agent      │  │ Message Manager │  │   Browser   │   │
│  │   Orchestrator  │  │                 │  │   Manager   │   │
│  └─────────────────┘  └─────────────────┘  └─────────────┘   │
├──────────────────────────────────────────────────────────────┤
│                   Controller Layer                           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                Controller Service                       │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐  │ │
│  │  │   Action    │  │   Gaming    │  │    Browser      │  │ │
│  │  │ Execution   │  │  Controls   │  │  Automation     │  │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘  │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                Registry System                          │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐  │ │
│  │  │   Action    │  │ Parameter   │  │    Context      │  │ │
│  │  │ Registry    │  │ Validation  │  │   Injection     │  │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘  │ │
│  └─────────────────────────────────────────────────────────┘ │
├──────────────────────────────────────────────────────────────┤
│                   Integration Layer                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐   │
│  │  Playwright │  │ PyAutoGUI   │  │    PyVJoystick      │   │
│  │   Browser   │  │ Keyboard/   │  │  Xbox Controller    │   │
│  │ Automation  │  │   Mouse     │  │    Simulation       │   │
│  └─────────────┘  └─────────────┘  └─────────────────────┘   │
└──────────────────────────────────────────────────────────────┘
```

### Controller Class Structure

The `Controller` class is a generic class that supports different context types and provides the main interface for action execution:

```python
class Controller(Generic[Context]):
    def __init__(self, exclude_actions: list[str] = [], output_model: Optional[Type[BaseModel]] = None)
```

#### Core Attributes
- `registry`: Action registry for managing registered actions
- `start_time`: Timestamp for session tracking and timing
- `gamepad`: VX360Gamepad instance for Xbox controller simulation
- `BUTTON_MAP`: Mapping of button names to Xbox controller constants

### Component Architecture

#### 1. Registry System (`registry/`)
The registry system provides the foundation for action management:

```
Registry System
├── service.py (Registry Class)
│   ├── Action Registration
│   ├── Parameter Model Creation
│   ├── Action Execution
│   └── Context Injection
└── views.py (Data Models)
    ├── RegisteredAction
    ├── ActionModel
    └── ActionRegistry
```

**Key Responsibilities:**
- Dynamic action registration with decorators
- Automatic Pydantic model generation from function signatures
- Validated parameter execution with dependency injection
- Telemetry integration for performance monitoring

#### 2. Action Execution Flow

```
Action Request → Parameter Validation → Context Injection → Action Execution → Result Processing
     ↓                    ↓                    ↓                   ↓                ↓
ActionModel → Pydantic Validation → Dependency Resolution → Function Call → ActionResult
```

**Execution Steps:**
1. **Request Parsing**: ActionModel parsed from input
2. **Parameter Validation**: Pydantic models validate parameters
3. **Context Resolution**: Required dependencies (browser, LLM, etc.) injected
4. **Action Execution**: Registered function called with validated parameters
5. **Result Processing**: ActionResult returned with execution details

#### 3. Gaming Architecture

The gaming system integrates multiple input methods:

```
Gaming Controls
├── Xbox Controller (pyvjoystick)
│   ├── Button Operations (A, B, X, Y, etc.)
│   ├── Joystick Control (Left/Right thumbsticks)
│   ├── Trigger Control (Left/Right triggers)
│   └── D-Pad Control (Directional input)
├── Keyboard Gaming (pyKey)
│   ├── Movement Keys (W, A, S, D)
│   ├── Special Keys (ESC, Enter, etc.)
│   └── Key Combinations
└── Mouse Gaming (pydirectinput)
    ├── Click Operations (Left/Right)
    ├── Movement Control
    └── Position Management
```

#### 4. Browser Integration

Browser automation leverages the BrowserContext system:

```
Browser Automation
├── Page Navigation
│   ├── URL Navigation
│   ├── Tab Management
│   └── History Control
├── Element Interaction
│   ├── Click Operations
│   ├── Text Input
│   ├── Dropdown Selection
│   └── Scroll Control
├── Content Extraction
│   ├── LLM-based Extraction
│   ├── Text Retrieval
│   └── Data Processing
└── Frame Management
    ├── Main Frame Operations
    ├── Iframe Handling
    └── Cross-frame Element Access
```

### Data Flow Architecture

#### 1. Action Registration Flow
```
Function Definition → Decorator Application → Parameter Model Creation → Registry Storage
        ↓                      ↓                        ↓                     ↓
   @registry.action()    Metadata Extraction    Pydantic Model Gen.    Action Storage
```

#### 2. Action Execution Flow
```
Client Request → Action Parsing → Parameter Validation → Context Injection → Execution
      ↓               ↓                   ↓                    ↓              ↓
  ActionModel    Action Lookup      Pydantic Check      Dependency Res.   Function Call
      ↓               ↓                   ↓                    ↓              ↓
Result Processing ← Error Handling ← Telemetry Capture ← Context Cleanup ← Return Value
```

#### 3. Gaming Control Flow
```
Gaming Action → Input Method Selection → Hardware Interface → Game Response
     ↓                    ↓                      ↓                ↓
Action Type         Xbox/Keyboard         Driver/API         Game State
(movement, etc.)    (controller choice)   (pyvjoystick/      (character
                                         pyautogui)          movement)
```

### Key Architectural Patterns

#### 1. Generic Context Support
```python
Context = TypeVar('Context')
class Controller(Generic[Context]):
    # Supports any context type for different execution environments
```

#### 2. Dependency Injection
```python
# Automatic injection based on function signature
if 'browser' in parameter_names:
    extra_args['browser'] = browser
if 'page_extraction_llm' in parameter_names:
    extra_args['page_extraction_llm'] = page_extraction_llm
```

#### 3. Decorator-Based Registration
```python
@self.registry.action('Action description', param_model=ActionModel)
async def action_function(params: ActionModel, browser: BrowserContext):
    # Action implementation
```

#### 4. Error Handling Strategy
```python
try:
    # Action execution
    result = await action.function(validated_params, **extra_args)
except Exception as e:
    raise RuntimeError(f'Error executing action {action_name}: {str(e)}') from e
```

### Scalability Considerations

#### 1. Action Extensibility
- New actions can be added without modifying core system
- Parameter models automatically generated from function signatures
- Type safety maintained through Pydantic validation

#### 2. Context Flexibility
- Generic context support allows different execution environments
- Dependency injection enables loose coupling
- Context-specific actions can be registered conditionally

#### 3. Performance Optimization
- Async/await pattern for non-blocking execution
- Telemetry integration for performance monitoring
- Resource management for gaming hardware

#### 4. Error Recovery
- Robust exception handling at multiple layers
- Graceful degradation for failed actions
- Detailed error reporting for debugging

## Core Components

### 1. Controller Service (`service.py`)

The main `Controller` class provides:
- Action registration and execution
- Browser automation capabilities
- Gaming control functionality
- Context management
- Error handling and logging

### 2. Action Models (`views.py`)

Defines Pydantic models for action parameters:
- `SearchGoogleAction`: Google search parameters
- `GoToUrlAction`: URL navigation parameters
- `ClickElementAction`: Element clicking parameters
- `InputTextAction`: Text input parameters
- `KeyGameControlAction`: Keyboard gaming controls
- `XboxGameControlAction`: Xbox controller controls
- And many more...

### 3. Registry System (`registry/`)

#### Registry Service (`registry/service.py`)
- **Action Registration**: Decorator-based action registration
- **Parameter Model Creation**: Automatic Pydantic model generation
- **Action Execution**: Validated parameter execution
- **Context Injection**: Automatic dependency injection

#### Registry Views (`registry/views.py`)
- `RegisteredAction`: Action metadata and function wrapper
- `ActionModel`: Base class for dynamic action models
- `ActionRegistry`: Container for all registered actions

## Action System

### Action Categories

#### 1. Navigation Actions
- `search_google`: Search queries in Google
- `go_to_url`: Navigate to specific URLs
- `go_back`: Browser back navigation
- `switch_tab`: Tab management
- `open_tab`: New tab creation

#### 2. Element Interaction
- `click_element`: Click on page elements
- `input_text`: Text input into form fields
- `scroll_down`/`scroll_up`: Page scrolling
- `scroll_to_text`: Scroll to specific text
- `get_dropdown_options`: Retrieve dropdown options
- `select_dropdown_option`: Select from dropdowns

#### 3. Content Actions
- `extract_content`: Extract page information using LLM
- `send_keys`: Send keyboard shortcuts and special keys
- `wait`: Pause execution for specified duration

#### 4. Gaming Controls
- `in_game_action_seq_keyboard`: Keyboard-based game controls
- `in_game_action_seq_xbox`: Xbox controller game controls
- `connect_xbox_controller`: Controller connection
- `send_keys_with_delay`: Gaming-specific key input

#### 5. Task Completion
- `done`: Mark task completion with success status

### Action Registration

Actions are registered using the `@registry.action()` decorator:

```python
@self.registry.action('Description of the action', param_model=ActionModel)
async def action_name(params: ActionModel, browser: BrowserContext):
    # Action implementation
    return ActionResult(extracted_content="Action completed")
```

### Parameter Validation

All actions use Pydantic models for parameter validation:

```python
class ClickElementAction(BaseModel):
    index: int
    xpath: Optional[str] = None
```

## Gaming Controls

### Xbox Controller Integration

The Controller includes comprehensive Xbox controller simulation using the `pyvjoystick` library:

#### Button Mapping
```python
BUTTON_MAP = {
    "a": XUSB_BUTTON.XUSB_GAMEPAD_A,
    "b": XUSB_BUTTON.XUSB_GAMEPAD_B,
    "x": XUSB_BUTTON.XUSB_GAMEPAD_X,
    "y": XUSB_BUTTON.XUSB_GAMEPAD_Y,
    "back": XUSB_BUTTON.XUSB_GAMEPAD_BACK,
    "start": XUSB_BUTTON.XUSB_GAMEPAD_START,
    # ... more buttons
}
```

#### Controller Operations
- **Button Actions**: Press, release, and tap operations
- **Joystick Control**: Left and right thumbstick movement
- **Trigger Control**: Left and right trigger pressure
- **D-Pad Control**: Directional pad input

### Keyboard Gaming Controls

For games that require keyboard input:
- **Movement Keys**: W, A, S, D for directional movement
- **Mouse Control**: Left/right click and movement
- **Special Keys**: ESC, Enter, and other game-specific keys
- **Continuous Play**: 10-minute automated gameplay mode

## Usage Examples

### Basic Browser Automation

```python
from browser_use.controller.service import Controller
from browser_use.browser.context import BrowserContext

# Initialize controller
controller = Controller()

# Execute navigation action
action = ActionModel(go_to_url=GoToUrlAction(url="https://example.com"))
result = await controller.act(action, browser_context)
```

### Gaming Control Example

```python
# Xbox controller gaming
xbox_action = ActionModel(
    in_game_action_seq_xbox=XboxGameControlAction(
        is_play_ten_min=False,
        actions=["forward", "a", "view_right"]
    )
)
result = await controller.act(xbox_action, browser_context)

# Keyboard gaming
keyboard_action = ActionModel(
    in_game_action_seq_keyboard=KeyGameControlAction(
        is_play_ten_min=True,
        actions=["forward", "mouse_left", "right"]
    )
)
result = await controller.act(keyboard_action, browser_context)
```

### Custom Action Registration

```python
# Register a custom action
@controller.action("Custom action description")
async def custom_action(param1: str, param2: int, browser: BrowserContext):
    # Custom implementation
    return ActionResult(extracted_content=f"Processed {param1} with {param2}")
```

### Content Extraction

```python
# Extract content using LLM
extract_action = ActionModel(
    extract_content="Extract all product names from this page"
)
result = await controller.act(extract_action, browser_context, page_extraction_llm=llm)
```

## API Reference

### Controller Class

#### Constructor
```python
Controller(exclude_actions: list[str] = [], output_model: Optional[Type[BaseModel]] = None)
```

**Parameters:**
- `exclude_actions`: List of action names to exclude from registration
- `output_model`: Custom output model for the `done` action

#### Methods

##### `act(action, browser_context, **kwargs) -> ActionResult`
Execute a registered action with validated parameters.

**Parameters:**
- `action`: ActionModel instance containing the action and parameters
- `browser_context`: BrowserContext for browser operations
- `page_extraction_llm`: Optional LLM for content extraction
- `sensitive_data`: Optional dictionary for sensitive data replacement
- `available_file_paths`: Optional list of available file paths
- `context`: Optional custom context object

**Returns:** `ActionResult` with execution results

##### `action(description: str, **kwargs)`
Decorator for registering custom actions.

**Parameters:**
- `description`: Human-readable description of the action
- `param_model`: Optional Pydantic model for parameters

### Action Models

#### Navigation Actions
```python
class SearchGoogleAction(BaseModel):
    query: str

class GoToUrlAction(BaseModel):
    url: str

class SwitchTabAction(BaseModel):
    page_id: int

class OpenTabAction(BaseModel):
    url: str
```

#### Interaction Actions
```python
class ClickElementAction(BaseModel):
    index: int
    xpath: Optional[str] = None

class InputTextAction(BaseModel):
    index: int
    text: str
    xpath: Optional[str] = None

class ScrollAction(BaseModel):
    amount: Optional[int] = None
```

#### Gaming Actions
```python
class KeyGameControlAction(BaseModel):
    is_play_ten_min: bool
    actions: List[Literal["forward", "backward", "left", "right", "mouse_move_left", "mouse_move_right"]]

class XboxGameControlAction(BaseModel):
    is_play_ten_min: bool
    actions: List[Literal["forward", "backward", "left", "right", "a", "b", "x", "y", "view_left", "view_right"]]
```

#### Utility Actions
```python
class SendKeysAction(BaseModel):
    keys: str

class DoneAction(BaseModel):
    text: str
    success: bool

class NoParamsAction(BaseModel):
    # Accepts any input and discards it
    pass
```

### Registry API

#### Registry Class
```python
class Registry(Generic[Context]):
    def __init__(self, exclude_actions: list[str] = [])

    def action(self, description: str, param_model: Optional[Type[BaseModel]] = None)
    async def execute_action(self, action_name: str, params: dict, **kwargs) -> Any
    def create_action_model(self, include_actions: Optional[list[str]] = None) -> Type[ActionModel]
    def get_prompt_description(self) -> str
```

### ActionResult Class
```python
class ActionResult:
    extracted_content: Optional[str] = None
    error: Optional[str] = None
    is_done: bool = False
    success: bool = True
    include_in_memory: bool = False
```

## Best Practices

### Action Development

1. **Clear Descriptions**: Write descriptive action descriptions for LLM understanding
2. **Parameter Validation**: Use Pydantic models for all action parameters
3. **Error Handling**: Implement proper exception handling and return meaningful errors
4. **Logging**: Use structured logging for debugging and monitoring
5. **Async Operations**: Make actions async for better performance

### Gaming Controls

1. **Timing**: Use appropriate delays between gaming actions
2. **State Management**: Reset controller state after gaming sessions
3. **Error Recovery**: Implement fallback mechanisms for failed gaming actions
4. **Resource Cleanup**: Properly dispose of gaming resources

### Browser Automation

1. **Element Validation**: Always validate element existence before interaction
2. **Wait Strategies**: Use appropriate wait strategies for dynamic content
3. **Frame Handling**: Consider iframe contexts for element interactions
4. **Timeout Management**: Set reasonable timeouts for operations

### Performance Optimization

1. **Action Batching**: Combine related actions when possible
2. **Resource Management**: Properly manage browser and gaming resources
3. **Memory Usage**: Monitor memory usage in long-running sessions
4. **Telemetry**: Use built-in telemetry for performance monitoring

## Troubleshooting

### Common Issues

#### Action Registration Errors
**Problem**: Actions not being registered properly
**Solution**:
- Check decorator syntax: `@controller.action("description")`
- Ensure action names are unique
- Verify parameter models are valid Pydantic models

#### Gaming Control Issues
**Problem**: Xbox controller not responding
**Solution:**
- Verify `pyvjoystick` installation
- Check controller connection
- Ensure proper button mapping
- Reset controller state: `controller.reset_controller()`

#### Browser Interaction Failures
**Problem**: Element interactions failing
**Solution:**
- Verify element index exists in selector map
- Check for iframe contexts
- Ensure page is fully loaded
- Use appropriate wait strategies

#### Parameter Validation Errors
**Problem**: Action parameters not validating
**Solution:**
- Check Pydantic model definitions
- Verify parameter types match model
- Ensure required fields are provided
- Review parameter naming conventions

### Debugging Tips

1. **Enable Debug Logging**: Set logging level to DEBUG for detailed information
2. **Check Action Registry**: Use `registry.get_prompt_description()` to see registered actions
3. **Validate Parameters**: Test parameter models independently
4. **Monitor Telemetry**: Use telemetry data to identify performance issues
5. **Test Incrementally**: Test actions individually before combining

### Error Codes

| Error Type | Description | Solution |
|------------|-------------|----------|
| `ActionNotFound` | Action not registered | Check action name and registration |
| `ParameterValidation` | Invalid parameters | Review parameter model and values |
| `BrowserContextMissing` | Browser context required | Provide valid BrowserContext |
| `ElementNotFound` | Element index invalid | Verify element exists and is accessible |
| `GamepadError` | Gaming control failure | Check controller connection and drivers |

---

**Documentation Version**: 1.0
**Last Updated**: July 2025
**Maintained by**: VizCheck Development Team
