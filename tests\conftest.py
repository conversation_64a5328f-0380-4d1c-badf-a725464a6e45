"""
VizCheck Test Configuration

Pytest configuration and fixtures for the VizCheck test suite.
"""

import pytest
import sys
import os
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Test environment setup
os.environ.setdefault("TESTING", "true")
os.environ.setdefault("LOG_LEVEL", "ERROR")


@pytest.fixture
def temp_dir():
    """Provide a temporary directory for tests"""
    temp_path = tempfile.mkdtemp()
    yield temp_path
    shutil.rmtree(temp_path, ignore_errors=True)


@pytest.fixture
def mock_config():
    """Provide a mock config object for testing"""
    config = Mock()
    config.get_app_name.return_value = "VizCheck"
    config.get_version.return_value = "2.0.0"
    config.get_debug_mode.return_value = False
    config.get_start_url.return_value = "https://example.com"
    config.get_max_num_of_steps.return_value = 10
    config.get_output_dir.return_value = "./test_outputs"
    config.get_azure_api_key.return_value = "test_key"
    return config


@pytest.fixture
def sample_config_content():
    """Provide sample configuration content for testing"""
    return """
[App]
app_name = VizCheck
version = 2.0.0
debug = false

[settings]
start_url = https://example.com
max_num_of_steps = 10
output_dir = ./test_outputs
"""


def pytest_configure(config):
    """Configure pytest with custom markers"""
    config.addinivalue_line(
        "markers", "unit: Unit tests for individual functions"
    )
    config.addinivalue_line(
        "markers", "integration: Integration tests for module interaction"
    )
    config.addinivalue_line(
        "markers", "browser: Browser-related functionality tests"
    )
    config.addinivalue_line(
        "markers", "api: API endpoint tests"
    )
    config.addinivalue_line(
        "markers", "core: Core functionality tests"
    )
    config.addinivalue_line(
        "markers", "slow: Slow running tests"
    )


def pytest_collection_modifyitems(config, items):
    """Automatically add markers to tests based on their names and locations"""
    for item in items:
        # Add markers based on test names
        if "browser" in item.name.lower():
            item.add_marker(pytest.mark.browser)
        if "api" in item.name.lower() or "ticket" in item.name.lower():
            item.add_marker(pytest.mark.api)
        if "integration" in item.name.lower():
            item.add_marker(pytest.mark.integration)
        else:
            item.add_marker(pytest.mark.unit)
        
        # Add core marker to main test files
        if "test_all_pytest" in str(item.fspath):
            item.add_marker(pytest.mark.core)


def pytest_runtest_setup(item):
    """Setup before each test"""
    # Clear any singleton instances to prevent test interference
    if hasattr(item.module, 'utils'):
        # Reset singleton decorator state if it exists
        try:
            import utils
            if hasattr(utils.singleton, '_instances'):
                utils.singleton._instances.clear()
        except (ImportError, AttributeError):
            pass


def pytest_sessionstart(session):
    """Called after the Session object has been created"""
    print("\n🧪 VizCheck Test Suite Starting...")
    print(f"📁 Test directory: {Path(__file__).parent}")
    print(f"🐍 Python version: {sys.version.split()[0]}")


def pytest_sessionfinish(session, exitstatus):
    """Called after whole test run finished"""
    if exitstatus == 0:
        print("\n✅ All tests completed successfully!")
    else:
        print(f"\n❌ Tests completed with exit status: {exitstatus}")


def pytest_report_header(config):
    """Add custom header to test report"""
    return [
        "VizCheck Professional Test Suite",
        f"Project: {project_root.name}",
        f"Test Config: {config.inifile or 'default'}"
    ]
