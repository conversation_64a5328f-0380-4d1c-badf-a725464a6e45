# VizCheck Deployment Guide

Comprehensive guide for deploying VizCheck in production environments, covering installation, configuration, monitoring, and maintenance procedures.

## Table of Contents

1. [Deployment Overview](#deployment-overview)
2. [System Requirements](#system-requirements)
3. [Installation Methods](#installation-methods)
4. [Production Configuration](#production-configuration)
5. [Security Considerations](#security-considerations)
6. [Monitoring and Logging](#monitoring-and-logging)
7. [Maintenance Procedures](#maintenance-procedures)
8. [Troubleshooting](#troubleshooting)
9. [Scaling Considerations](#scaling-considerations)

## Deployment Overview

VizCheck can be deployed in several configurations:

- **Standalone Desktop Application**: Single-user GUI application
- **Server Deployment**: Multi-user web interface
- **Container Deployment**: Docker-based deployment
- **Cloud Deployment**: Azure/AWS cloud environments

## System Requirements

### Minimum Requirements

| Component   | Requirement                                  |
|-------------|----------------------------------------------|
| **OS**      | Windows 10+, Ubuntu 18.04+, macOS 10.15+   |
| **Python**  | 3.11 or higher                              |
| **Memory**  | 4 GB RAM                                     |
| **Storage** | 2 GB free space                             |
| **Browser** | Chrome/Chromium 90+                         |
| **Network** | Internet connectivity for AI services       |

### Recommended Requirements

| Component   | Requirement                      |
|-------------|----------------------------------|
| **Memory**  | 8 GB RAM                         |
| **Storage** | 10 GB free space                 |
| **CPU**     | 4+ cores                         |
| **Network** | High-speed internet (100+ Mbps) |

### Dependencies

- **Python 3.11+**
- **Google Chrome or Chromium**
- **Azure OpenAI API access**
- **Playwright browser automation**

## Installation Methods

### Method 1: Pre-built Executable (Recommended)

#### Windows
1. Download the latest release from the build artifacts
2. Extract `VizCheck_<version>.zip`
3. Run `VizCheck.exe`

#### Configuration
1. Copy `config/config.ini` to the application directory
2. Set environment variables or create `.env` file
3. Configure Azure OpenAI credentials

### Method 2: Source Installation

#### Step 1: Clone Repository
```bash
git clone https://dev.azure.com/ascendionava/AWE/_git/vizcheck
cd vizcheck
```

#### Step 2: Create Virtual Environment
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# Linux/macOS
source venv/bin/activate
```

#### Step 3: Install Dependencies
```bash
pip install -r requirements.txt
playwright install chromium
```

#### Step 4: Configure Application
```bash
cp config/config.ini.example config/config.ini
# Edit config.ini with your settings
```

#### Step 5: Set Environment Variables
```bash
# Windows
set AZURE_API_KEY=your_api_key_here

# Linux/macOS
export AZURE_API_KEY=your_api_key_here
```

#### Step 6: Run Application
```bash
python app.py
```

### Method 3: Docker Deployment

#### Dockerfile
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    && rm -rf /var/lib/apt/lists/*

# Install Chrome
RUN wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google-chrome.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable

# Copy application files
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

# Install Playwright
RUN playwright install chromium

EXPOSE 7860

CMD ["python", "app.py"]
```

#### Docker Compose
```yaml
version: '3.8'
services:
  vizcheck:
    build: .
    ports:
      - "7860:7860"
    environment:
      - AZURE_API_KEY=${AZURE_API_KEY}
      - AZURE_ENDPOINT=${AZURE_ENDPOINT}
      - LOGGING_LEVEL=INFO
    volumes:
      - ./outputs:/app/outputs
      - ./config:/app/config
    restart: unless-stopped
```

## Production Configuration

### Environment Variables

Create a production `.env` file:
```env
# Azure OpenAI Configuration
AZURE_API_KEY=your_production_api_key
AZURE_DEPLOYMENT=gpt-4o
AZURE_ENDPOINT=https://your-endpoint.openai.azure.com/
OPENAI_API_VERSION=2024-05-01-preview

# Application Settings
LOGGING_LEVEL=INFO
ANONYMIZED_TELEMETRY=true

# Security Settings
ACS_END_POINT=your_acs_endpoint
ACS_ACCESS_KEY=your_acs_key
```

### Configuration File

Production `config/config.ini`:
```ini
[App]
app_name = VizCheck
version = 2.0.0
description = Production VizCheck Instance
debug = false
email_service = true

[settings]
start_url = https://www.xbox.com/en-US/play
max_num_of_steps = 30
preview_image_path = ./assets/images/white.jpg
load_in_browser = false
output_dir = /var/log/vizcheck/outputs
```

### Web Server Configuration

#### Nginx Reverse Proxy
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:7860;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### SSL Configuration
```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    location / {
        proxy_pass http://localhost:7860;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Security Considerations

### API Key Management
- Store API keys in environment variables
- Use Azure Key Vault for production secrets
- Rotate API keys regularly
- Monitor API usage and costs

### Network Security
- Use HTTPS for all communications
- Implement firewall rules
- Restrict access to management interfaces
- Use VPN for administrative access

### Application Security
- Run with minimal privileges
- Disable debug mode in production
- Implement rate limiting
- Monitor for suspicious activity

### Browser Security
- Use sandboxed browser instances
- Disable unnecessary browser features
- Monitor browser resource usage
- Implement timeout mechanisms

## Monitoring and Logging

### Application Monitoring

#### Health Check Endpoint
```python
@app.route('/health')
def health_check():
    return {
        'status': 'healthy',
        'version': config.get_version(),
        'timestamp': datetime.now().isoformat()
    }
```

#### Metrics Collection
- Task execution times
- Success/failure rates
- Resource utilization
- API response times

### Logging Configuration

#### Production Logging
```python
import logging
import logging.handlers

# Configure rotating file handler
handler = logging.handlers.RotatingFileHandler(
    '/var/log/vizcheck/app.log',
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5
)

# Set format
formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
handler.setFormatter(formatter)

# Configure logger
logger = logging.getLogger('vizcheck')
logger.setLevel(logging.INFO)
logger.addHandler(handler)
```

#### Log Aggregation
- Use ELK Stack (Elasticsearch, Logstash, Kibana)
- Configure log shipping to centralized systems
- Set up alerting for error conditions

### Performance Monitoring

#### System Metrics
- CPU usage
- Memory consumption
- Disk I/O
- Network traffic

#### Application Metrics
- Request latency
- Throughput
- Error rates
- Queue depths

## Maintenance Procedures

### Regular Maintenance

#### Daily Tasks
- Check application logs
- Monitor resource usage
- Verify API connectivity
- Review error reports

#### Weekly Tasks
- Update dependencies
- Review security logs
- Check disk space
- Backup configuration

#### Monthly Tasks
- Update browser versions
- Review API usage costs
- Update documentation
- Security audit

### Update Procedures

#### Application Updates
1. Download new version
2. Backup current installation
3. Stop application services
4. Install new version
5. Update configuration if needed
6. Start services
7. Verify functionality

#### Dependency Updates
```bash
# Update Python packages
pip install --upgrade -r requirements.txt

# Update Playwright browsers
playwright install --with-deps
```

### Backup Procedures

#### Configuration Backup
```bash
# Backup configuration
tar -czf config-backup-$(date +%Y%m%d).tar.gz config/

# Backup outputs
tar -czf outputs-backup-$(date +%Y%m%d).tar.gz outputs/
```

#### Database Backup (if applicable)
```bash
# Backup application data
pg_dump vizcheck_db > vizcheck_backup_$(date +%Y%m%d).sql
```

## Troubleshooting

### Common Issues

#### Application Won't Start
1. Check Python version compatibility
2. Verify all dependencies installed
3. Check configuration file syntax
4. Verify environment variables

#### Browser Connection Issues
1. Check Chrome installation
2. Verify port 9222 availability
3. Check firewall settings
4. Restart browser service

#### API Authentication Errors
1. Verify API key validity
2. Check endpoint configuration
3. Monitor API rate limits
4. Review network connectivity

#### Performance Issues
1. Monitor resource usage
2. Check browser memory leaks
3. Review task complexity
4. Optimize configuration

### Diagnostic Commands

#### System Information
```bash
# Check Python version
python --version

# Check installed packages
pip list

# Check browser version
google-chrome --version

# Check system resources
top
df -h
```

#### Application Diagnostics
```bash
# Test configuration
python -c "from config import Config; c=Config(); print(c.get_app_name())"

# Test API connectivity
curl -H "Authorization: Bearer $AZURE_API_KEY" $AZURE_ENDPOINT/v1/models
```

## Scaling Considerations

### Horizontal Scaling
- Deploy multiple instances behind load balancer
- Use shared storage for outputs
- Implement session affinity if needed

### Vertical Scaling
- Increase memory allocation
- Add CPU cores
- Optimize browser resource usage

### Cloud Deployment
- Use Azure Container Instances
- Implement auto-scaling policies
- Monitor costs and usage

---

**Next Steps**: 
- Review [Configuration Guide](Configuration_Guide.md) for detailed settings
- Check [Troubleshooting Guide](Troubleshooting.md) for common issues
- See [Performance Optimization Guide](Performance_Guide.md) for tuning
