#!/bin/bash

# VizCheck Build Script for Unix/Linux/macOS
# This shell script activates the virtual environment and runs the Python build script
#
# Usage: ./build_vizcheck.sh [major|minor|patch] [--default-assets]

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${CYAN}[$timestamp] INFO: $message${NC}"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[$timestamp] SUCCESS: $message${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}[$timestamp] WARNING: $message${NC}"
            ;;
        "ERROR")
            echo -e "${RED}[$timestamp] ERROR: $message${NC}"
            ;;
        *)
            echo -e "[$timestamp] $level: $message"
            ;;
    esac
}

# Get executable name from app.spec
get_exe_name_from_spec() {
    local spec_file="app.spec"

    if [[ ! -f "$spec_file" ]]; then
        log "WARNING" "app.spec not found in build_scripts directory, using default name 'VizCheck'"
        echo "VizCheck"
        return
    fi

    # Extract name from EXE section
    local exe_name=$(grep -A 20 "exe.*=.*EXE" "$spec_file" | grep -o "name.*=.*['\"][^'\"]*['\"]" | head -1 | sed "s/.*['\"]\\([^'\"]*\\)['\"].*/\\1/")

    if [[ -n "$exe_name" ]]; then
        log "INFO" "Extracted executable name from app.spec: $exe_name"
        echo "$exe_name"
        return
    fi

    # Fallback: search anywhere in file
    exe_name=$(grep -o "name.*=.*['\"][^'\"]*['\"]" "$spec_file" | head -1 | sed "s/.*['\"]\\([^'\"]*\\)['\"].*/\\1/")

    if [[ -n "$exe_name" ]]; then
        log "INFO" "Found executable name in app.spec: $exe_name"
        echo "$exe_name"
        return
    fi

    log "WARNING" "Could not find executable name in app.spec, using default 'VizCheck'"
    echo "VizCheck"
}

# Check and activate virtual environment
check_virtual_environment() {
    log "INFO" "Checking virtual environment..."

    # Get project root directory
    local parent_dir=$(dirname "$PWD")
    local venv_path="$parent_dir/venv"
    local venv_activate="$venv_path/bin/activate"

    if [[ ! -f "$venv_activate" ]]; then
        log "ERROR" "Virtual environment not found at $venv_path"
        log "ERROR" "Please create a virtual environment first:"
        log "ERROR" "  cd $parent_dir"
        log "ERROR" "  python -m venv venv"
        log "ERROR" "  source venv/bin/activate"
        log "ERROR" "  pip install -r requirements.txt"
        return 1
    fi

    log "INFO" "Activating virtual environment..."
    source "$venv_activate"

    # Check if Python is available in venv
    if ! command -v python &> /dev/null; then
        log "ERROR" "Python is not available in virtual environment"
        log "ERROR" "Please check your virtual environment setup"
        return 1
    fi

    PYTHON_CMD="python"
    local python_version=$($PYTHON_CMD --version 2>&1)
    log "INFO" "Using Python from virtual environment: $python_version"

    return 0
}

# Check prerequisites
check_prerequisites() {
    log "INFO" "Checking prerequisites..."

    # First activate virtual environment
    if ! check_virtual_environment; then
        return 1
    fi
    
    # Determine if we're in root or build_scripts folder and set paths accordingly
    BUILD_SCRIPT_PATH=""
    WORKING_DIR=""

    # Check if we're in the build_scripts folder
    if [[ -f "build_vizcheck.py" ]]; then
        log "INFO" "Running from build_scripts folder"
        BUILD_SCRIPT_PATH="build_vizcheck.py"
        WORKING_DIR="$PWD"
    elif [[ -f "build_scripts/build_vizcheck.py" ]]; then
        # Check if we're in the root folder
        log "INFO" "Running from project root folder"
        BUILD_SCRIPT_PATH="build_scripts/build_vizcheck.py"
        WORKING_DIR="$PWD/build_scripts"
    else
        log "ERROR" "build_vizcheck.py not found"
        log "ERROR" "This script must be run from either:"
        log "ERROR" "  - Project root folder (where app.py is located)"
        log "ERROR" "  - build_scripts folder (where build_vizcheck.py is located)"
        log "ERROR" ""
        log "ERROR" "Current directory: $(pwd)"
        return 1
    fi
    
    # Check if specs directory exists
    if [[ ! -d "specs" ]]; then
        log "ERROR" "specs directory not found in build_scripts directory"
        return 1
    fi

    # Check if there are any spec files
    if ! ls specs/*.spec >/dev/null 2>&1; then
        log "ERROR" "No .spec files found in specs directory"
        return 1
    fi
    
    if [[ ! -f "$parent_dir/config/config.ini" ]]; then
        log "ERROR" "config/config.ini not found in parent directory"
        return 1
    fi

    # Log detected executable name
    local exe_name=$(get_exe_name_from_spec)
    log "INFO" "Detected executable name: $exe_name"

    log "SUCCESS" "All prerequisites met"
    return 0
}

# Run the build
run_build() {
    log "INFO" "Starting VizCheck build process..."

    # Build command arguments
    local build_args=()
    if [[ -n "$INCREMENT_TYPE" ]]; then
        build_args+=("--increment" "$INCREMENT_TYPE")
        log "INFO" "Version increment: $INCREMENT_TYPE"
    fi
    if [[ -n "$SPEC_FILE" ]]; then
        build_args+=("--spec" "$SPEC_FILE")
        log "INFO" "Using spec file: $SPEC_FILE"
    fi
    if [[ "$USE_DEFAULT_ASSETS" == "true" ]]; then
        build_args+=("--default-assets")
        log "INFO" "Using default assets fallback"
    fi

    # Change to the correct working directory and run the build
    pushd "$WORKING_DIR" > /dev/null

    if $PYTHON_CMD "$BUILD_SCRIPT_PATH" "${build_args[@]}"; then
        log "SUCCESS" "Build completed successfully!"
        popd > /dev/null
        return 0
    else
        log "ERROR" "Build failed with exit code $?"
        popd > /dev/null
        return 1
    fi
}

# Show build results
show_results() {
    log "INFO" "Checking build results..."
    
    local parent_dir=$(dirname "$PWD")
    local dist_dir="$parent_dir/dist"
    
    if [[ -d "$dist_dir" ]]; then
        log "INFO" "Contents of dist folder:"
        
        while IFS= read -r -d '' file; do
            local relative_path=$(basename "$file")
            if [[ -d "$file" ]]; then
                log "INFO" "  DIR:  $relative_path/"
            else
                local size=$(du -h "$file" | cut -f1)
                log "INFO" "  FILE: $relative_path ($size)"
            fi
        done < <(find "$dist_dir" -maxdepth 1 -mindepth 1 -print0 | sort -z)
        
        # Check for main executable (directly in dist folder)
        local exe_name=$(get_exe_name_from_spec)
        if [[ -f "$dist_dir/$exe_name.exe" ]] || [[ -f "$dist_dir/$exe_name" ]]; then
            log "SUCCESS" "Main executable found: $exe_name"
        fi

        # Check for versioned folders
        local versioned_folders=$(find "$dist_dir" -maxdepth 1 -name "${exe_name}_*" -type d | wc -l)
        if [[ $versioned_folders -gt 0 ]]; then
            log "SUCCESS" "Found $versioned_folders versioned build(s)"
        fi
    else
        log "WARNING" "Dist folder not found"
    fi
}

# Parse command line arguments
parse_arguments() {
    INCREMENT_TYPE=""
    SPEC_FILE=""
    USE_DEFAULT_ASSETS="false"

    while [[ $# -gt 0 ]]; do
        case "$1" in
            "major"|"minor"|"patch")
                INCREMENT_TYPE="$1"
                shift
                ;;
            "--spec")
                SPEC_FILE="$2"
                shift 2
                ;;
            "--default-assets")
                USE_DEFAULT_ASSETS="true"
                shift
                ;;
            "list"|"--list-specs")
                # Determine script location first
                if [[ -f "build_vizcheck.py" ]]; then
                    python build_vizcheck.py --list-specs
                elif [[ -f "build_scripts/build_vizcheck.py" ]]; then
                    pushd "build_scripts" > /dev/null
                    python build_vizcheck.py --list-specs
                    popd > /dev/null
                else
                    log "ERROR" "build_vizcheck.py not found"
                fi
                exit 0
                ;;
            "-h"|"--help")
                echo "Usage: $0 [options] [increment]"
                echo ""
                echo "Options:"
                echo "  --spec <file>     Use specific spec file (from specs/ directory)"
                echo "  --default-assets  Use runtime_assets as fallback"
                echo "  --list-specs      List all available spec files"
                echo "  -h, --help        Show this help message"
                echo ""
                echo "Increment:"
                echo "  major             Increment major version (0.1.0 -> 1.0.0)"
                echo "  minor             Increment minor version (0.1.0 -> 0.2.0)"
                echo "  patch             Increment patch version (0.1.0 -> 0.1.1)"
                echo ""
                echo "Examples:"
                echo "  $0                        # Build with default spec"
                echo "  $0 patch                  # Increment patch version"
                echo "  $0 --spec console         # Use console.spec"
                echo "  $0 --default-assets       # Use runtime_assets as fallback"
                echo "  $0 --spec debug minor     # Use debug.spec and increment minor"
                echo "  $0 list                   # List available spec files"
                exit 0
                ;;
            *)
                # If it's not a known option, treat as spec file (for backward compatibility)
                if [[ -z "$SPEC_FILE" && ! "$1" =~ ^- ]]; then
                    SPEC_FILE="$1"
                else
                    log "ERROR" "Invalid argument: $1"
                    log "ERROR" "Use -h or --help for usage information"
                    exit 1
                fi
                shift
                ;;
        esac
    done
}

# Main function
main() {
    # Parse arguments
    parse_arguments "$@"

    # Display header
    echo ""
    echo -e "${CYAN}========================================${NC}"
    echo -e "${CYAN}       VizCheck Build Script${NC}"
    echo -e "${CYAN}========================================${NC}"
    echo ""

    # Check prerequisites
    if ! check_prerequisites; then
        log "ERROR" "Prerequisites check failed. Exiting."
        exit 1
    fi

    # Run build
    if run_build; then
        build_success=true
    else
        build_success=false
    fi

    # Show results
    show_results

    # Final status
    echo ""
    echo -e "${CYAN}========================================${NC}"
    if [[ "$build_success" == "true" ]]; then
        echo -e "${GREEN}         BUILD SUCCESSFUL!${NC}"
        echo -e "${GREEN}Check the dist folder for your executable${NC}"
        exit_code=0
    else
        echo -e "${RED}          BUILD FAILED!${NC}"
        echo -e "${RED}Check the error messages above${NC}"
        exit_code=1
    fi
    echo -e "${CYAN}========================================${NC}"
    echo ""

    exit $exit_code
}

# Note: Directory checking is now handled in the main function

# Run main function
main "$@"
