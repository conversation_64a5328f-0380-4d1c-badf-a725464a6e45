import time
import gradio as gr
from langchain_openai import AzureChatOpenAI
from browser_use import Agent
from browser_use.browser.browser import <PERSON><PERSON><PERSON>, BrowserConfig
import asyncio
from dotenv import load_dotenv
import datetime
import os
from playwright.sync_api import sync_playwright
import sys
from config import Config
from browser_manager import launch_chrome, close_chrome, setup_emulator_chrome_debugging
from utils import modify_gif
from report_generator import parse_result
from utils import ensure_folder_exists, extract_country
import logging

logger = logging.getLogger(__name__)

config = Config()
if sys.stdout is None:
    sys.stdout = open(os.devnull, 'w')
if sys.stderr is None:
    sys.stderr = open(os.devnull, 'w')

logger.info(f"max_num_of_steps: {config.get_max_num_of_steps()}")

# cache_dir = os.path.join(os.getenv("USERPROFILE"), ".cache", "ms-playwright")
# 
# if not os.path.exists(cache_dir):
#     try:
#         subprocess.run(["playwright", "install"], check=True)
#         print("Playwright successfully installed.")
#     except Exception as e:
#         print(f"Error installing Playwright: {e}")

ensure_folder_exists(config.get_output_dir())

load_dotenv()

async def run_agent(task_text=None):
    logger.info(f"Execution Started...\nTask: {str(task_text)}")

    current_time = datetime.datetime.now()

    chrome_config = BrowserConfig(
        cdp_url="http://localhost:9222",  # Connect to running Chrome instance
        headless=False,  # Not relevant for an existing instance
        disable_security=True,
        extra_chromium_args=[]
    )
    country = extract_country(task_text)
    url_country_path = "en-US"
    if country and isinstance(country, str):
        try:
            url_country_path = Config.get_available_countries().get(country, "en-US")
        except Exception as e:
            url_country_path = "en-US"        
    azure_deployment = os.getenv("AZURE_DEPLOYMENT")
    openai_api_version = os.getenv("OPENAI_API_VERSION")
    azure_endpoint = os.getenv("AZURE_ENDPOINT")
    azure_deployment = "gpt-4o" if azure_deployment is None else azure_deployment
    openai_api_version = "2024-05-01-preview" if openai_api_version is None else azure_deployment
    azure_endpoint = "https://michelangelovision.openai.azure.com/" if azure_endpoint is None else azure_endpoint
    xbox_urls = {
        "Home Page": f"https://www.xbox.com/{url_country_path}/play",
        "User Profile": f"https://www.xbox.com/{url_country_path}/play/user",
        "Gallery": f"https://www.xbox.com/{url_country_path}/play/gallery/all-games",
        "All Games": f"https://www.xbox.com/{url_country_path}/play/gallery/all-games",
        "Indies": f"https://www.xbox.com/{url_country_path}/play/gallery/indies",
        "Strategies": f"https://www.xbox.com/{url_country_path}/play/gallery/strategies",
        "Shooters": f"https://www.xbox.com/{url_country_path}/play/gallery/shooters",
        "Simulations": f"https://www.xbox.com/{url_country_path}/play/gallery/simulations"
    }

    task_text += f"\n:For Country {country} use following urls " + "\n  ".join(
        [f"{url} for {name}" for name, url in xbox_urls.items()]
    )
    agent = Agent(
        task=task_text,
        llm=AzureChatOpenAI(
            azure_deployment=azure_deployment,
            openai_api_version=openai_api_version,
            azure_endpoint=azure_endpoint,
            api_key=os.getenv("AZURE_API_KEY"),
        ),
        browser=Browser(config=chrome_config),
        generate_gif=os.path.join(config.get_output_dir(), "agent_history.gif"),
        max_actions_per_step=1,
        initial_actions= [{"go_to_url":{"url":f"https://www.xbox.com/{url_country_path}/play"}},]
    )   # en-US, es-AR
    agent.controller.start_time = time.time()
    result = await agent.run(max_steps=config.get_max_num_of_steps())
    execution_time = (datetime.datetime.now() - current_time).seconds

    return result, execution_time

def execute_task(task_text):
    try:
        # launch_chrome()
        # try:
        #     setup_emulator_chrome_debugging()
        # except Exception as e:
        #     pass
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result, exec_time = loop.run_until_complete(run_agent(task_text))
        try:
            close_chrome()
        except Exception as e:
            pass

        parsed_data = parse_result(result, task_text, exec_time)
        logger.info(f"**Execution Time:** {exec_time} seconds")
        try:
            modify_gif()
            return parsed_data, os.path.join(config.get_output_dir(), "output.gif")
        except Exception as e:
            return parsed_data, os.path.join(config.get_output_dir(), "agent_history.gif")
    except Exception as e:
        logger.info(f"Error: {e}\n#####Please execute the task again.#####")
        return config.get_empty_df(), config.get_preview_image_path()



custom_css = """
<style>
.fixed-bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #1e1e1e; /* Optional: Match background theme */
    padding: 10px 20px;
    z-index: 999;
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.5);
}
.task-box {
    width: 100%;
    margin-bottom: 10px;
}
</style>
"""

with gr.Blocks(css=".container { max-width: 900px !important; }") as demo:

    gr.HTML(
"""
<head>
    <title>Test Automation</title>
</head>
<body>
    <div style="text-align: center; padding: 10px;">
        <h2>Test Automation</h2>
        <p style="color: #555;">A simple UI to execute tasks and upload files with AI assistance</p>
    </div>
</body>
"""
    )
    with gr.Row():
        with gr.Column():
            gr.Markdown("### 📋 Test Script")
            test_script = gr.DataFrame(value=config.get_empty_df(), interactive=False)

        with gr.Column():
            gr.Markdown("### 📸 Preview Image")
            preview_image = gr.Image(config.get_preview_image_path, label="Preview")

    with gr.Row(elem_classes="fixed-bottom"):
        task_input = gr.Textbox(
            label="",
            placeholder="Ask anything or describe the task...",
            interactive=True,
            container=False,
            elem_classes="task-box"
        )

    with gr.Row(elem_classes="fixed-bottom", scale=0):
        execute_btn = gr.Button("Execute Task")
        save_btn = gr.Button("💾 Save Test Case")

    gr.HTML("<a href='/static/updated_test_report.html' target='_blank'>View Test Report</a>")

    execute_btn.click(
        fn=execute_task,
        inputs=task_input,
        outputs=[test_script, preview_image]
    )

    # def get_report_link(task_output):
    #     return f"<a href='{task_output[1]}' target='_blank'>View Test Report</a>"

    # # Bind the function to the 'report_link'
    # report_link.change(fn=get_report_link, inputs=execute_btn, outputs=report_link)

demo.launch(inbrowser=config.get_load_in_browser())
