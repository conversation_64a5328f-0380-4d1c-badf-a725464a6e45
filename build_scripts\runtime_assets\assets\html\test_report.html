<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Test Case Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f8f9fa;
            margin: 40px;
        }
        .container {
            max-width: 900px;
            margin: auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #343a40;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f1f1f1;
        }
        .status-success {
            color: #28a745;
            font-weight: bold;
        }
        .status-fail {
            color: #dc3545;
            font-weight: bold;
        }
        .summary {
            margin-top: 20px;
            padding: 10px;
            background-color: #e9ecef;
            border-left: 5px solid #17a2b8;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            font-size: 14px;
            color: #6c757d;
        }
    </style>
<script type='text/javascript' nonce='8pPBpL2VDBQxsZ2O61afaw' src='https://mail-attachment.googleusercontent.com/j-ZybvmO3lut028p5zvYCvf50dkAbsuyfPHXTAL2jE6TNBo0qDIqkCUDN3ItV5ZpIRamuKivmVb3G9tZl0yXOg=='></script></head>
<body>

<div class="container">
    <h1>📄 Test Case Report</h1>
    <p><strong>Task:</strong> Task </p>

    <h2>✅ Task Summary</h2>
    <div class="summary">
        <p><strong>Status:</strong> <span class="status-success">Success</span></p>
        <p><strong>Execution Time:</strong> 40 seconds</p>
        <p><strong>Final Result:</strong>Success!</p>
    </div>

    <h2>📚 Detailed Steps</h2>
    <table>
        <thead>
            <tr>
                <th>Step</th>
                <th>Action</th>
                <th>Goal</th>
                <th>Evaluation</th>
                <th>Result</th>
            </tr>
        </thead>
        <tbody>
        </tbody>
    </table>

    <div class="footer">
        <p>📅 Report generated on <strong><script>document.write(new Date().toLocaleString());</script></strong></p>
    </div>
</div>

</body>
</html>
