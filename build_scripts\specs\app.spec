# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

datas = []
# Only collect data files for packages that exist
try:
    datas += collect_data_files('gradio_client')
except:
    pass
try:
    datas += collect_data_files('gradio')
except:
    pass
try:
    browser_use_data = collect_data_files('browser_use')
    datas += browser_use_data
    print(f"Collected {len(browser_use_data)} browser_use data files")
    for data_file in browser_use_data:
        print(f"  - {data_file}")
except Exception as e:
    print(f"Failed to collect browser_use data files: {e}")
    # Will try manual collection later after setting up paths
try:
    datas += collect_data_files('safehttpx')
except:
    pass
try:
    datas += collect_data_files('groovy')
except:
    pass

# Only add specific files if they exist
import os
import sys
from pathlib import Path

# Get current working directory and determine project root
current_dir = Path(os.getcwd())
print(f"Current working directory: {current_dir}")

# Check if we're running from root (has app.py) or from build_scripts
if (current_dir / 'app.py').exists():
    # Running from project root
    root_dir = current_dir
    print("Running from project root")
elif (current_dir.parent / 'app.py').exists():
    # Running from build_scripts
    root_dir = current_dir.parent
    print("Running from build_scripts directory")
else:
    # Fallback: assume we're in project root
    root_dir = current_dir
    print("Using current directory as root (fallback)")

print(f"Project root directory: {root_dir}")

# Add project root to Python path so PyInstaller can find browser_use package
if str(root_dir) not in sys.path:
    sys.path.insert(0, str(root_dir))
    print(f"Added {root_dir} to Python path")

# Define paths relative to project root
system_prompt_path = root_dir / 'browser_use' / 'agent' / 'system_prompt.md'
builddom_js_path = root_dir / 'browser_use' / 'dom' / 'buildDomTree.js'

print(f"Looking for system_prompt.md at: {system_prompt_path}")
print(f"Looking for buildDomTree.js at: {builddom_js_path}")

if system_prompt_path.exists():
    datas += [(str(system_prompt_path), 'browser_use/agent')]
    print("[OK] Added system_prompt.md")
else:
    print("[MISSING] system_prompt.md not found")

if builddom_js_path.exists():
    datas += [(str(builddom_js_path), 'browser_use/dom')]
    print("[OK] Added buildDomTree.js")
else:
    print("[MISSING] buildDomTree.js not found")

hiddenimports = []
# Only add imports for packages that exist
try:
    hiddenimports += ['playwright.driver']
    hiddenimports += collect_submodules('playwright')
except:
    pass
try:
    hiddenimports += ['pydantic.deprecated.decorator', 'pydantic']
except:
    pass
try:
    hiddenimports += ['langchain_openai', 'langchain_core']
except:
    pass


a = Analysis(
    [str(root_dir / 'app.py')],  # Path to main app from project root
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
    module_collection_mode={
        'gradio': 'py',  # Collect gradio package as source .py files
    },
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='WizCheck',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    icon=None,
)
