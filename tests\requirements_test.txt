# VizCheck Professional Test Dependencies
# Install with: pip install -r tests/requirements_test.txt

# Core testing framework
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-mock>=3.11.1
pytest-asyncio>=0.21.1

# Test reporting and utilities
pytest-html>=3.2.0
pytest-benchmark>=4.0.0
coverage>=7.2.7

# HTTP and API testing
httpx>=0.24.1
requests-mock>=1.11.0

# Additional testing tools
parameterized>=0.9.0
freezegun>=1.2.2

# Environment management
python-dotenv>=1.0.0

# Note: Core dependencies (pandas, pillow, etc.) are inherited from main requirements.txt

# Azure DevOps testing
azure-devops>=7.1.0b3

# Browser automation testing
selenium>=4.11.0

# Async testing
aiohttp>=3.8.5
asynctest>=0.13.0

# Database testing (if needed)
sqlalchemy>=2.0.0
alembic>=1.11.0

# Environment management
python-decouple>=3.8
pydantic>=2.0.0

# Logging and monitoring
structlog>=23.1.0
loguru>=0.7.0

# File handling
openpyxl>=3.1.0
xlsxwriter>=3.1.0

# Date/time utilities
python-dateutil>=2.8.2
pytz>=2023.3

# Network testing
requests-mock>=1.11.0
httpretty>=1.1.4

# Process testing
psutil>=5.9.0

# Temporary file handling
tempfile-utils>=0.1.0

# XML/HTML parsing
beautifulsoup4>=4.12.0
lxml>=4.9.0

# JSON handling
jsonschema>=4.18.0

# Regular expressions
regex>=2023.6.3

# Path utilities
pathlib2>=2.3.7

# String utilities
fuzzywuzzy>=0.18.0
python-levenshtein>=0.21.0

# Cryptography (for secure testing)
cryptography>=41.0.0

# Time zone handling
zoneinfo>=0.2.1

# Memory profiling
memory-profiler>=0.60.0
pympler>=0.9

# Network utilities
netifaces>=0.11.0
ipaddress>=1.0.23

# System information
platform>=1.0.8
distro>=1.8.0

# File system utilities
watchdog>=3.0.0
pathtools>=0.1.2

# Compression utilities
zipfile36>=0.1.3
tarfile>=0.1.0

# Image processing for testing
opencv-python>=4.8.0
numpy>=1.24.0

# PDF processing for testing
PyPDF2>=3.0.0
reportlab>=4.0.0

# Excel processing for testing
xlrd>=2.0.1
xlwt>=1.3.0

# CSV processing
csvkit>=1.1.1

# Configuration file testing
toml>=0.10.2
yaml>=0.2.5
pyyaml>=6.0.1

# Command line testing
click>=8.1.0
argparse>=1.4.0

# Threading and multiprocessing testing
concurrent-futures>=3.1.1
multiprocessing-logging>=0.3.4

# Retry mechanisms for testing
tenacity>=8.2.0
retrying>=1.3.4

# Rate limiting testing
ratelimit>=2.2.1
slowapi>=0.1.9

# Caching for testing
cachetools>=5.3.0
redis>=4.6.0

# Message queuing for testing
celery>=5.3.0
kombu>=5.3.0

# Email testing
email-validator>=2.0.0
premailer>=3.10.0

# Phone number testing
phonenumbers>=8.13.0

# URL testing
validators>=0.20.0
furl>=2.1.3

# Color utilities for testing
colorama>=0.4.6
termcolor>=2.3.0

# Progress bars for testing
tqdm>=4.65.0
progressbar2>=4.2.0

# Debugging utilities
pdb++>=0.10.3
ipdb>=0.13.13

# Memory debugging
objgraph>=3.5.0
guppy3>=3.1.3

# Performance profiling
line-profiler>=4.0.0
py-spy>=0.3.14

# Static analysis
bandit>=1.7.5
safety>=2.3.0

# Documentation testing
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0

# API documentation testing
swagger-ui-bundle>=0.0.9
redoc>=2.1.0

# Environment variables testing
environs>=9.5.0
python-environ>=0.4.54

# Secrets management testing
keyring>=24.2.0
cryptography>=41.0.0

# Internationalization testing
babel>=2.12.0
gettext>=4.0

# Timezone testing
pytz>=2023.3
tzlocal>=5.0.1

# Locale testing
locale>=1.0.0
babel>=2.12.0
