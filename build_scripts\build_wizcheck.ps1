# WizCheck Build Script for PowerShell
# This PowerShell script activates the virtual environment and runs the Python build script

param(
    [ValidateSet("major", "minor", "patch")]
    [string]$Increment,
    [string]$Spec,
    [switch]$ListSpecs,
    [switch]$DefaultAssets,
    [switch]$NoCleanup,
    [switch]$Verbose
)

# Set error action preference
$ErrorActionPreference = "Stop"

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "INFO" { "White" }
        "WARNING" { "Yellow" }
        "ERROR" { "Red" }
        "SUCCESS" { "Green" }
        default { "White" }
    }
    
    Write-Host "[$timestamp] $Level`: $Message" -ForegroundColor $color
}

function Get-ExeNameFromSpec {
    try {
        $specPath = "app.spec"

        if (-not (Test-Path $specPath)) {
            Write-Log "app.spec not found in build_scripts directory, using default name 'WizCheck'" "WARNING"
            return "WizCheck"
        }

        $specContent = Get-Content $specPath -Raw

        # Look for name parameter in EXE section
        if ($specContent -match "exe\s*=\s*EXE\s*\((.*?)\)" -and $Matches[1] -match "name\s*=\s*['\`"]([^'\`"]+)['\`"]") {
            $exeName = $Matches[1]
            Write-Log "Extracted executable name from app.spec: $exeName"
            return $exeName
        }

        # Fallback: search anywhere in file
        if ($specContent -match "name\s*=\s*['\`"]([^'\`"]+)['\`"]") {
            $exeName = $Matches[1]
            Write-Log "Found executable name in app.spec: $exeName"
            return $exeName
        }

        Write-Log "Could not find executable name in app.spec, using default 'WizCheck'" "WARNING"
        return "WizCheck"
    } catch {
        Write-Log "Error reading app.spec: $($_.Exception.Message), using default 'WizCheck'" "WARNING"
        return "WizCheck"
    }
}

function Test-VirtualEnvironment {
    Write-Log "Checking virtual environment..."

    # Get project root directory
    $projectRoot = Split-Path -Parent $PWD
    $venvPath = Join-Path $projectRoot "venv"
    $venvActivateScript = Join-Path $venvPath "Scripts\Activate.ps1"

    if (-not (Test-Path $venvActivateScript)) {
        Write-Log "Virtual environment not found at $venvPath" "ERROR"
        Write-Log "Please create a virtual environment first:" "ERROR"
        Write-Log "  cd $projectRoot" "ERROR"
        Write-Log "  python -m venv venv" "ERROR"
        Write-Log "  venv\Scripts\Activate.ps1" "ERROR"
        Write-Log "  pip install -r requirements.txt" "ERROR"
        return $false
    }

    Write-Log "Activating virtual environment..."
    try {
        & $venvActivateScript
        Write-Log "Virtual environment activated successfully"
    } catch {
        Write-Log "Failed to activate virtual environment: $($_.Exception.Message)" "ERROR"
        return $false
    }

    # Check if Python is available in venv
    try {
        $pythonVersion = python --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Log "Using Python from virtual environment: $pythonVersion"
        } else {
            throw "Python not found in virtual environment"
        }
    } catch {
        Write-Log "Python is not available in virtual environment" "ERROR"
        Write-Log "Please check your virtual environment setup" "ERROR"
        return $false
    }

    return $true
}

function Test-Prerequisites {
    Write-Log "Checking prerequisites..."

    # First activate virtual environment
    if (-not (Test-VirtualEnvironment)) {
        return $false
    }
    
    # Determine if we're in root or build_scripts folder and set paths accordingly
    $script:BuildScriptPath = ""
    $script:WorkingDir = ""

    # Check if we're in the build_scripts folder
    if (Test-Path "build_wizcheck.py") {
        Write-Log "Running from build_scripts folder"
        $script:BuildScriptPath = "build_wizcheck.py"
        $script:WorkingDir = $PWD
    } elseif (Test-Path "build_scripts\build_wizcheck.py") {
        # Check if we're in the root folder
        Write-Log "Running from project root folder"
        $script:BuildScriptPath = "build_scripts\build_wizcheck.py"
        $script:WorkingDir = Join-Path $PWD "build_scripts"
    } else {
        Write-Log "build_wizcheck.py not found" "ERROR"
        Write-Log "This script must be run from either:" "ERROR"
        Write-Log "  - Project root folder (where app.py is located)" "ERROR"
        Write-Log "  - build_scripts folder (where build_wizcheck.py is located)" "ERROR"
        Write-Log "" "ERROR"
        Write-Log "Current directory: $PWD" "ERROR"
        return $false
    }
    
    # Check if specs directory exists
    if (-not (Test-Path "specs")) {
        Write-Log "specs directory not found in build_scripts directory" "ERROR"
        return $false
    }

    # Check if there are any spec files
    $specFiles = Get-ChildItem "specs\*.spec" -ErrorAction SilentlyContinue
    if (-not $specFiles) {
        Write-Log "No .spec files found in specs directory" "ERROR"
        return $false
    }
    
    if (-not (Test-Path "$parentDir\config\config.ini")) {
        Write-Log "config\config.ini not found in parent directory" "ERROR"
        return $false
    }

    # Log detected executable name
    $exeName = Get-ExeNameFromSpec
    Write-Log "Detected executable name: $exeName"

    Write-Log "All prerequisites met" "SUCCESS"
    return $true
}

function Start-Build {
    Write-Log "Starting WizCheck build process..."

    # Build command arguments
    $buildArgs = @()
    if ($Increment) {
        $buildArgs += "--increment", $Increment
        Write-Log "Version increment: $Increment"
    }
    if ($Spec) {
        $buildArgs += "--spec", $Spec
        Write-Log "Using spec file: $Spec"
    }
    if ($DefaultAssets) {
        $buildArgs += "--default-assets"
        Write-Log "Using default assets fallback"
    }

    try {
        # Change to the correct working directory and run the Python build script
        Push-Location $script:WorkingDir

        if ($Verbose) {
            python $script:BuildScriptPath @buildArgs
        } else {
            python $script:BuildScriptPath @buildArgs 2>&1 | Tee-Object -Variable buildOutput
        }

        if ($LASTEXITCODE -eq 0) {
            Write-Log "Build completed successfully!" "SUCCESS"
            return $true
        } else {
            Write-Log "Build failed with exit code $LASTEXITCODE" "ERROR"
            if (-not $Verbose -and $buildOutput) {
                Write-Log "Build output:" "ERROR"
                $buildOutput | ForEach-Object { Write-Log $_ "ERROR" }
            }
            return $false
        }
    } catch {
        Write-Log "Error during build: $($_.Exception.Message)" "ERROR"
        return $false
    } finally {
        # Return to original directory
        Pop-Location
    }
}

function Show-BuildResults {
    Write-Log "Checking build results..."
    
    $parentDir = Split-Path -Parent $PWD
    $distPath = Join-Path $parentDir "dist"
    
    if (Test-Path $distPath) {
        $distItems = Get-ChildItem $distPath | Sort-Object Name
        
        Write-Log "Contents of dist folder:"
        foreach ($item in $distItems) {
            if ($item.PSIsContainer) {
                Write-Log "  DIR:  $($item.Name)/" "INFO"
            } else {
                $size = [math]::Round($item.Length / 1MB, 2)
                Write-Log "  FILE: $($item.Name) ($size MB)" "INFO"
            }
        }
        
        # Get executable name from app.spec
        $exeName = Get-ExeNameFromSpec

        # Look for the main executable (directly in dist folder)
        $exePath = Join-Path $distPath "$exeName.exe"
        if (Test-Path $exePath) {
            $exeSize = [math]::Round((Get-Item $exePath).Length / 1MB, 2)
            Write-Log "Main executable: $exeName.exe ($exeSize MB)" "SUCCESS"
        }

        # Look for versioned folders
        $versionedFolders = $distItems | Where-Object {
            $_.PSIsContainer -and $_.Name -match "$exeName_\d+\.\d+\.\d+"
        }
        
        if ($versionedFolders) {
            Write-Log "Versioned builds found:" "SUCCESS"
            foreach ($folder in $versionedFolders) {
                Write-Log "  $($folder.Name)" "SUCCESS"
            }
        }
    } else {
        Write-Log "Dist folder not found" "WARNING"
    }
}

function Main {
    # Handle list specs command
    if ($ListSpecs) {
        # Determine script location first
        if (Test-Path "build_wizcheck.py") {
            python build_wizcheck.py --list-specs
        } elseif (Test-Path "build_scripts\build_wizcheck.py") {
            Push-Location "build_scripts"
            python build_wizcheck.py --list-specs
            Pop-Location
        } else {
            Write-Host "ERROR: build_wizcheck.py not found" -ForegroundColor Red
        }
        exit 0
    }

    # Display header
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "       WizCheck Build Script" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""

    # Check prerequisites
    if (-not (Test-Prerequisites)) {
        Write-Log "Prerequisites check failed. Exiting." "ERROR"
        exit 1
    }
    
    # Start build
    $buildSuccess = Start-Build
    
    # Show results
    Show-BuildResults
    
    # Final status
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    if ($buildSuccess) {
        Write-Host "         BUILD SUCCESSFUL!" -ForegroundColor Green
        Write-Host "Check the dist folder for your executable" -ForegroundColor Green
    } else {
        Write-Host "          BUILD FAILED!" -ForegroundColor Red
        Write-Host "Check the error messages above" -ForegroundColor Red
    }
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    
    # Exit with appropriate code
    exit $(if ($buildSuccess) { 0 } else { 1 })
}

# Run main function
Main
