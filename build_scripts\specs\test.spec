# -*- mode: python ; coding: utf-8 -*-
# Test version - minimal configuration for testing
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

datas = []
# Minimal data collection for test builds
try:
    datas += collect_data_files('gradio_client')
except:
    pass

hiddenimports = []
# Minimal imports for test builds
try:
    hiddenimports += ['pydantic.deprecated.decorator', 'pydantic']
except:
    pass

a = Analysis(
    ['../../test.py'],  # Path to test file from specs/ directory
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='test',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='test',
)
