# VizCheck API Reference

Complete API documentation for the VizCheck browser automation platform, covering all classes, methods, and interfaces for developers.

## Table of Contents

1. [Core APIs](#core-apis)
2. [Configuration API](#configuration-api)
3. [Agent API](#agent-api)
4. [Browser Management API](#browser-management-api)
5. [Build System API](#build-system-api)
6. [Ticket Management API](#ticket-management-api)
7. [Utility Functions](#utility-functions)
8. [Examples](#examples)

## Core APIs

### Main Application Entry Point

#### `app.py`

The main application module providing the Gradio interface and task execution.

```python
def execute_task(task_text: str) -> tuple[Any, int]
```
**Description**: Executes an automation task using natural language.
- **Parameters**: 
  - `task_text` (str): Natural language description of the task
- **Returns**: Tuple of (result, execution_time)
- **Example**:
```python
result, time = execute_task("Navigate to Google and search for Python")
```

```python
async def run_agent(task_text: str) -> tuple[Any, int]
```
**Description**: Asynchronous agent execution with country detection.
- **Parameters**: 
  - `task_text` (str): Task description with optional country context
- **Returns**: Tuple of (agent_result, execution_time)

## Configuration API

### Config Class

#### `config.Config`

Singleton configuration manager for application settings.

```python
class Config:
    def __init__(self, config_file: str = 'config/config.ini')
```

#### Core Methods

```python
def get_app_name() -> str
```
**Description**: Returns the application name.
- **Default**: 'VizCheck'

```python
def get_version() -> str
```
**Description**: Returns the application version.
- **Default**: '2.0.0'

```python
def get_debug_mode() -> bool
```
**Description**: Returns debug mode status.
- **Default**: False

```python
def get_start_url() -> str
```
**Description**: Returns the default starting URL.
- **Default**: 'https://example.com'

```python
def get_max_num_of_steps() -> int
```
**Description**: Returns maximum automation steps.
- **Default**: 45

```python
def get_output_dir() -> str
```
**Description**: Returns output directory path.
- **Default**: './outputs'

#### Security Methods

```python
def get_azure_api_key() -> str | None
```
**Description**: Returns Azure OpenAI API key from environment.

```python
def get_logging_level() -> str
```
**Description**: Returns logging level.
- **Default**: 'info'

```python
def get_acs_end_point() -> str | None
```
**Description**: Returns Azure Communication Service endpoint.

```python
def get_acs_access_key() -> str | None
```
**Description**: Returns Azure Communication Service access key.

#### Configuration Management

```python
def update_config_file(self, config_file: str) -> None
```
**Description**: Updates configuration file path and reloads settings.

## Agent API

### Agent Class

#### `browser_use.agent.service.Agent`

Core AI agent for browser automation tasks.

```python
class Agent(Generic[Context]):
    def __init__(
        self,
        task: str,
        llm: BaseChatModel,
        browser: Browser | None = None,
        browser_context: BrowserContext | None = None,
        controller: Controller[Context] = Controller(),
        sensitive_data: Optional[Dict[str, str]] = None,
        initial_actions: Optional[List[Dict[str, Dict[str, Any]]]] = None,
        **kwargs
    )
```

#### Core Methods

```python
async def run(self, max_steps: int = 100) -> AgentHistoryList
```
**Description**: Execute the automation task.
- **Parameters**: 
  - `max_steps` (int): Maximum number of steps to execute
- **Returns**: History of agent actions and results

```python
async def step(self, step_info: Optional[AgentStepInfo] = None) -> None
```
**Description**: Execute one step of the task.

```python
async def get_next_action(self, input_messages: list[BaseMessage]) -> AgentOutput
```
**Description**: Get next action from LLM based on current state.

### Agent Settings

#### `browser_use.agent.views.AgentSettings`

Configuration model for agent behavior.

```python
class AgentSettings(BaseModel):
    use_vision: bool = True
    use_vision_for_planner: bool = False
    save_conversation_path: Optional[str] = None
    max_failures: int = 3
    retry_delay: int = 10
    max_input_tokens: int = 128000
    validate_output: bool = False
    generate_gif: bool | str = False
    include_attributes: list[str] = [
        'title', 'type', 'name', 'role', 'tabindex',
        'aria-label', 'placeholder', 'value', 'alt', 'aria-expanded'
    ]
```

## Browser Management API

### Browser Manager

#### `browser_manager.py`

Browser lifecycle management functions.

```python
def launch_chrome() -> None
```
**Description**: Launches Chrome with remote debugging enabled.

```python
def close_chrome() -> None
```
**Description**: Closes all Chrome instances and processes.

```python
def is_chrome_running() -> bool
```
**Description**: Checks if Chrome is currently running.

#### Android Emulator Support

```python
def start_android_emulator(avd_name: str = "Pixel_9_Pro") -> None
```
**Description**: Starts Android emulator with specified AVD.

```python
def launch_chrome_on_emulator() -> None
```
**Description**: Launches Chrome on the Android emulator.

```python
def forward_remote_debugging_port() -> None
```
**Description**: Forwards port 9222 for remote debugging.

```python
def setup_emulator_chrome_debugging() -> None
```
**Description**: Complete setup for emulator Chrome debugging.

## Build System API

### VizCheckBuilder Class

#### `build_scripts.build_vizcheck.VizCheckBuilder`

Automated build system for VizCheck applications.

```python
class VizCheckBuilder:
    def __init__(
        self, 
        increment_type: Optional[str] = None, 
        spec_file: Optional[str] = None
    )
```

#### Core Methods

```python
def build() -> bool
```
**Description**: Execute complete build process.
- **Returns**: True if build successful, False otherwise

```python
def list_available_specs() -> None
```
**Description**: Display available PyInstaller spec files.

#### Build Configuration

```python
def _resolve_spec_file(self, spec_file: Optional[str]) -> Path
```
**Description**: Resolve spec file path from name or path.

```python
def _get_exe_name_from_spec() -> str
```
**Description**: Extract executable name from spec file.

## Ticket Management API

### ADO Work Item Manager

#### `ticket_api.py`

FastAPI application for Azure DevOps ticket management.

```python
app = FastAPI(title="Azure DevOps Ticket API")
```

#### Request Models

```python
class CreateWorkItemRequest(BaseModel):
    work_item_type: str
    title: str
    description: Optional[str] = None
    fields: Optional[Dict[str, str]] = None

class UpdateWorkItemRequest(BaseModel):
    fields_to_update: Dict[str, str]

class WIQLQueryRequest(BaseModel):
    query: str
```

## Utility Functions

### Country Detection

#### `utils.py`

```python
def extract_country(text: str) -> str | None
```
**Description**: Extracts country information from task text.
- **Parameters**: 
  - `text` (str): Input text to analyze
- **Returns**: Country code or None

### File Management

```python
def ensure_folder_exists(folder_path: str) -> None
```
**Description**: Creates folder if it doesn't exist.

## Examples

### Basic Agent Usage

```python
from browser_use.agent.service import Agent
from browser_use.browser import Browser
from langchain_openai import AzureChatOpenAI
import asyncio

# Configure LLM
llm = AzureChatOpenAI(
    azure_deployment="gpt-4o",
    api_key="your-api-key",
    azure_endpoint="your-endpoint"
)

# Create browser instance
browser = Browser()

# Create and run agent
agent = Agent(
    task="Navigate to Google and search for Python tutorials",
    llm=llm,
    browser=browser
)

# Execute task
async def run_automation():
    result = await agent.run(max_steps=10)
    return result

# Run the automation
result = asyncio.run(run_automation())
```

### Configuration Usage

```python
from config import Config

# Initialize configuration
config = Config()

# Access settings
app_name = config.get_app_name()
max_steps = config.get_max_num_of_steps()
output_dir = config.get_output_dir()

# Update configuration file
config.update_config_file('custom/config.ini')
```

### Build System Usage

```python
from build_scripts.build_vizcheck import VizCheckBuilder

# Create builder with version increment
builder = VizCheckBuilder(increment_type='patch', spec_file='debug')

# Execute build
success = builder.build()

if success:
    print("Build completed successfully")
else:
    print("Build failed")
```

### Browser Management

```python
from browser_manager import launch_chrome, close_chrome, is_chrome_running

# Check Chrome status
if not is_chrome_running():
    launch_chrome()

# Perform automation tasks
# ...

# Clean up
close_chrome()
```

---

**Next Steps**: 
- See [Configuration Guide](Configuration_Guide.md) for setup details
- Review [Agent Documentation](Agent_Documentation.md) for advanced usage
- Check [Browser Documentation](Browser_Documentation.md) for browser automation specifics
