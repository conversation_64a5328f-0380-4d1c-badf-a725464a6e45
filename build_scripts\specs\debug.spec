# -*- mode: python ; coding: utf-8 -*-
# Debug version - with debug symbols and verbose output
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

datas = []
# Only collect data files for packages that exist
try:
    datas += collect_data_files('gradio_client')
except:
    pass
try:
    datas += collect_data_files('gradio')
except:
    pass
try:
    datas += collect_data_files('browser_use')
except:
    pass
try:
    datas += collect_data_files('safehttpx')
except:
    pass
try:
    datas += collect_data_files('groovy')
except:
    pass

# Only add specific files if they exist
import os
if os.path.exists('../../browser_use/agent/system_prompt.md'):
    datas += [('../../browser_use/agent/system_prompt.md', 'browser_use/agent')]
if os.path.exists('../../browser_use/dom/buildDomTree.js'):
    datas += [('../../browser_use/dom/buildDomTree.js', 'browser_use/dom')]

hiddenimports = []
# Only add imports for packages that exist
try:
    hiddenimports += ['playwright.driver']
    hiddenimports += collect_submodules('playwright')
except:
    pass
try:
    hiddenimports += ['pydantic.deprecated.decorator', 'pydantic']
except:
    pass
try:
    hiddenimports += ['langchain_openai', 'langchain_core']
except:
    pass


a = Analysis(
    ['../../app.py'],  # Path to main app from specs/ directory
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,  # No optimization for debugging
    module_collection_mode={
        'gradio': 'py',  # Collect gradio package as source .py files
    },
    module_collection_mode={
        'gradio': 'py',  # Collect gradio package as source .py files
    },
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='WizCheck_Debug',
    debug=True,  # Debug mode enabled
    bootloader_ignore_signals=False,
    strip=False,  # Don't strip debug symbols
    upx=False,   # Disable UPX compression for debugging
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    icon=None,
)
