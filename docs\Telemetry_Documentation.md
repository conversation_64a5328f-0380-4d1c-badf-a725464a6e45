# Telemetry Documentation

## Table of Contents
1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Core Components](#core-components)
4. [Event Types](#event-types)
5. [Privacy and Security](#privacy-and-security)
6. [Configuration](#configuration)
7. [Usage Examples](#usage-examples)
8. [API Reference](#api-reference)
9. [Best Practices](#best-practices)
10. [Troubleshooting](#troubleshooting)

## Overview

The Telemetry module provides anonymized usage analytics and performance monitoring for VizCheck. It captures key metrics about agent behavior, controller actions, and system performance to help improve the platform while maintaining user privacy. The system is built on PostHog analytics platform and follows strict anonymization principles.

### Key Features
- **Anonymized Data Collection**: All telemetry data is anonymized with no personal information
- **Opt-out Support**: Users can completely disable telemetry via environment variables
- **Performance Monitoring**: Track agent performance, step execution, and system metrics
- **Action Analytics**: Monitor controller action usage and registration patterns
- **Error Tracking**: Capture and analyze error patterns for system improvement
- **Singleton Pattern**: Ensures consistent telemetry instance across the application

### Use Cases
- **Performance Analysis**: Monitor agent execution times and step counts
- **Feature Usage**: Track which actions and features are most commonly used
- **Error Analysis**: Identify common failure patterns and error scenarios
- **System Optimization**: Gather data to optimize performance and reliability
- **Product Development**: Inform feature development based on usage patterns

## Architecture

The Telemetry module follows a singleton pattern with event-driven architecture for efficient and consistent data collection across the VizCheck platform.

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    VizCheck Application                     │
├─────────────────────────────────────────────────────────────┤
│                   Application Components                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │     Agent       │  │   Controller    │  │   Registry  │  │
│  │    Service      │  │    Service      │  │   Service   │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
│           │                     │                   │       │
│           ▼                     ▼                   ▼       │
├─────────────────────────────────────────────────────────────┤
│                    Telemetry Module                         │
│  ┌────────────────────────────────────────────────────────┐ │
│  │              ProductTelemetry Service                  │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐ │ │
│  │  │   Event     │  │    User     │  │   PostHog       │ │ │
│  │  │ Processing  │  │ Management  │  │  Integration    │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘ │ │
│  └────────────────────────────────────────────────────────┘ │
│  ┌────────────────────────────────────────────────────────┐ │
│  │                  Event Models                          │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐ │ │
│  │  │   Agent     │  │ Controller  │  │     Base        │ │ │
│  │  │   Events    │  │   Events    │  │    Events       │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘ │ │
│  └────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                   External Integration                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │    PostHog      │  │   File System   │  │ Environment │  │
│  │   Analytics     │  │   User ID       │  │  Variables  │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### Component Architecture

#### 1. ProductTelemetry Service (Singleton)
The main telemetry service that manages all data collection:

```
ProductTelemetry
├── Initialization
│   ├── Environment Configuration
│   ├── PostHog Client Setup
│   └── User ID Management
├── Event Processing
│   ├── Event Capture
│   ├── Data Anonymization
│   └── Error Handling
├── User Management
│   ├── Anonymous User ID Generation
│   ├── User ID Persistence
│   └── Privacy Protection
└── Integration
    ├── PostHog Communication
    ├── Debug Logging
    └── Configuration Management
```

#### 2. Event System Architecture
Event-driven architecture with typed event models:

```
Event System
├── Base Event Model
│   ├── Abstract Event Interface
│   ├── Property Extraction
│   └── Name Definition
├── Specialized Events
│   ├── Agent Events (Run, Step, End)
│   ├── Controller Events (Function Registration)
│   └── Custom Events
└── Event Processing
    ├── Property Serialization
    ├── Data Validation
    └── PostHog Transmission
```

### Data Flow Architecture

```
Event Creation → Property Extraction → Anonymization → PostHog Transmission → Analytics
      ↓                ↓                    ↓                ↓                ↓
  Event Model → Dictionary Format → User ID Mapping → API Call → Dashboard
      ↓                ↓                    ↓                ↓                ↓
Component Code → Event Properties → Anonymous Data → PostHog → Insights
```

**Processing Pipeline:**
1. **Event Creation**: Components create typed event objects
2. **Property Extraction**: Event properties extracted via dataclass serialization
3. **User ID Association**: Anonymous user ID attached to event
4. **PostHog Transmission**: Event sent to PostHog analytics platform
5. **Error Handling**: Failed transmissions logged but don't block execution

### Privacy Architecture

#### 1. Anonymization Strategy
```
Privacy Protection
├── Anonymous User IDs
│   ├── UUID4 Generation
│   ├── Local File Storage
│   └── No Personal Information
├── Data Minimization
│   ├── Essential Metrics Only
│   ├── No Sensitive Content
│   └── Aggregated Data
└── Opt-out Mechanism
    ├── Environment Variable Control
    ├── Complete Disable Option
    └── No Data Collection When Disabled
```

#### 2. Data Storage Strategy
- **Local User ID**: Stored in `~/.cache/browser_use/telemetry_user_id`
- **No Personal Data**: Only anonymous usage metrics collected
- **Opt-out Respected**: No data collection when telemetry disabled

## Core Components

### 1. ProductTelemetry Service (`service.py`)

The main telemetry service implemented as a singleton:

#### Key Responsibilities
- **Event Capture**: Receive and process telemetry events
- **User Management**: Generate and manage anonymous user IDs
- **PostHog Integration**: Send data to PostHog analytics platform
- **Privacy Protection**: Ensure all data is anonymized
- **Configuration Management**: Handle telemetry enable/disable settings

#### Core Features
- **Singleton Pattern**: Ensures single instance across application
- **Environment Configuration**: Configurable via environment variables
- **Error Resilience**: Telemetry failures don't affect application functionality
- **Debug Support**: Optional debug logging for development

### 2. Event Models (`views.py`)

#### BaseTelemetryEvent
Abstract base class for all telemetry events:
- Defines event interface with name and properties
- Automatic property extraction from dataclass fields
- Type safety for event creation

#### Specialized Event Types
- **AgentRunTelemetryEvent**: Agent execution start events
- **AgentStepTelemetryEvent**: Individual agent step events
- **AgentEndTelemetryEvent**: Agent execution completion events
- **ControllerRegisteredFunctionsTelemetryEvent**: Controller action registration events

### 3. Integration Points

#### Agent Integration
The Agent service captures comprehensive execution metrics:
- **Run Events**: Task start, model configuration, version information
- **Step Events**: Individual step execution, actions taken, errors encountered
- **End Events**: Completion status, total duration, token usage

#### Controller Integration
The Controller registry captures action usage patterns:
- **Function Registration**: Track which actions are registered and used
- **Action Parameters**: Monitor action parameter schemas and usage

#### Registry Integration
The Registry service captures action management metrics:
- **Action Registration**: Track action registration patterns
- **Parameter Models**: Monitor parameter model complexity and usage

## Event Types

### Agent Events

#### AgentRunTelemetryEvent
Captures agent execution start information:
```python
@dataclass
class AgentRunTelemetryEvent(BaseTelemetryEvent):
    agent_id: str                    # Anonymous agent identifier
    use_vision: bool                 # Vision capability usage
    task: str                        # Task description (anonymized)
    model_name: str                  # LLM model name
    chat_model_library: str          # Chat model library used
    version: str                     # VizCheck version
    source: str                      # Execution source/context
    name: str = 'agent_run'         # Event name
```

#### AgentStepTelemetryEvent
Captures individual agent step execution:
```python
@dataclass
class AgentStepTelemetryEvent(BaseTelemetryEvent):
    agent_id: str                    # Anonymous agent identifier
    step: int                        # Step number in execution
    step_error: list[str]            # Errors encountered in step
    consecutive_failures: int        # Count of consecutive failures
    actions: list[dict]              # Actions taken in step
    name: str = 'agent_step'        # Event name
```

#### AgentEndTelemetryEvent
Captures agent execution completion:
```python
@dataclass
class AgentEndTelemetryEvent(BaseTelemetryEvent):
    agent_id: str                    # Anonymous agent identifier
    steps: int                       # Total steps executed
    max_steps_reached: bool          # Whether max steps limit reached
    is_done: bool                    # Task completion status
    success: bool | None             # Success status
    total_input_tokens: int          # Total tokens consumed
    total_duration_seconds: float    # Total execution time
    errors: Sequence[str | None]     # Errors encountered
    name: str = 'agent_end'         # Event name
```

### Controller Events

#### ControllerRegisteredFunctionsTelemetryEvent
Captures controller action registration:
```python
@dataclass
class ControllerRegisteredFunctionsTelemetryEvent(BaseTelemetryEvent):
    registered_functions: list[RegisteredFunction]  # List of registered actions
    name: str = 'controller_registered_functions'   # Event name

@dataclass
class RegisteredFunction:
    name: str                        # Action name
    params: dict[str, Any]           # Parameter schema
```

### Custom Events

#### Creating Custom Events
```python
@dataclass
class CustomTelemetryEvent(BaseTelemetryEvent):
    custom_property: str
    numeric_value: int
    name: str = 'custom_event'
    
    # Properties automatically extracted from dataclass fields
    # name field excluded from properties
```

## Privacy and Security

### Anonymization Principles

#### 1. Anonymous User Identification
```python
# User ID generation and management
def user_id(self) -> str:
    if not os.path.exists(self.USER_ID_PATH):
        os.makedirs(os.path.dirname(self.USER_ID_PATH), exist_ok=True)
        with open(self.USER_ID_PATH, 'w') as f:
            new_user_id = str(uuid.uuid4())  # Anonymous UUID4
            f.write(new_user_id)
        self._curr_user_id = new_user_id
    else:
        with open(self.USER_ID_PATH, 'r') as f:
            self._curr_user_id = f.read()
    return self._curr_user_id
```

#### 2. Data Minimization
- **No Personal Information**: No names, emails, or personal identifiers
- **Task Anonymization**: Task descriptions may be anonymized or excluded
- **Essential Metrics Only**: Only performance and usage metrics collected
- **No Sensitive Content**: No user data, passwords, or sensitive information

#### 3. Opt-out Mechanism
```python
# Complete telemetry disable via environment variable
telemetry_disabled = os.getenv('ANONYMIZED_TELEMETRY', 'true').lower() == 'false'
if telemetry_disabled:
    self._posthog_client = None  # No data collection
```

### Security Measures

#### 1. Local Data Protection
- **User ID Storage**: Stored in user's cache directory
- **File Permissions**: Standard user file permissions
- **No Network Exposure**: User ID never transmitted in clear text

#### 2. Transmission Security
- **HTTPS Only**: All data transmitted over secure connections
- **PostHog Security**: Leverages PostHog's security infrastructure
- **Error Isolation**: Telemetry failures don't affect application security

#### 3. Data Retention
- **PostHog Policies**: Data retention governed by PostHog policies
- **No Local Retention**: No telemetry data stored locally beyond user ID
- **Anonymized Data**: All transmitted data is anonymized

## Configuration

### Environment Variables

#### ANONYMIZED_TELEMETRY
Controls telemetry collection:
```bash
# Enable telemetry (default)
ANONYMIZED_TELEMETRY=true

# Disable telemetry completely
ANONYMIZED_TELEMETRY=false
```

#### LOGGING_LEVEL
Controls debug logging for telemetry:
```bash
# Enable debug logging
LOGGING_LEVEL=debug

# Standard logging (default)
LOGGING_LEVEL=info
```

#### POSTHOG_PROJECT_API_KEY
Custom PostHog project key (optional):
```bash
# Use custom PostHog project
POSTHOG_PROJECT_API_KEY=your_custom_key
```

### Configuration Examples

#### Development Configuration
```bash
# .env file for development
ANONYMIZED_TELEMETRY=true
LOGGING_LEVEL=debug
```

#### Production Configuration
```bash
# .env file for production
ANONYMIZED_TELEMETRY=true
LOGGING_LEVEL=info
```

#### Privacy-First Configuration
```bash
# .env file for maximum privacy
ANONYMIZED_TELEMETRY=false
```

## Usage Examples

### Basic Telemetry Integration

```python
from browser_use.telemetry.service import ProductTelemetry
from browser_use.telemetry.views import AgentRunTelemetryEvent

# Get telemetry instance (singleton)
telemetry = ProductTelemetry()

# Create and capture an event
event = AgentRunTelemetryEvent(
    agent_id="agent_123",
    use_vision=True,
    task="Navigate to website",
    model_name="gpt-4",
    chat_model_library="openai",
    version="1.0.0",
    source="api"
)

# Capture the event
telemetry.capture(event)
```

### Agent Integration Example

```python
class Agent:
    def __init__(self):
        self.telemetry = ProductTelemetry()
        self.agent_id = str(uuid.uuid4())

    async def run(self, task: str):
        # Log agent run start
        self.telemetry.capture(
            AgentRunTelemetryEvent(
                agent_id=self.agent_id,
                use_vision=self.settings.use_vision,
                task=task,
                model_name=self.model_name,
                chat_model_library=self.chat_model_library,
                version=self.version,
                source=self.source
            )
        )

        # Execute agent steps
        for step in range(max_steps):
            try:
                result = await self.execute_step()

                # Log step completion
                self.telemetry.capture(
                    AgentStepTelemetryEvent(
                        agent_id=self.agent_id,
                        step=step,
                        step_error=[],
                        consecutive_failures=0,
                        actions=[action.model_dump() for action in result.actions]
                    )
                )
            except Exception as e:
                # Log step error
                self.telemetry.capture(
                    AgentStepTelemetryEvent(
                        agent_id=self.agent_id,
                        step=step,
                        step_error=[str(e)],
                        consecutive_failures=self.consecutive_failures,
                        actions=[]
                    )
                )

        # Log agent completion
        self.telemetry.capture(
            AgentEndTelemetryEvent(
                agent_id=self.agent_id,
                steps=step + 1,
                max_steps_reached=step >= max_steps,
                is_done=self.is_done,
                success=self.success,
                total_input_tokens=self.total_tokens,
                total_duration_seconds=time.time() - start_time,
                errors=self.errors
            )
        )
```

### Controller Integration Example

```python
class Registry:
    def __init__(self):
        self.telemetry = ProductTelemetry()

    def create_action_model(self, include_actions=None):
        # Create action model
        fields = self._build_fields(include_actions)

        # Capture registered functions telemetry
        self.telemetry.capture(
            ControllerRegisteredFunctionsTelemetryEvent(
                registered_functions=[
                    RegisteredFunction(
                        name=name,
                        params=action.param_model.model_json_schema()
                    )
                    for name, action in self.registry.actions.items()
                    if include_actions is None or name in include_actions
                ]
            )
        )

        return create_model('ActionModel', **fields)
```

### Custom Event Creation

```python
from browser_use.telemetry.views import BaseTelemetryEvent
from dataclasses import dataclass

@dataclass
class CustomPerformanceEvent(BaseTelemetryEvent):
    operation_name: str
    duration_ms: float
    memory_usage_mb: float
    success: bool
    name: str = 'custom_performance'

# Usage
def monitor_operation(operation_name: str):
    start_time = time.time()
    start_memory = get_memory_usage()

    try:
        # Perform operation
        result = perform_operation()
        success = True
    except Exception:
        success = False
    finally:
        # Capture performance metrics
        telemetry = ProductTelemetry()
        telemetry.capture(
            CustomPerformanceEvent(
                operation_name=operation_name,
                duration_ms=(time.time() - start_time) * 1000,
                memory_usage_mb=get_memory_usage() - start_memory,
                success=success
            )
        )
```

### Conditional Telemetry

```python
class TelemetryManager:
    def __init__(self):
        self.telemetry = ProductTelemetry()
        self.is_enabled = self.telemetry._posthog_client is not None

    def capture_if_enabled(self, event: BaseTelemetryEvent):
        """Only capture if telemetry is enabled"""
        if self.is_enabled:
            self.telemetry.capture(event)

    def capture_with_fallback(self, event: BaseTelemetryEvent, fallback_action=None):
        """Capture with fallback action if telemetry fails"""
        try:
            self.telemetry.capture(event)
        except Exception as e:
            if fallback_action:
                fallback_action(event, e)
            # Continue execution regardless of telemetry failure
```

### Debug Mode Usage

```python
import logging
import os

# Enable debug mode
os.environ['LOGGING_LEVEL'] = 'debug'

# Configure logging
logging.basicConfig(level=logging.DEBUG)

# Create telemetry instance with debug logging
telemetry = ProductTelemetry()

# Events will now be logged to console
event = AgentRunTelemetryEvent(
    agent_id="debug_agent",
    use_vision=False,
    task="Debug task",
    model_name="gpt-3.5-turbo",
    chat_model_library="openai",
    version="1.0.0",
    source="debug"
)

telemetry.capture(event)
# Output: DEBUG:browser_use.telemetry.service:Telemetry event: agent_run {...}
```

## API Reference

### ProductTelemetry Class

#### Constructor
```python
ProductTelemetry() -> ProductTelemetry
```
Singleton class - returns the same instance on each call.

**Configuration:**
- Reads `ANONYMIZED_TELEMETRY` environment variable
- Reads `LOGGING_LEVEL` environment variable
- Reads `POSTHOG_PROJECT_API_KEY` environment variable

#### Methods

##### `capture(event: BaseTelemetryEvent) -> None`
Capture a telemetry event and send to PostHog.

**Parameters:**
- `event`: BaseTelemetryEvent instance to capture

**Behavior:**
- Returns immediately if telemetry is disabled
- Logs event in debug mode
- Sends event to PostHog asynchronously
- Handles errors gracefully without affecting application

**Usage:**
```python
telemetry = ProductTelemetry()
telemetry.capture(AgentRunTelemetryEvent(...))
```

##### `user_id: str` (Property)
Get the anonymous user ID for this installation.

**Returns:** Anonymous UUID4 string

**Behavior:**
- Generates new UUID4 if no user ID exists
- Stores user ID in `~/.cache/browser_use/telemetry_user_id`
- Returns cached user ID for subsequent calls
- Falls back to 'UNKNOWN_USER_ID' if file operations fail

#### Private Methods

##### `_direct_capture(event: BaseTelemetryEvent) -> None`
Internal method for sending events to PostHog.

**Note:** This method is non-blocking and handles errors internally.

### Event Models

#### BaseTelemetryEvent (Abstract)

```python
@dataclass
class BaseTelemetryEvent(ABC):
    @property
    @abstractmethod
    def name(self) -> str:
        pass

    @property
    def properties(self) -> Dict[str, Any]:
        return {k: v for k, v in asdict(self).items() if k != 'name'}
```

**Key Features:**
- Abstract base class for all events
- Automatic property extraction from dataclass fields
- Name property excluded from event properties

#### AgentRunTelemetryEvent

```python
@dataclass
class AgentRunTelemetryEvent(BaseTelemetryEvent):
    agent_id: str
    use_vision: bool
    task: str
    model_name: str
    chat_model_library: str
    version: str
    source: str
    name: str = 'agent_run'
```

#### AgentStepTelemetryEvent

```python
@dataclass
class AgentStepTelemetryEvent(BaseTelemetryEvent):
    agent_id: str
    step: int
    step_error: list[str]
    consecutive_failures: int
    actions: list[dict]
    name: str = 'agent_step'
```

#### AgentEndTelemetryEvent

```python
@dataclass
class AgentEndTelemetryEvent(BaseTelemetryEvent):
    agent_id: str
    steps: int
    max_steps_reached: bool
    is_done: bool
    success: bool | None
    total_input_tokens: int
    total_duration_seconds: float
    errors: Sequence[str | None]
    name: str = 'agent_end'
```

#### ControllerRegisteredFunctionsTelemetryEvent

```python
@dataclass
class ControllerRegisteredFunctionsTelemetryEvent(BaseTelemetryEvent):
    registered_functions: list[RegisteredFunction]
    name: str = 'controller_registered_functions'

@dataclass
class RegisteredFunction:
    name: str
    params: dict[str, Any]
```

### Constants

#### Configuration Constants
```python
USER_ID_PATH = str(Path.home() / '.cache' / 'browser_use' / 'telemetry_user_id')
PROJECT_API_KEY = os.getenv('POSTHOG_PROJECT_API_KEY', 'phc_cp5aBTtPHCpepwlQjUAAn5zk5w71HXgVqAS0m1rbOdc')
HOST = 'https://us.i.posthog.com'
UNKNOWN_USER_ID = 'xcloud'
```

#### PostHog Settings
```python
POSTHOG_EVENT_SETTINGS = {
    'process_person_profile': True,
}
```

## Best Practices

### Event Design

1. **Use Typed Events**: Always extend BaseTelemetryEvent for type safety
2. **Meaningful Names**: Use descriptive event names that indicate purpose
3. **Consistent Naming**: Follow naming conventions (snake_case for properties)
4. **Essential Data Only**: Include only necessary data for analysis
5. **Avoid Sensitive Data**: Never include personal or sensitive information

### Performance Considerations

1. **Non-Blocking**: Telemetry should never block application execution
2. **Error Isolation**: Telemetry failures should not affect application functionality
3. **Minimal Overhead**: Keep event creation and capture lightweight
4. **Batch When Possible**: Consider batching events for high-frequency scenarios

### Privacy Best Practices

1. **Anonymous IDs**: Always use anonymous identifiers
2. **Data Minimization**: Collect only essential metrics
3. **Opt-out Respect**: Always respect user opt-out preferences
4. **No Personal Data**: Never collect names, emails, or personal information
5. **Secure Transmission**: Ensure all data is transmitted securely

### Integration Patterns

1. **Singleton Usage**: Use the singleton pattern consistently
2. **Error Handling**: Wrap telemetry calls in try-catch blocks
3. **Conditional Capture**: Check if telemetry is enabled before expensive operations
4. **Debug Support**: Use debug logging for development and troubleshooting

### Development Guidelines

1. **Test with Disabled Telemetry**: Ensure application works with telemetry disabled
2. **Debug Mode**: Use debug mode for development and testing
3. **Event Validation**: Validate event data before capture
4. **Documentation**: Document custom events and their purposes

## Troubleshooting

### Common Issues

#### Telemetry Not Working
**Problem**: Events not being captured or sent to PostHog
**Causes:**
- Telemetry disabled via environment variable
- Network connectivity issues
- Invalid PostHog configuration
- PostHog client initialization failure

**Solutions:**
```python
# Check if telemetry is enabled
telemetry = ProductTelemetry()
if telemetry._posthog_client is None:
    print("Telemetry is disabled")
else:
    print("Telemetry is enabled")

# Enable debug logging to see telemetry events
import os
import logging
os.environ['LOGGING_LEVEL'] = 'debug'
logging.basicConfig(level=logging.DEBUG)

# Test event capture
telemetry.capture(AgentRunTelemetryEvent(...))
```

#### User ID Generation Issues
**Problem**: User ID not being generated or persisted
**Causes:**
- File system permissions
- Cache directory not accessible
- Disk space issues

**Solutions:**
```python
# Check user ID generation
telemetry = ProductTelemetry()
try:
    user_id = telemetry.user_id
    print(f"User ID: {user_id}")
except Exception as e:
    print(f"User ID generation failed: {e}")

# Manually check user ID file
import os
from pathlib import Path
user_id_path = Path.home() / '.cache' / 'browser_use' / 'telemetry_user_id'
if user_id_path.exists():
    print(f"User ID file exists: {user_id_path}")
    print(f"Content: {user_id_path.read_text()}")
else:
    print(f"User ID file does not exist: {user_id_path}")
```

#### PostHog Connection Issues
**Problem**: Events not reaching PostHog dashboard
**Causes:**
- Network firewall blocking requests
- Invalid API key
- PostHog service issues
- Proxy configuration problems

**Solutions:**
```python
# Test PostHog connectivity
import requests
try:
    response = requests.get('https://us.i.posthog.com/api/projects/')
    print(f"PostHog connectivity: {response.status_code}")
except Exception as e:
    print(f"PostHog connection failed: {e}")

# Verify API key
telemetry = ProductTelemetry()
if telemetry.PROJECT_API_KEY:
    print(f"API Key configured: {telemetry.PROJECT_API_KEY[:10]}...")
else:
    print("No API key configured")
```

#### Event Property Issues
**Problem**: Event properties not appearing correctly in PostHog
**Causes:**
- Incorrect dataclass structure
- Property name conflicts
- Data type serialization issues

**Solutions:**
```python
# Debug event properties
event = AgentRunTelemetryEvent(
    agent_id="test",
    use_vision=True,
    task="test task",
    model_name="gpt-4",
    chat_model_library="openai",
    version="1.0.0",
    source="debug"
)

print(f"Event name: {event.name}")
print(f"Event properties: {event.properties}")

# Verify property extraction
from dataclasses import asdict
all_fields = asdict(event)
print(f"All fields: {all_fields}")
```

### Debugging Tips

1. **Enable Debug Logging**: Set `LOGGING_LEVEL=debug` to see all telemetry events
2. **Check Environment Variables**: Verify telemetry configuration
3. **Test Network Connectivity**: Ensure PostHog is accessible
4. **Validate Event Structure**: Check event properties and data types
5. **Monitor PostHog Dashboard**: Verify events appear in PostHog interface

### Error Codes and Messages

| Error Type | Description | Solution |
|------------|-------------|----------|
| `TelemetryDisabled` | Telemetry is disabled via environment variable | Set `ANONYMIZED_TELEMETRY=true` |
| `PostHogConnectionError` | Cannot connect to PostHog service | Check network connectivity and API key |
| `UserIDGenerationError` | Cannot generate or persist user ID | Check file system permissions |
| `EventSerializationError` | Cannot serialize event properties | Verify event dataclass structure |
| `InvalidAPIKey` | PostHog API key is invalid | Check `POSTHOG_PROJECT_API_KEY` configuration |

### Performance Troubleshooting

#### High Memory Usage
**Problem**: Telemetry causing memory issues
**Solutions:**
```python
# Monitor telemetry memory usage
import psutil
import os

process = psutil.Process(os.getpid())
memory_before = process.memory_info().rss

# Capture events
for i in range(1000):
    telemetry.capture(AgentStepTelemetryEvent(...))

memory_after = process.memory_info().rss
print(f"Memory increase: {(memory_after - memory_before) / 1024 / 1024:.2f} MB")
```

#### Slow Event Capture
**Problem**: Telemetry capture taking too long
**Solutions:**
```python
# Time event capture
import time

start_time = time.time()
telemetry.capture(event)
capture_time = time.time() - start_time

if capture_time > 0.1:  # 100ms threshold
    print(f"Slow telemetry capture: {capture_time:.3f}s")
```

### Configuration Troubleshooting

#### Environment Variable Issues
```python
# Debug environment configuration
import os

print("Telemetry Configuration:")
print(f"ANONYMIZED_TELEMETRY: {os.getenv('ANONYMIZED_TELEMETRY', 'not set')}")
print(f"LOGGING_LEVEL: {os.getenv('LOGGING_LEVEL', 'not set')}")
print(f"POSTHOG_PROJECT_API_KEY: {os.getenv('POSTHOG_PROJECT_API_KEY', 'not set')}")

# Check effective configuration
telemetry = ProductTelemetry()
print(f"Telemetry enabled: {telemetry._posthog_client is not None}")
print(f"Debug logging: {telemetry.debug_logging}")
```

#### File System Issues
```python
# Debug file system access
from pathlib import Path
import os

cache_dir = Path.home() / '.cache' / 'browser_use'
user_id_path = cache_dir / 'telemetry_user_id'

print(f"Cache directory: {cache_dir}")
print(f"Cache directory exists: {cache_dir.exists()}")
print(f"Cache directory writable: {os.access(cache_dir.parent, os.W_OK)}")
print(f"User ID path: {user_id_path}")
print(f"User ID file exists: {user_id_path.exists()}")

if user_id_path.exists():
    print(f"User ID file readable: {os.access(user_id_path, os.R_OK)}")
```

### Best Practices for Troubleshooting

1. **Start with Environment**: Always check environment variable configuration first
2. **Enable Debug Mode**: Use debug logging to understand telemetry behavior
3. **Test Incrementally**: Test individual components before full integration
4. **Monitor PostHog**: Check PostHog dashboard for event delivery
5. **Check Logs**: Review application logs for telemetry-related errors
6. **Isolate Issues**: Test telemetry independently from main application logic

### Support and Resources

#### Getting Help
1. **Check Documentation**: Review this documentation for common solutions
2. **Enable Debug Logging**: Use debug mode to gather diagnostic information
3. **Test Configuration**: Verify environment variables and PostHog connectivity
4. **Review Code**: Check event creation and capture implementation

#### Useful Commands
```bash
# Check telemetry configuration
env | grep -E "(ANONYMIZED_TELEMETRY|LOGGING_LEVEL|POSTHOG)"

# Test PostHog connectivity
curl -I https://us.i.posthog.com/api/projects/

# Check user ID file
cat ~/.cache/browser_use/telemetry_user_id

# Enable debug logging
export LOGGING_LEVEL=debug
```

---

**Documentation Version**: 1.0
**Last Updated**: July 2025
**Maintained by**: VizCheck Development Team
