#!/usr/bin/env python3
"""
Test script for build scripts
This script tests the functionality of all build scripts without actually running builds
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


class EmojiHelper:
    """Helper class to handle emoji display with ASCII fallbacks"""

    def __init__(self):
        self.use_emoji = self._can_use_emoji()

    def _can_use_emoji(self) -> bool:
        """Determine if we can safely use emoji characters"""
        try:
            # Test if we can encode a simple emoji
            "✓".encode(sys.stdout.encoding or 'utf-8')
            return True
        except (UnicodeEncodeError, LookupError):
            return False

    def get(self, emoji_name: str) -> str:
        """Get emoji or ASCII fallback"""
        emoji_map = {
            'check': '✓' if self.use_emoji else '[OK]',
            'cross': '✗' if self.use_emoji else '[FAIL]',
            'warning': '⚠' if self.use_emoji else '[WARNING]',
            'info': 'ℹ' if self.use_emoji else '[INFO]',
            'success': '🎉' if self.use_emoji else '[SUCCESS]',
            'error': '❌' if self.use_emoji else '[ERROR]'
        }
        return emoji_map.get(emoji_name, f'[{emoji_name.upper()}]')


# Global emoji helper
emoji = EmojiHelper()


def log(message, level="INFO"):
    """Simple logging function with emoji support"""
    # level_emojis = {
    #     "INFO": emoji.get('info'),
    #     "ERROR": emoji.get('error'),
    #     "WARNING": emoji.get('warning'),
    #     "SUCCESS": emoji.get('success')
    # }
    # emoji_prefix = level_emojis.get(level, emoji.get('info'))
    if level == "":
        print(message)
        return
    emoji_prefix = level.upper()
    print(f"{emoji_prefix} {message}")


def test_python_script():
    """Test the Python build script"""
    log("Testing Python build script...")
    
    try:
        # Test help
        result = subprocess.run([sys.executable, "build_vizcheck.py", "--help"], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            log(f"{emoji.get('check')} Python script help works")
        else:
            log(f"{emoji.get('cross')} Python script help failed", "ERROR")
            return False

        # Test list specs
        result = subprocess.run([sys.executable, "build_vizcheck.py", "--list-specs"],
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            log(f"{emoji.get('check')} Python script list-specs works")
        else:
            log(f"{emoji.get('cross')} Python script list-specs failed", "ERROR")
            log(f"Output: {result.stdout}", "ERROR")
            log(f"Error: {result.stderr}", "ERROR")
            return False
            
        return True
    except Exception as e:
        log(f"{emoji.get('cross')} Python script test failed: {e}", "ERROR")
        return False


def test_batch_script():
    """Test the Windows batch script"""
    if platform.system() != "Windows":
        log("Skipping batch script test (not on Windows)")
        return True

    log("Testing Windows batch script...")

    try:
        # Test help - use --no-pause flag to avoid waiting for user input
        result = subprocess.run(["cmd", "/c", "build_vizcheck.bat", "help", "--no-pause"],
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            log("✓ Batch script help works")
        else:
            log("✗ Batch script help failed", "ERROR")
            log(f"Return code: {result.returncode}", "ERROR")
            # Only show first few lines of output to avoid spam
            output_lines = result.stdout.split('\n')[:10]
            log(f"Output (first 10 lines): {chr(10).join(output_lines)}", "ERROR")
            if result.stderr:
                log(f"Error: {result.stderr}", "ERROR")
            return False

        # Test list - use --no-pause flag to avoid waiting for user input
        result = subprocess.run(["cmd", "/c", "build_vizcheck.bat", "list", "--no-pause"],
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            log("✓ Batch script list works")
        else:
            log("✗ Batch script list failed", "ERROR")
            log(f"Return code: {result.returncode}", "ERROR")
            # Only show first few lines of output to avoid spam
            output_lines = result.stdout.split('\n')[:10]
            log(f"Output (first 10 lines): {chr(10).join(output_lines)}", "ERROR")
            if result.stderr:
                log(f"Error: {result.stderr}", "ERROR")
            return False

        return True
    except Exception as e:
        log(f"✗ Batch script test failed: {e}", "ERROR")
        return False


def test_powershell_script():
    """Test the PowerShell script"""
    if platform.system() != "Windows":
        log("Skipping PowerShell script test (not on Windows)")
        return True
        
    log("Testing PowerShell script...")
    
    try:
        # Test list specs
        result = subprocess.run(["powershell", "-ExecutionPolicy", "Bypass", 
                               "-File", "build_vizcheck.ps1", "-ListSpecs"], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            log("✓ PowerShell script list-specs works")
        else:
            log("✗ PowerShell script list-specs failed", "ERROR")
            return False
            
        return True
    except Exception as e:
        log(f"✗ PowerShell script test failed: {e}", "ERROR")
        return False


def test_shell_script():
    """Test the Unix/Linux/macOS shell script"""
    if platform.system() == "Windows":
        log("Skipping shell script test (on Windows)")
        return True
        
    log("Testing shell script...")
    
    try:
        # Make script executable
        os.chmod("build_vizcheck.sh", 0o755)
        
        # Test help
        result = subprocess.run(["./build_vizcheck.sh", "--help"], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            log("✓ Shell script help works")
        else:
            log("✗ Shell script help failed", "ERROR")
            return False
            
        # Test list
        result = subprocess.run(["./build_vizcheck.sh", "list"], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            log("✓ Shell script list works")
        else:
            log("✗ Shell script list failed", "ERROR")
            return False
            
        return True
    except Exception as e:
        log(f"✗ Shell script test failed: {e}", "ERROR")
        return False


def check_virtual_environment():
    """Check if virtual environment exists"""
    log("Checking virtual environment...")
    
    parent_dir = Path.cwd().parent
    venv_path = parent_dir / "venv"
    
    if platform.system() == "Windows":
        activate_script = venv_path / "Scripts" / "activate.bat"
    else:
        activate_script = venv_path / "bin" / "activate"
    
    if activate_script.exists():
        log(f"{emoji.get('check')} Virtual environment found")
        return True
    else:
        log(f"{emoji.get('cross')} Virtual environment not found", "WARNING")
        log(f"Expected at: {activate_script}", "WARNING")
        return False


def main():
    """Main test function"""
    log("=" * 60, "")
    log("BUILD SCRIPTS TEST", "\t")
    log("=" * 60, "")
    
    # Change to build_scripts directory if not already there
    if not Path("build_vizcheck.py").exists():
        build_scripts_dir = Path(__file__).parent
        os.chdir(build_scripts_dir)
        log(f"Changed to directory: {build_scripts_dir}")
    
    # Check virtual environment
    venv_ok = check_virtual_environment()
    
    # Test all scripts
    tests = [
        ("Python Script", test_python_script),
        ("Batch Script", test_batch_script),
        ("PowerShell Script", test_powershell_script),
        ("Shell Script", test_shell_script),
    ]
    
    results = []
    for test_name, test_func in tests:
        log(f"\n--- Testing {test_name} ---", "")
        success = test_func()
        results.append((test_name, success))
    
    # Summary
    log("\n" + "=" * 60, "")
    log("TEST SUMMARY", "\t")
    log("=" * 60, "")
    
    if not venv_ok:
        log(f"{emoji.get('warning')} Virtual environment not found - scripts may fail during actual builds")

    all_passed = True
    for test_name, success in results:
        status = f"{emoji.get('check')} PASS" if success else f"{emoji.get('cross')} FAIL"
        log(f"{test_name}: {status}")
        if not success:
            all_passed = False

    log("=" * 60, "")
    if all_passed:
        log(f"{emoji.get('success')} All tests passed!", "SUCCESS")
        return 0
    else:
        log(f"{emoji.get('error')} Some tests failed!", "ERROR")
        return 1


if __name__ == "__main__":
    sys.exit(main())
