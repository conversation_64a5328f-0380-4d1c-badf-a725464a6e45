# VizCheck Spec Files

This directory contains PyInstaller specification files for different build configurations of VizCheck.

## 📋 Available Spec Files

### **app.spec** (Default)
- **Purpose**: Standard VizCheck build
- **Executable**: `VizCheck.exe`
- **Console**: Enabled
- **Optimization**: Standard
- **Use Case**: General purpose build

### **console.spec**
- **Purpose**: Console-optimized version
- **Executable**: `VizCheck_Console.exe`
- **Console**: Enabled (optimized for CLI)
- **Optimization**: Standard
- **Use Case**: Command-line usage, server environments

### **debug.spec**
- **Purpose**: Debug build with symbols
- **Executable**: `VizCheck_Debug.exe`
- **Console**: Enabled
- **Optimization**: None (debug=True, strip=False, upx=False)
- **Use Case**: Development, debugging, troubleshooting

### **app_no_console.spec**
- **Purpose**: Windowed application (no console)
- **Executable**: `XboxCloudGaming.exe`
- **Console**: Disabled (console=False)
- **Optimization**: Standard
- **Use Case**: GUI-only deployment, cleaner user experience

### **XboxCloudGaming.spec**
- **Purpose**: Xbox Cloud Gaming specific build
- **Executable**: `XboxCloudGaming.exe`
- **Console**: Enabled
- **Optimization**: Debug enabled
- **Use Case**: Xbox Cloud Gaming automation

### **test.spec**
- **Purpose**: Minimal test build
- **Executable**: `test.exe`
- **Console**: Enabled
- **Optimization**: Minimal dependencies
- **Use Case**: Testing, development, CI/CD

## 🔧 Common Features

All spec files include:
- **Safe dependency collection** with try/catch blocks
- **Conditional file inclusion** (only if files exist)
- **Gradio module collection** as source files
- **Proper path resolution** from specs/ directory
- **Error-resistant configuration**

## 🚀 Usage Examples

```bash
# List all available spec files
python build_vizcheck.py --list-specs

# Use default spec (app.spec)
python build_vizcheck.py

# Use specific spec files
python build_vizcheck.py --spec console
python build_vizcheck.py --spec debug
python build_vizcheck.py --spec app_no_console

# Combine with version increment
python build_vizcheck.py --spec debug --increment patch
```

## 📁 File Structure

```
specs/
├── app.spec              # Default build
├── console.spec          # Console optimized
├── debug.spec            # Debug build
├── app_no_console.spec   # No console window
├── XboxCloudGaming.spec  # Xbox specific
├── test.spec             # Minimal test build
└── README.md             # This file
```

## 🛠️ Customization

To create a new spec file:

1. Copy an existing spec file
2. Modify the `name=` parameter for different executable name
3. Adjust console, debug, and optimization settings
4. Update comments to describe the purpose
5. Test with: `python build_vizcheck.py --spec your_new_spec`

## 📝 Notes

- All spec files use relative paths from the specs/ directory
- Dependencies are collected safely with error handling
- Module collection mode is set for gradio compatibility
- Paths are automatically resolved by the build script
