# VizCheck Troubleshooting Guide

Comprehensive troubleshooting documentation with common issues, solutions, and diagnostic procedures for VizCheck.

## Table of Contents

1. [Quick Diagnostics](#quick-diagnostics)
2. [Installation Issues](#installation-issues)
3. [Configuration Problems](#configuration-problems)
4. [Browser Connection Issues](#browser-connection-issues)
5. [API and Authentication Errors](#api-and-authentication-errors)
6. [Performance Issues](#performance-issues)
7. [Gaming-Specific Problems](#gaming-specific-problems)
8. [Build and Deployment Issues](#build-and-deployment-issues)
9. [Diagnostic Tools](#diagnostic-tools)
10. [Getting Help](#getting-help)

## Quick Diagnostics

### System Health Check

Run these commands to quickly diagnose common issues:

```bash
# Check Python version
python --version

# Check installed packages
pip list | grep -E "(playwright|gradio|langchain|azure)"

# Check Chrome installation
google-chrome --version  # Linux/macOS
"C:\Program Files\Google\Chrome\Application\chrome.exe" --version  # Windows

# Test configuration
python -c "from config import Config; c=Config(); print(f'App: {c.get_app_name()}, Version: {c.get_version()}')"
```

### Environment Variables Check

```bash
# Check critical environment variables
echo $AZURE_API_KEY  # Should not be empty
echo $AZURE_ENDPOINT
echo $LOGGING_LEVEL
```

## Installation Issues

### Python Version Compatibility

**Issue**: `ModuleNotFoundError` or compatibility errors
```
Error: Python 3.11+ required
```

**Solutions**:
1. **Check Python version**:
   ```bash
   python --version
   # Should show 3.11.0 or higher
   ```

2. **Install correct Python version**:
   - Download from [python.org](https://python.org)
   - Use pyenv for version management:
     ```bash
     pyenv install 3.11.0
     pyenv global 3.11.0
     ```

3. **Update virtual environment**:
   ```bash
   python -m venv venv --upgrade-deps
   ```

### Dependency Installation Failures

**Issue**: Package installation errors
```
ERROR: Failed building wheel for package
```

**Solutions**:
1. **Update pip and setuptools**:
   ```bash
   python -m pip install --upgrade pip setuptools wheel
   ```

2. **Install with verbose output**:
   ```bash
   pip install -r requirements.txt -v
   ```

3. **Use alternative package sources**:
   ```bash
   pip install -r requirements.txt -i https://pypi.org/simple/
   ```

4. **Install system dependencies** (Linux):
   ```bash
   sudo apt-get update
   sudo apt-get install build-essential python3-dev
   ```

### Playwright Installation Issues

**Issue**: Browser installation failures
```
Error: Playwright browsers not found
```

**Solutions**:
1. **Install Playwright browsers**:
   ```bash
   playwright install
   playwright install chromium
   ```

2. **Install with dependencies**:
   ```bash
   playwright install --with-deps
   ```

3. **Manual browser installation**:
   ```bash
   playwright install chromium --force
   ```

## Configuration Problems

### Configuration File Not Found

**Issue**: Config file missing or inaccessible
```
Warning: Config file 'config/config.ini' not found. Using default values.
```

**Solutions**:
1. **Create config directory and file**:
   ```bash
   mkdir -p config
   cp config/config.ini.example config/config.ini  # If example exists
   ```

2. **Create minimal config file**:
   ```ini
   [App]
   app_name = VizCheck
   version = 2.0.0
   debug = false

   [settings]
   max_num_of_steps = 45
   start_url = https://example.com
   output_dir = ./outputs
   ```

3. **Check file permissions**:
   ```bash
   chmod 644 config/config.ini
   ```

### Invalid Configuration Values

**Issue**: Configuration parsing errors
```
Error parsing INI file: invalid syntax
```

**Solutions**:
1. **Validate INI syntax**:
   ```python
   import configparser
   parser = configparser.ConfigParser()
   try:
       parser.read('config/config.ini')
       print("Configuration valid")
   except Exception as e:
       print(f"Configuration error: {e}")
   ```

2. **Check for common issues**:
   - No spaces around `=` in key-value pairs
   - Proper section headers `[section]`
   - No special characters in values without quotes

3. **Reset to defaults**:
   ```bash
   mv config/config.ini config/config.ini.backup
   # Create new config file with known good values
   ```

## Browser Connection Issues

### Chrome Connection Failed

**Issue**: Cannot connect to Chrome browser
```
Error: Could not connect to Chrome on port 9222
```

**Solutions**:
1. **Check if Chrome is running**:
   ```bash
   # Windows
   tasklist | findstr chrome.exe
   
   # Linux/macOS
   ps aux | grep chrome
   ```

2. **Launch Chrome with debugging**:
   ```bash
   # Windows
   "C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir="C:/temp/chrome-profile"
   
   # Linux/macOS
   google-chrome --remote-debugging-port=9222 --user-data-dir="/tmp/chrome-profile"
   ```

3. **Check port availability**:
   ```bash
   # Windows
   netstat -an | findstr 9222
   
   # Linux/macOS
   lsof -i :9222
   ```

4. **Kill existing Chrome processes**:
   ```bash
   # Windows
   taskkill /F /IM chrome.exe
   
   # Linux/macOS
   pkill -f chrome
   ```

### Browser Launch Failures

**Issue**: Browser fails to start
```
Error: Browser executable not found
```

**Solutions**:
1. **Verify Chrome installation**:
   ```bash
   which google-chrome  # Linux/macOS
   where chrome.exe     # Windows
   ```

2. **Install Chrome**:
   - Download from [google.com/chrome](https://google.com/chrome)
   - Or use package manager:
     ```bash
     # Ubuntu/Debian
     sudo apt-get install google-chrome-stable
     
     # macOS with Homebrew
     brew install --cask google-chrome
     ```

3. **Use alternative browser**:
   ```python
   # In browser configuration
   browser_config = BrowserConfig(
       browser_type='firefox'  # or 'webkit'
   )
   ```

### Headless Mode Issues

**Issue**: Headless browser problems
```
Error: Display issues in headless mode
```

**Solutions**:
1. **Disable headless mode**:
   ```python
   browser_config = BrowserConfig(headless=False)
   ```

2. **Install virtual display** (Linux):
   ```bash
   sudo apt-get install xvfb
   export DISPLAY=:99
   Xvfb :99 -screen 0 1280x720x24 &
   ```

3. **Use Docker with display**:
   ```bash
   docker run --rm -e DISPLAY=$DISPLAY -v /tmp/.X11-unix:/tmp/.X11-unix vizcheck
   ```

## API and Authentication Errors

### Azure OpenAI Authentication

**Issue**: API authentication failures
```
Error: Invalid API key or endpoint
```

**Solutions**:
1. **Verify API key**:
   ```bash
   echo $AZURE_API_KEY
   # Should show your API key (keep secure)
   ```

2. **Test API connectivity**:
   ```bash
   curl -H "Authorization: Bearer $AZURE_API_KEY" \
        -H "Content-Type: application/json" \
        "$AZURE_ENDPOINT/openai/deployments?api-version=2024-02-15-preview"
   ```

3. **Check endpoint format**:
   ```
   Correct: https://your-resource.openai.azure.com/
   Incorrect: https://your-resource.openai.azure.com/openai/
   ```

4. **Verify deployment name**:
   ```python
   # Check available deployments
   import openai
   openai.api_key = "your-api-key"
   openai.api_base = "your-endpoint"
   print(openai.Deployment.list())
   ```

### API Rate Limiting

**Issue**: Rate limit exceeded
```
Error: Rate limit exceeded. Please retry after X seconds
```

**Solutions**:
1. **Implement retry logic**:
   ```python
   import time
   import random
   
   def retry_with_backoff(func, max_retries=3):
       for attempt in range(max_retries):
           try:
               return func()
           except Exception as e:
               if "rate limit" in str(e).lower():
                   wait_time = (2 ** attempt) + random.uniform(0, 1)
                   time.sleep(wait_time)
               else:
                   raise
   ```

2. **Reduce request frequency**:
   ```python
   # Add delays between requests
   import time
   time.sleep(1)  # Wait 1 second between API calls
   ```

3. **Monitor usage**:
   - Check Azure portal for usage metrics
   - Implement usage tracking in application

## Performance Issues

### Slow Execution

**Issue**: Tasks taking too long to complete
```
Task execution exceeding expected time
```

**Solutions**:
1. **Optimize browser settings**:
   ```python
   browser_config = BrowserConfig(
       headless=True,  # Faster execution
       disable_security=True,
       wait_for_network_idle_page_load_time=0.5,  # Reduce wait time
       maximum_wait_page_load_time=3  # Reduce timeout
   )
   ```

2. **Reduce max steps**:
   ```ini
   [settings]
   max_num_of_steps = 20  # Reduce from default 45
   ```

3. **Monitor resource usage**:
   ```bash
   # Check CPU and memory usage
   top
   htop  # If available
   
   # Windows
   taskmgr
   ```

### Memory Issues

**Issue**: High memory consumption
```
MemoryError: Unable to allocate memory
```

**Solutions**:
1. **Increase system memory**
2. **Close unnecessary applications**
3. **Use memory profiling**:
   ```python
   import psutil
   import os
   
   process = psutil.Process(os.getpid())
   print(f"Memory usage: {process.memory_info().rss / 1024 / 1024:.2f} MB")
   ```

4. **Implement memory cleanup**:
   ```python
   import gc
   gc.collect()  # Force garbage collection
   ```

### Network Connectivity

**Issue**: Network-related timeouts
```
Error: Connection timeout
```

**Solutions**:
1. **Check internet connection**:
   ```bash
   ping google.com
   curl -I https://www.xbox.com
   ```

2. **Configure proxy settings**:
   ```python
   browser_config = BrowserConfig(
       proxy={'server': 'http://proxy:8080'}
   )
   ```

3. **Increase timeout values**:
   ```python
   browser_config = BrowserConfig(
       maximum_wait_page_load_time=10  # Increase timeout
   )
   ```

## Gaming-Specific Problems

### Controller Not Responding

**Issue**: Xbox controller inputs not working
```
Controller actions not registering in game
```

**Solutions**:
1. **Test controller connection**:
   ```python
   from browser_use.controller.service import Controller
   controller = Controller()
   controller.handle_action("tap", "a")  # Test A button
   ```

2. **Check game controller support**:
   - Verify game supports controller input
   - Try switching between keyboard and controller modes

3. **Restart controller service**:
   ```python
   controller.reset_controller()
   ```

### Game Launch Failures

**Issue**: Games fail to start
```
Game loading timeout or failure
```

**Solutions**:
1. **Check Xbox Game Pass subscription**
2. **Verify regional availability**:
   ```python
   from config import Config
   config = Config()
   countries = config.get_available_countries()
   print("Available countries:", countries)
   ```

3. **Clear browser cache**:
   ```python
   # Use incognito/private browsing mode
   browser_config = BrowserConfig(
       browser_context_config={'incognito': True}
   )
   ```

## Build and Deployment Issues

### Build Failures

**Issue**: PyInstaller build errors
```
Error: Failed to build executable
```

**Solutions**:
1. **Check build dependencies**:
   ```bash
   pip install pyinstaller
   pyinstaller --version
   ```

2. **Clean build directory**:
   ```bash
   rm -rf build/ dist/
   pyinstaller --clean app.spec
   ```

3. **Use debug build**:
   ```bash
   python build_vizcheck.py --spec debug
   ```

4. **Check spec file syntax**:
   ```python
   # Validate spec file
   with open('app.spec', 'r') as f:
       content = f.read()
       # Check for syntax errors
   ```

### Runtime Errors in Built Application

**Issue**: Executable fails to run
```
Error: Module not found in built application
```

**Solutions**:
1. **Add missing modules to spec file**:
   ```python
   # In app.spec
   hiddenimports=['missing_module']
   ```

2. **Include data files**:
   ```python
   # In app.spec
   datas=[('config/', 'config/'), ('assets/', 'assets/')]
   ```

3. **Test with console version**:
   ```bash
   python build_vizcheck.py --spec console
   ```

## Diagnostic Tools

### Logging Configuration

Enable detailed logging for troubleshooting:

```python
import logging

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('vizcheck_debug.log'),
        logging.StreamHandler()
    ]
)

# Set specific logger levels
logging.getLogger('browser_use').setLevel(logging.DEBUG)
logging.getLogger('playwright').setLevel(logging.INFO)
```

### System Information Collection

```python
import platform
import sys
import psutil

def collect_system_info():
    """Collect system information for troubleshooting"""
    info = {
        'platform': platform.platform(),
        'python_version': sys.version,
        'cpu_count': psutil.cpu_count(),
        'memory_total': psutil.virtual_memory().total,
        'disk_free': psutil.disk_usage('/').free
    }
    return info

print(collect_system_info())
```

### Network Diagnostics

```python
import requests
import time

def test_network_connectivity():
    """Test network connectivity to required services"""
    endpoints = [
        'https://www.google.com',
        'https://www.xbox.com',
        'https://api.openai.com'
    ]
    
    for endpoint in endpoints:
        try:
            start_time = time.time()
            response = requests.get(endpoint, timeout=10)
            latency = time.time() - start_time
            print(f"{endpoint}: {response.status_code} ({latency:.2f}s)")
        except Exception as e:
            print(f"{endpoint}: ERROR - {e}")

test_network_connectivity()
```

## Getting Help

### Information to Provide

When seeking help, include:

1. **System Information**:
   - Operating system and version
   - Python version
   - VizCheck version

2. **Error Details**:
   - Complete error message
   - Stack trace
   - Steps to reproduce

3. **Configuration**:
   - Relevant config.ini settings
   - Environment variables (without sensitive data)
   - Browser configuration

4. **Logs**:
   - Application logs
   - Browser console errors
   - System logs if relevant

### Support Channels

- **Documentation**: Check [docs/](../docs/) folder
- **Issues**: Report bugs in Azure DevOps repository
- **Configuration**: Review [Configuration Guide](Configuration_Guide.md)
- **API Reference**: Check [API Reference](API_Reference.md)

### Self-Help Checklist

Before seeking help:

- [ ] Checked this troubleshooting guide
- [ ] Verified system requirements
- [ ] Tested with minimal configuration
- [ ] Checked logs for error details
- [ ] Tried with different browser settings
- [ ] Verified network connectivity
- [ ] Updated to latest version

---

**Related Documentation**:
- [Configuration Guide](Configuration_Guide.md) - Setup and configuration
- [Deployment Guide](Deployment_Guide.md) - Production deployment
- [Performance Guide](Performance_Guide.md) - Optimization strategies
