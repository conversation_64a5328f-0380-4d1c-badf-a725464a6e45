# Agent Module Documentation

## Overview

The Agent module is the core intelligence component of the VizCheck browser automation system. It orchestrates AI-powered browser interactions by combining language models, browser automation, and intelligent decision-making to execute complex tasks described in natural language.

## Table of Contents

1. [Architecture](#architecture)
2. [Core Components](#core-components)
3. [Agent Service](#agent-service)
4. [Data Models](#data-models)
5. [Prompt System](#prompt-system)
6. [GIF Generation](#gif-generation)
7. [Message Management](#message-management)
8. [Usage Examples](#usage-examples)
9. [Configuration](#configuration)
10. [Best Practices](#best-practices)
11. [Troubleshooting](#troubleshooting)

## Architecture

### Module Structure
```
browser_use/agent/
├── service.py              # Core Agent implementation
├── views.py               # Data models and structures
├── prompts.py             # Prompt management and templates
├── gif.py                 # GIF generation for session recording
├── tests.py               # Comprehensive test suite
├── system_prompt.md       # System prompt template
├── system_prompt copy.md  # Backup system prompt
└── message_manager/       # Message and conversation management
    ├── service.py         # MessageManager implementation
    ├── views.py           # Message data models
    ├── utils.py           # Message processing utilities
    └── tests.py           # Message manager tests
```

### High-Level Architecture

The VizCheck Agent operates within a sophisticated multi-layered architecture that combines AI reasoning, browser automation, and intelligent state management to execute complex web tasks.

```
┌──────────────────────────────────────────────────────────────────────────────────┐
│                              VizCheck Agent System                               │
├──────────────────────────────────────────────────────────────────────────────────┤
│                                User Interface                                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │   Natural       │  │    Task         │  │   Configuration │  │   Results   │  │
│  │   Language      │  │  Definition     │  │   & Settings    │  │ & Reports   │  │
│  │   Input         │  │                 │  │                 │  │             │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────┘  │
├──────────────────────────────────────────────────────────────────────────────────┤
│                              Agent Orchestration Layer                           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                            Agent Service                                    │ │
│  │  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐  ┌──────────────────┐ │ │
│  │  │    Task      │  │    Step      │  │    Action    │  │      State       │ │ │
│  │  │  Planning    │  │  Execution   │  │  Validation  │  │   Management     │ │ │
│  │  └──────────────┘  └──────────────┘  └──────────────┘  └──────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                         Message Management                                  │ │
│  │  ┌──────────────────┐  ┌─────────────┐  ┌─────────────┐  ┌────────────────┐ │ │
│  │  │   Conversation   │  │   Prompt    │  │   Context   │  │     History    │ │ │
│  │  │     Manager      │  │  Templates  │  │  Injection  │  │    Tracking    │ │ │
│  │  └──────────────────┘  └─────────────┘  └─────────────┘  └────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
├──────────────────────────────────────────────────────────────────────────────────┤
│                              AI Reasoning Layer                                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │   Language      │  │    Vision       │  │   Decision      │  │   Response  │  │
│  │   Model         │  │   Processing    │  │   Making        │  │  Processing │  │
│  │ (GPT-4/Claude)  │  │  (Screenshots)  │  │   Engine        │  │             │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────┘  │
├──────────────────────────────────────────────────────────────────────────────────┤
│                            Action Execution Layer                                │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                           Controller System                                 │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │   Action    │  │  Parameter  │  │   Registry  │  │     Gaming          │ │ │
│  │  │ Execution   │  │ Validation  │  │ Management  │  │    Controls         │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
├──────────────────────────────────────────────────────────────────────────────────┤
│                            Browser Automation Layer                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │   Browser       │  │      DOM        │  │   Navigation    │  │   Session   │  │
│  │   Context       │  │   Analysis      │  │   & Interaction │  │ Management  │  │
│  │  (Playwright)   │  │                 │  │                 │  │             │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────┘  │
├──────────────────────────────────────────────────────────────────────────────────┤
│                              Data & Analytics Layer                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │   Telemetry     │  │   Performance   │  │     Error       │  │   Session   │  │
│  │   Collection    │  │   Monitoring    │  │   Tracking      │  │  Recording  │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────┘  │
└──────────────────────────────────────────────────────────────────────────────────┘
```

#### Architectural Principles

##### 1. Layered Architecture
The system follows a strict layered architecture where each layer has specific responsibilities:
- **User Interface Layer**: Handles user input and result presentation
- **Agent Orchestration Layer**: Manages task execution and state
- **AI Reasoning Layer**: Provides intelligent decision-making capabilities
- **Action Execution Layer**: Executes browser and system actions
- **Browser Automation Layer**: Handles low-level browser interactions
- **Data & Analytics Layer**: Monitors performance and collects insights

##### 2. Event-Driven Design
The Agent operates on an event-driven model where:
- Each step generates events that trigger subsequent actions
- State changes are propagated through the system
- Error events trigger recovery mechanisms
- Completion events initiate reporting and cleanup

##### 3. Modular Component Design
Each component is designed for:
- **Independence**: Components can operate independently
- **Replaceability**: Components can be swapped without affecting others
- **Testability**: Each component can be tested in isolation
- **Extensibility**: New components can be added easily

##### 4. AI-First Architecture
The architecture is optimized for AI integration:
- **Context Preservation**: Maintains conversation context across steps
- **Vision Integration**: Seamlessly incorporates visual understanding
- **Prompt Engineering**: Optimized prompt management and templating
- **Model Flexibility**: Supports multiple LLM providers and models

#### Data Flow Architecture

```
User Task Input → Task Analysis → Step Planning → Action Generation → Execution → Validation → Next Step
      ↓               ↓              ↓               ↓               ↓            ↓           ↓
Natural Language → AI Processing → Step Queue → Action Models → Browser API → State Update → Loop
      ↓               ↓              ↓               ↓               ↓            ↓           ↓
Context Building → Prompt Creation → LLM Call → Response Parse → DOM Interaction → History → Report
```

**Detailed Flow:**
1. **Input Processing**: Natural language task converted to structured format
2. **Context Building**: Current browser state and history incorporated
3. **AI Reasoning**: LLM generates next action based on context
4. **Action Validation**: Actions validated against available options
5. **Execution**: Actions executed through Controller and Browser layers
6. **State Update**: Browser state and agent history updated
7. **Loop Control**: Decision made whether to continue or complete

#### Integration Architecture

##### External System Integration
```
VizCheck Agent
├── Language Model APIs
│   ├── OpenAI (GPT-4, GPT-3.5)
│   ├── Anthropic (Claude)
│   ├── Google (Gemini)
│   └── Custom Models
├── Browser Automation
│   ├── Playwright (Primary)
│   ├── Chrome DevTools
│   └── WebDriver (Fallback)
├── Analytics & Monitoring
│   ├── PostHog (Telemetry)
│   ├── Performance Metrics
│   └── Error Tracking
└── File System
    ├── Session Storage
    ├── Configuration Files
    └── Generated Reports
```

##### Internal Component Integration
```
Agent Service ←→ Message Manager ←→ Prompt System
     ↕                ↕                 ↕
Controller ←→ Browser Context ←→ DOM Service
     ↕                ↕                 ↕
Registry ←→ History Processor ←→ Telemetry
```

#### Scalability Architecture

##### Horizontal Scaling
- **Multi-Agent Support**: Multiple agent instances can run concurrently
- **Session Isolation**: Each agent maintains independent state
- **Resource Pooling**: Shared browser instances and model connections

##### Vertical Scaling
- **Memory Management**: Efficient memory usage with garbage collection
- **Performance Optimization**: Caching and optimization strategies
- **Resource Monitoring**: Real-time performance tracking

##### Cloud Integration
- **Stateless Design**: Agent state can be externalized
- **API Integration**: RESTful API for remote agent control
- **Distributed Execution**: Support for distributed task execution

### Component Interaction Flow
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   User Task     │────│     Agent        │────│   LLM Model     │
│   (Natural      │    │   (service.py)   │    │   (GPT-4/etc)   │
│   Language)     │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Browser       │────│  MessageManager  │────│   Controller    │
│   Context       │    │                  │    │   (Actions)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │   GIF Generator  │
                       │   & Reporting    │
                       └──────────────────┘
```

## Core Components

### 1. Agent Service (service.py)

**Purpose**: Central orchestrator for AI-powered browser automation

**Key Responsibilities**:
- Task interpretation and execution planning
- LLM integration and response processing
- Browser state management and action coordination
- Error handling and retry logic
- Session recording and reporting

#### Agent Class

```python
class Agent(Generic[Context]):
    def __init__(
        self,
        task: str,                              # Natural language task description
        llm: BaseChatModel,                     # Language model instance
        browser: Browser | None = None,         # Browser instance
        browser_context: BrowserContext | None = None,  # Browser context
        controller: Controller[Context] = Controller(),  # Action controller
        # ... additional parameters
    )
```

#### Key Methods

- **`run(max_steps: int) -> AgentHistoryList`**: Main execution method
- **`step(step_info: AgentStepInfo) -> None`**: Execute single step
- **`multi_act(actions: list[ActionModel]) -> list[ActionResult]`**: Execute multiple actions
- **`get_next_action(input_messages: list[BaseMessage]) -> AgentOutput`**: Get AI decision
- **`take_step() -> tuple[bool, bool]`**: Take step with validation

### 2. Data Models (views.py)

#### AgentSettings
```python
class AgentSettings(BaseModel):
    use_vision: bool = True                    # Enable vision capabilities
    use_vision_for_planner: bool = False       # Use vision for planning
    max_failures: int = 3                      # Maximum consecutive failures
    retry_delay: int = 10                      # Delay between retries
    max_input_tokens: int = 128000             # Token limit for conversations
    generate_gif: bool | str = False           # GIF generation settings
    include_attributes: list[str] = [...]      # HTML attributes to include
    max_actions_per_step: int = 10             # Actions per execution step
```

#### AgentState
```python
class AgentState(BaseModel):
    agent_id: str                              # Unique agent identifier
    n_steps: int = 1                           # Current step number
    consecutive_failures: int = 0              # Failure counter
    last_result: Optional[List[ActionResult]]  # Last execution results
    history: AgentHistoryList                  # Complete execution history
    paused: bool = False                       # Pause state
    stopped: bool = False                      # Stop state
    message_manager_state: MessageManagerState # Message history state
```

#### AgentBrain
```python
class AgentBrain(BaseModel):
    evaluation_previous_goal: str              # Assessment of previous action
    memory: str                                # Agent's working memory
    next_goal: str                             # Next objective to achieve
```

#### AgentOutput
```python
class AgentOutput(BaseModel):
    current_state: AgentBrain                  # Agent's current thinking
    action: list[ActionModel]                  # Actions to execute
```

### 3. Prompt System (prompts.py)

#### SystemPrompt Class
```python
class SystemPrompt:
    def __init__(self, action_description: str, max_actions_per_step: int = 10)
    def get_system_message(self) -> SystemMessage
```

**Features**:
- Dynamic prompt template loading from `system_prompt.md`
- Action description injection
- Xbox Cloud Gaming specialization
- Configurable action limits

#### AgentMessagePrompt Class
```python
class AgentMessagePrompt:
    def __init__(
        self,
        state: BrowserState,
        result: Optional[List[ActionResult]] = None,
        include_attributes: list[str] = [],
        step_info: Optional[AgentStepInfo] = None,
    )
    def get_user_message(self, use_vision: bool = True) -> HumanMessage
```

**Features**:
- Browser state formatting for LLM consumption
- Vision integration with screenshots
- Action result integration
- Step information inclusion
- Scrolling context awareness

#### PlannerPrompt Class
```python
class PlannerPrompt(SystemPrompt):
    def get_system_message(self) -> SystemMessage
```

**Purpose**: Specialized prompt for high-level task planning and strategy

### 4. GIF Generation (gif.py)

#### create_history_gif Function
```python
def create_history_gif(
    task: str,
    history: AgentHistoryList,
    output_path: str = 'agent_history.gif',
    duration: int = 3000,
    show_goals: bool = True,
    show_task: bool = True,
    show_logo: bool = False,
    font_size: int = 40,
    # ... additional styling parameters
) -> None
```

**Features**:
- Animated session recording from screenshots
- Task and goal overlay on frames
- Customizable styling and branding
- Font and layout optimization
- Cross-platform font handling

## Usage Examples

### Basic Agent Initialization
```python
from browser_use.agent.service import Agent
from browser_use.browser.browser import Browser, BrowserConfig
from langchain_openai import AzureChatOpenAI

# Configure browser
browser_config = BrowserConfig(
    cdp_url="http://localhost:9222",
    headless=False
)

# Initialize agent
agent = Agent(
    task="Navigate to Google and search for 'AI automation'",
    llm=AzureChatOpenAI(
        azure_deployment="gpt-4o",
        api_key="your-api-key"
    ),
    browser=Browser(config=browser_config),
    use_vision=True,
    max_actions_per_step=3,
    generate_gif="session_recording.gif"
)

# Execute task
result = await agent.run(max_steps=20)
```

### Advanced Configuration
```python
from browser_use.agent.views import AgentSettings

# Custom settings
settings = AgentSettings(
    use_vision=True,
    use_vision_for_planner=True,
    max_failures=5,
    retry_delay=15,
    max_input_tokens=150000,
    generate_gif=True,
    include_attributes=[
        'title', 'type', 'name', 'role', 
        'aria-label', 'placeholder', 'value'
    ],
    max_actions_per_step=5
)

# Initialize with custom settings
agent = Agent(
    task="Complex multi-step automation task",
    llm=llm,
    browser=browser,
    **settings.dict()
)
```

### Gaming-Specific Usage
```python
# Xbox Cloud Gaming automation
agent = Agent(
    task="Launch Forza Horizon and play for 10 minutes",
    llm=llm,
    browser=browser,
    initial_actions=[
        {"go_to_url": {"url": "https://www.xbox.com/en-US/play"}}
    ],
    system_prompt_class=XboxSystemPrompt,  # Specialized for gaming
    use_vision=True,
    max_actions_per_step=1  # Careful action execution for games
)
```

### Step-by-Step Execution
```python
# Manual step control
agent = Agent(task="Multi-step task", llm=llm, browser=browser)

for step in range(10):
    step_info = AgentStepInfo(step_number=step, max_steps=10)
    await agent.step(step_info)
    
    if agent.state.history.is_done():
        break
    
    # Custom logic between steps
    if step == 5:
        agent.state.paused = True
        # Perform custom actions
        agent.state.paused = False
```

## Configuration

### Environment Integration
```python
# Integration with VizCheck config
from config import Config

config = Config()
agent = Agent(
    task=user_input,
    llm=AzureChatOpenAI(
        azure_deployment=config.get_azure_deployment(),
        api_key=config.get_azure_api_key()
    ),
    max_actions_per_step=config.get_max_actions_per_step(),
    generate_gif=config.get_generate_gif_path()
)
```

### Sensitive Data Handling
```python
sensitive_data = {
    "password": os.getenv("TEST_PASSWORD"),
    "api_key": os.getenv("API_KEY"),
    "email": "<EMAIL>"
}

agent = Agent(
    task="Login and perform actions",
    llm=llm,
    browser=browser,
    sensitive_data=sensitive_data  # Automatically masked in logs
)
```

## Best Practices

### 1. **Task Design**
```python
# Good: Specific, actionable tasks
task = "Navigate to Google, search for 'Python automation', and extract the first 3 result titles"

# Avoid: Vague or overly complex tasks
task = "Do some web stuff and find information"
```

### 2. **Error Handling**
```python
try:
    result = await agent.run(max_steps=20)
    if result.is_done() and result.success:
        print("Task completed successfully")
    else:
        print("Task failed or incomplete")
except Exception as e:
    logger.error(f"Agent execution failed: {e}")
```

### 3. **Resource Management**
```python
# Proper cleanup
try:
    agent = Agent(task=task, llm=llm, browser=browser)
    result = await agent.run()
finally:
    if not agent.injected_browser:
        await agent.browser.close()
```

## Troubleshooting

### Common Issues

#### 1. Token Limit Exceeded
**Symptoms**: `ValueError: Max token limit reached`

**Solutions**:
- Reduce `max_input_tokens`
- Limit `include_attributes`
- Use shorter task descriptions
- Implement conversation pruning

#### 2. Action Execution Failures
**Symptoms**: Repeated action failures or timeouts

**Solutions**:
- Increase `max_failures` and `retry_delay`
- Check browser connectivity
- Verify element selectors
- Review system prompt for clarity

#### 3. Vision Processing Issues
**Symptoms**: Poor element detection or interaction

**Solutions**:
- Ensure high-quality screenshots
- Verify browser viewport size
- Check element highlighting
- Test with `use_vision=False` for comparison

#### 4. Memory Leaks
**Symptoms**: Increasing memory usage over time

**Solutions**:
- Monitor `AgentState.history` size
- Implement periodic history cleanup
- Use `include_in_memory=False` for temporary results
- Regular browser context refresh

### Debug Mode
```python
import logging
logging.getLogger('browser_use.agent').setLevel(logging.DEBUG)

# Enable detailed execution logging
agent = Agent(
    task=task,
    llm=llm,
    browser=browser,
    save_conversation_path="debug_conversation.txt"
)
```

### Performance Monitoring
```python
# Monitor execution metrics
start_time = time.time()
result = await agent.run()
execution_time = time.time() - start_time

print(f"Execution time: {execution_time:.2f}s")
print(f"Steps taken: {agent.state.n_steps}")
print(f"Tokens used: {agent._message_manager.state.history.current_tokens}")
```

---

**Document Version**: 1.0  
**Last Updated**: July 2025  
**Component Version**: VizCheck 2.0.0
