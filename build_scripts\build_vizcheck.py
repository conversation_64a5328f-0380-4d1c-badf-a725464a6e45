#!/usr/bin/env python3
"""
VizCheck Build Script

This script automates the build process for VizCheck:
1. Checks existing files in dist folder
2. Runs PyInstaller with app.spec
3. Verifies successful build
4. Creates versioned folder with the executable and runtime assets
5. Organizes build artifacts
6. Supports version increment via command line

Usage:
    python build_vizcheck.py [--increment major|minor|patch]

Examples:
    python build_vizcheck.py                    # Build with current version
    python build_vizcheck.py --increment patch  # Increment patch version (0.1.0 -> 0.1.1)
    python build_vizcheck.py --increment minor  # Increment minor version (0.1.0 -> 0.2.0)
    python build_vizcheck.py --increment major  # Increment major version (0.1.0 -> 1.0.0)
"""

import os
import sys
import subprocess
import configparser
import shutil
import datetime
import argparse
from pathlib import Path
from typing import List, Optional, Tuple


class EmojiHelper:
    """Helper class to handle emoji display with ASCII fallbacks"""

    def __init__(self):
        self.use_emoji = self._can_use_emoji()

    def _can_use_emoji(self) -> bool:
        """Determine if we can safely use emoji characters"""
        try:
            # Test if we can encode a simple emoji
            "✓".encode(sys.stdout.encoding or 'utf-8')
            return True
        except (UnicodeEncodeError, LookupError):
            return False

    def get(self, emoji_name: str) -> str:
        """Get emoji or ASCII fallback"""
        emoji_map = {
            'check': '✓' if self.use_emoji else '[OK]',
            'warning': '⚠' if self.use_emoji else '[WARNING]',
            'error': '❌' if self.use_emoji else '[ERROR]',
            'info': 'ℹ' if self.use_emoji else '[INFO]',
            'folder': '📁' if self.use_emoji else '[FOLDER]',
            'file': '📄' if self.use_emoji else '[FILE]',
            'build': '🔨' if self.use_emoji else '[BUILD]',
            'success': '🎉' if self.use_emoji else '[SUCCESS]',
            'rocket': '🚀' if self.use_emoji else '[LAUNCH]',
            'gear': '⚙' if self.use_emoji else '[CONFIG]',
            'package': '📦' if self.use_emoji else '[PACKAGE]',
            'arrow': '→' if self.use_emoji else '->',
            'tree_branch': '├──' if self.use_emoji else '+--',
            'tree_end': '└──' if self.use_emoji else '+--',
            'bullet': '•' if self.use_emoji else '-'
        }
        return emoji_map.get(emoji_name, f'[{emoji_name.upper()}]')


class VizCheckBuilder:
    def __init__(self, increment_type: str = None, spec_file: str = None, use_default_assets: bool = False):
        # Initialize emoji helper for cross-platform compatibility
        self.emoji = EmojiHelper()

        # Support running from build_scripts folder or root folder
        current_dir = Path.cwd()
        if current_dir.name == "build_scripts":
            self.root_dir = current_dir.parent
            self.build_scripts_dir = current_dir
        else:
            self.root_dir = current_dir
            self.build_scripts_dir = current_dir / "build_scripts"

        self.dist_dir = self.root_dir / "dist"
        self.config_file = self.root_dir / "config" / "config.ini"
        self.specs_dir = self.build_scripts_dir / "specs"
        self.runtime_assets_dir = self.build_scripts_dir / "runtime_assets"

        # Root directories for config and assets (first priority)
        self.root_config_dir = self.root_dir / "config"
        self.root_assets_dir = self.root_dir / "assets"

        # Determine spec file to use
        self.spec_file = self._resolve_spec_file(spec_file)

        # Organized output directories
        self.builds_dir = self.dist_dir / "builds"
        self.archives_dir = self.dist_dir / "archives"
        self.backups_dir = self.dist_dir / "backups"

        self.exe_name = self._get_exe_name_from_spec()
        self.increment_type = increment_type
        self.use_default_assets = use_default_assets
        
    def log(self, message: str, level: str = "INFO"):
        """Log messages with timestamp"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")

    def _resolve_spec_file(self, spec_file: str = None) -> Path:
        """Resolve which spec file to use"""
        # Create specs directory if it doesn't exist
        self.specs_dir.mkdir(parents=True, exist_ok=True)

        if spec_file:
            # User specified a spec file
            if spec_file.endswith('.spec'):
                spec_name = spec_file
            else:
                spec_name = f"{spec_file}.spec"

            spec_path = self.specs_dir / spec_name
            if spec_path.exists():
                self.log(f"Using specified spec file: specs/{spec_name}")
                return spec_path
            else:
                self.log(f"Specified spec file not found: specs/{spec_name}", "ERROR")
                self.log("Available spec files:")
                self._list_available_specs()
                raise FileNotFoundError(f"Spec file not found: {spec_path}")
        else:
            # Default to app.spec
            default_spec = self.specs_dir / "app.spec"
            if default_spec.exists():
                self.log(f"Using default spec file: specs/app.spec")
                return default_spec
            else:
                # Check if there are any spec files available
                available_specs = list(self.specs_dir.glob("*.spec"))
                if available_specs:
                    # Use the first available spec file
                    chosen_spec = available_specs[0]
                    self.log(f"Default app.spec not found, using: specs/{chosen_spec.name}")
                    return chosen_spec
                else:
                    self.log("No spec files found in specs/ directory", "ERROR")
                    self.log("Please create at least one .spec file in the specs/ directory")
                    raise FileNotFoundError("No spec files available")

    def _list_available_specs(self):
        """List all available spec files"""
        if self.specs_dir.exists():
            spec_files = list(self.specs_dir.glob("*.spec"))
            if spec_files:
                for spec_file in spec_files:
                    self.log(f"  - {spec_file.name}")
            else:
                self.log("  No .spec files found in specs/ directory")
        else:
            self.log("  specs/ directory does not exist")

    def _get_exe_name_from_spec(self) -> str:
        """Extract executable name from app.spec file"""
        try:
            if not self.spec_file.exists():
                self.log("app.spec file not found, using default name 'VizCheck'", "WARNING")
                return "VizCheck"

            with open(self.spec_file, 'r', encoding='utf-8') as f:
                spec_content = f.read()

            # Look for the EXE section and extract the name parameter
            import re

            # Pattern to match: name='ExeName' or name="ExeName"
            name_pattern = r"name\s*=\s*['\"]([^'\"]+)['\"]"

            # Search for the pattern in the EXE section
            exe_section_pattern = r"exe\s*=\s*EXE\s*\((.*?)\)"
            exe_match = re.search(exe_section_pattern, spec_content, re.DOTALL | re.IGNORECASE)

            if exe_match:
                exe_section = exe_match.group(1)
                name_match = re.search(name_pattern, exe_section)
                if name_match:
                    exe_name = name_match.group(1)
                    self.log(f"Extracted executable name from app.spec: {exe_name}")
                    return exe_name

            # Fallback: search for name parameter anywhere in the file
            name_match = re.search(name_pattern, spec_content)
            if name_match:
                exe_name = name_match.group(1)
                self.log(f"Found executable name in app.spec: {exe_name}")
                return exe_name

            self.log("Could not find executable name in app.spec, using default 'VizCheck'", "WARNING")
            return "VizCheck"

        except Exception as e:
            self.log(f"Error reading app.spec: {e}, using default name 'VizCheck'", "WARNING")
            return "VizCheck"
    
    def check_prerequisites(self) -> bool:
        """Check if all required files exist"""
        self.log("Checking prerequisites...")

        if not self.spec_file.exists():
            self.log(f"Spec file not found at {self.spec_file}", "ERROR")
            return False

        if not self.config_file.exists():
            self.log(f"config.ini file not found at {self.config_file}", "ERROR")
            return False

        # Log the detected executable name and spec file
        self.log(f"Using spec file: {self.spec_file.relative_to(self.build_scripts_dir)}")
        self.log(f"Detected executable name: {self.exe_name}")

        # Check if PyInstaller is available
        try:
            # Use the current Python interpreter to check for PyInstaller
            result = subprocess.run(
                [sys.executable, "-m", "PyInstaller", "--version"],
                capture_output=True,
                text=True,
                check=True
            )
            self.log(f"PyInstaller is available: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.log("PyInstaller not found in current Python environment", "ERROR")
            self.log("Please install it:", "ERROR")
            self.log("  pip install pyinstaller", "ERROR")
            self.log("", "ERROR")
            self.log("If using virtual environment, make sure it's activated first:", "ERROR")
            if os.name == 'nt':  # Windows
                self.log("  venv\\Scripts\\activate", "ERROR")
            else:  # Unix/Linux/macOS
                self.log("  source venv/bin/activate", "ERROR")
            return False

        return True
    
    def get_version_info(self) -> Tuple[str, str]:
        """Read version and app name from config.ini (without updating version)"""
        self.log("Reading version information from config.ini...")

        config = configparser.ConfigParser()
        config.read(self.config_file)

        try:
            app_name = config.get('App', 'app_name', fallback='VizCheck')
            current_version = config.get('App', 'version', fallback='0.0.0')

            self.log(f"App: {app_name}, Current Version: {current_version}")

            # Use current version for build artifacts (version update happens later)
            build_version = current_version

            return app_name, build_version
        except Exception as e:
            self.log(f"Error reading config: {e}", "ERROR")
            return "VizCheck", "0.1.0"

    def update_version_after_build(self):
        """Update version in config.ini AFTER all config file copying is done"""
        if not self.increment_type:
            return  # No version increment requested

        self.log("Updating version in config.ini after build completion...")

        try:
            config = configparser.ConfigParser()
            config.read(self.config_file)

            current_version = config.get('App', 'version', fallback='0.0.0')
            new_version = self._increment_version(current_version, self.increment_type)

            self.log(f"Incrementing version in config: {current_version} -> {new_version}")
            self._update_version_in_config(config, new_version)
            self.log(f"Version updated in config.ini for next build")

        except Exception as e:
            self.log(f"Error updating version in config: {e}", "ERROR")

    def _get_base_exe_name(self) -> str:
        """Get the base executable name without any version suffix"""
        current_name = self._get_exe_name_from_spec()

        # If the name contains version pattern (name_x.y.z), extract base name
        import re
        version_pattern = r"^(.+?)_\d+\.\d+\.\d+$"
        match = re.match(version_pattern, current_name)

        if match:
            base_name = match.group(1)
            self.log(f"Extracted base name '{base_name}' from versioned name '{current_name}'")
            return base_name
        else:
            # No version suffix found, return as-is
            return current_name

    def _update_spec_with_version(self, version: str) -> bool:
        """Update app.spec file to include version in executable name"""
        try:
            if not self.spec_file.exists():
                self.log("app.spec file not found", "ERROR")
                return False

            # Read current spec file
            with open(self.spec_file, 'r', encoding='utf-8') as f:
                spec_content = f.read()

            # Get the base name (without any existing version)
            base_name = self._get_base_exe_name()

            # Create versioned exe name with current version
            versioned_exe_name = f"{base_name}_{version}"

            # Replace the name parameter in EXE section
            import re
            pattern = r"(name\s*=\s*['\"])([^'\"]+)(['\"])"

            def replace_name(match):
                return f"{match.group(1)}{versioned_exe_name}{match.group(3)}"

            updated_content = re.sub(pattern, replace_name, spec_content)

            # Write back to spec file
            with open(self.spec_file, 'w', encoding='utf-8') as f:
                f.write(updated_content)

            self.log(f"{self.emoji.get('gear')} Updated app.spec: {match.group(2) if (match := re.search(pattern, spec_content)) else 'name'} {self.emoji.get('arrow')} {versioned_exe_name}")

            # Update our exe_name to match
            self.exe_name = versioned_exe_name

            return True

        except Exception as e:
            self.log(f"Error updating app.spec: {e}", "ERROR")
            return False

    def _restore_spec_file(self) -> bool:
        """Restore original exe name in app.spec file"""
        try:
            if not self.spec_file.exists():
                return False

            # Read current spec file
            with open(self.spec_file, 'r', encoding='utf-8') as f:
                spec_content = f.read()

            # Get the base name (without version)
            base_name = self._get_base_exe_name()

            # Replace the name parameter back to base name
            import re
            pattern = r"(name\s*=\s*['\"])([^'\"]+)(['\"])"

            def replace_name(match):
                return f"{match.group(1)}{base_name}{match.group(3)}"

            updated_content = re.sub(pattern, replace_name, spec_content)

            # Write back to spec file
            with open(self.spec_file, 'w', encoding='utf-8') as f:
                f.write(updated_content)

            self.log(f"Restored app.spec: executable name restored to {base_name}")
            return True

        except Exception as e:
            self.log(f"Error restoring app.spec: {e}", "WARNING")
            return False

    def _increment_version(self, version: str, increment_type: str) -> str:
        """Increment version based on type (major, minor, patch)"""
        try:
            parts = version.split('.')
            if len(parts) != 3:
                self.log(f"Invalid version format: {version}, expected x.y.z", "WARNING")
                return version

            major, minor, patch = map(int, parts)

            if increment_type == 'major':
                major += 1
                minor = 0
                patch = 0
            elif increment_type == 'minor':
                minor += 1
                patch = 0
            elif increment_type == 'patch':
                patch += 1
            else:
                self.log(f"Invalid increment type: {increment_type}", "WARNING")
                return version

            return f"{major}.{minor}.{patch}"
        except Exception as e:
            self.log(f"Error incrementing version: {e}", "ERROR")
            return version

    def _update_version_in_config(self, config: configparser.ConfigParser, new_version: str):
        """Update version in config.ini file"""
        try:
            if not config.has_section('App'):
                config.add_section('App')

            config.set('App', 'version', new_version)

            with open(self.config_file, 'w') as f:
                config.write(f)

            self.log(f"Updated config.ini with new version: {new_version}")
        except Exception as e:
            self.log(f"Error updating config.ini: {e}", "ERROR")
    
    def list_dist_contents(self) -> List[str]:
        """List current contents of dist folder"""
        self.log("Checking current dist folder contents...")
        
        if not self.dist_dir.exists():
            self.log("Dist folder does not exist")
            return []
        
        contents = []
        for item in self.dist_dir.iterdir():
            if item.is_file():
                size = item.stat().st_size
                contents.append(f"FILE: {item.name} ({size:,} bytes)")
            else:
                contents.append(f"DIR:  {item.name}/")
        
        if contents:
            self.log("Current dist folder contents:")
            for item in contents:
                self.log(f"  {item}")
        else:
            self.log("Dist folder is empty")
            
        return contents
    
    def _create_output_directories(self):
        """Create organized output directories"""
        try:
            self.builds_dir.mkdir(parents=True, exist_ok=True)
            self.archives_dir.mkdir(parents=True, exist_ok=True)
            self.backups_dir.mkdir(parents=True, exist_ok=True)
            self.log("Created output directories")
        except Exception as e:
            self.log(f"Error creating output directories: {e}", "WARNING")

    def _move_existing_version_to_backup(self, versioned_path: Path, versioned_name: str):
        """Move existing versioned folder to backup directory"""
        try:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

            # Target backup path
            backup_target_name = versioned_name
            backup_target_path = self.backups_dir / backup_target_name

            # If backup target already exists, append timestamp
            if backup_target_path.exists():
                backup_target_name = f"{versioned_name}_{timestamp}"
                backup_target_path = self.backups_dir / backup_target_name

            self.log(f"Moving existing versioned folder to backup: backups/{backup_target_name}")

            # Ensure backup directory exists
            self.backups_dir.mkdir(parents=True, exist_ok=True)

            # Move the existing versioned folder to backup
            shutil.move(str(versioned_path), str(backup_target_path))

            self.log(f"Successfully moved builds/{versioned_name} to backups/{backup_target_name}")

        except Exception as e:
            self.log(f"Error moving versioned folder to backup: {e}", "ERROR")
            # Fallback to deletion if move fails
            try:
                self.log(f"Fallback: Removing existing versioned folder: builds/{versioned_name}")
                shutil.rmtree(versioned_path)
            except Exception as e2:
                self.log(f"Error removing versioned folder: {e2}", "ERROR")

    def backup_existing_build(self, app_name: str, version: str) -> bool:
        """Backup existing build if it exists"""
        # Check for existing executable (could be versioned or not)
        possible_exe_names = [
            f"{app_name}_{version}.exe"
            f"{app_name}.exe",
        ]

        existing_exe_paths: List[Path] = []
        for exe_name in possible_exe_names:
            exe_path = self.dist_dir / exe_name
            if exe_path.exists():
                existing_exe_paths.append(exe_path)

        for existing_exe in existing_exe_paths:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

            # Create backup folder name based on exe name (without .exe extension)
            exe_base_name = existing_exe.stem
            backup_folder_name = f"{exe_base_name}_backup"
            backup_folder_path = self.backups_dir / backup_folder_name

            # If backup folder already exists, append timestamp
            if backup_folder_path.exists():
                backup_folder_name = f"{exe_base_name}_backup_{timestamp}"
                backup_folder_path = self.backups_dir / backup_folder_name

            self.log(f"Backing up existing build to: backups/{backup_folder_name}")

            try:
                # Create backup folder
                backup_folder_path.mkdir(parents=True, exist_ok=True)

                # Copy executable to backup folder
                backup_exe_path = backup_folder_path / existing_exe.name
                shutil.copy2(existing_exe, backup_exe_path)
                self.log(f"Copied executable: {existing_exe.name}")

                # Copy runtime assets to backup folder
                if self.runtime_assets_dir.exists():
                    self.log("Copying runtime assets to backup...")
                    for item in self.runtime_assets_dir.iterdir():
                        dest_item = backup_folder_path / item.name
                        if item.is_dir():
                            shutil.copytree(item, dest_item, dirs_exist_ok=True)
                            self.log(f"Copied directory to backup: {item.name}")
                        else:
                            shutil.copy2(item, dest_item)
                            self.log(f"Copied file to backup: {item.name}")

                self.log("Backup completed successfully")
                return True

            except Exception as e:
                self.log(f"Backup failed: {e}", "ERROR")
                return False

        self.log("No existing executable found, skipping backup")
        return True
    
    def _check_venv_activated(self) -> bool:
        """Check if virtual environment is activated and provide guidance if not"""
        import sys

        # Check if we're in a virtual environment
        in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)

        if in_venv:
            self.log(f"{self.emoji.get('check')} Virtual environment is activated")
            return True

        # Not in virtual environment - provide helpful guidance
        self.log(f"{self.emoji.get('warning')} Virtual environment is NOT activated", "WARNING")

        venv_path = self.root_dir / "venv"
        if venv_path.exists():
            self.log(f"Found virtual environment at: {venv_path}")
            self.log("", "WARNING")
            self.log("RECOMMENDATION: Use the provided wrapper scripts to ensure proper venv activation:", "WARNING")
            if os.name == 'nt':  # Windows
                self.log("  - build_vizcheck.bat", "WARNING")
                self.log("  - build_vizcheck.ps1", "WARNING")
            else:  # Unix/Linux/macOS
                self.log("  - ./build_vizcheck.sh", "WARNING")
            self.log("", "WARNING")
            self.log("Or manually activate the virtual environment first:", "WARNING")
            if os.name == 'nt':  # Windows
                self.log("  venv\\Scripts\\activate", "WARNING")
            else:  # Unix/Linux/macOS
                self.log("  source venv/bin/activate", "WARNING")
        else:
            self.log("Virtual environment not found at venv/ directory", "WARNING")
            self.log("Please create a virtual environment first:", "WARNING")
            self.log("  python -m venv venv", "WARNING")
            if os.name == 'nt':  # Windows
                self.log("  venv\\Scripts\\activate", "WARNING")
            else:  # Unix/Linux/macOS
                self.log("  source venv/bin/activate", "WARNING")
            self.log("  pip install -r requirements.txt", "WARNING")

        self.log("", "WARNING")
        self.log("Continuing with current Python environment (may cause issues)...", "WARNING")
        return True  # Continue anyway, but warn user

    def _get_build_progress_config(self) -> Path:
        """Get path to build progress config file"""
        return self.build_scripts_dir / "build_progress.ini"

    def _load_previous_build_stats(self) -> dict:
        """Load previous build statistics for progress estimation"""
        progress_config_file = self._get_build_progress_config()

        if not progress_config_file.exists():
            return {"total_lines": 0, "last_build_time": 0}

        try:
            config = configparser.ConfigParser()
            config.read(progress_config_file)

            return {
                "total_lines": config.getint('BuildStats', 'total_lines', fallback=0),
                "last_build_time": config.getfloat('BuildStats', 'last_build_time', fallback=0)
            }
        except Exception as e:
            self.log(f"Warning: Could not load build progress stats: {e}", "WARNING")
            return {"total_lines": 0, "last_build_time": 0}

    def _save_build_stats(self, total_lines: int, build_time: float):
        """Save build statistics for future progress estimation"""
        progress_config_file = self._get_build_progress_config()

        try:
            config = configparser.ConfigParser()

            # Create BuildStats section if it doesn't exist
            config.add_section('BuildStats')
            config.set('BuildStats', 'total_lines', str(total_lines))
            config.set('BuildStats', 'last_build_time', str(build_time))
            config.set('BuildStats', 'last_updated', datetime.datetime.now().isoformat())

            with open(progress_config_file, 'w') as f:
                config.write(f)

            self.log(f"Saved build stats: {total_lines} lines, {build_time:.1f}s")
        except Exception as e:
            self.log(f"Warning: Could not save build progress stats: {e}", "WARNING")

    def run_pyinstaller(self) -> bool:
        """Run PyInstaller with app.spec and show progress"""
        self.log("Starting PyInstaller build...")

        # Check virtual environment status and provide guidance
        self._check_venv_activated()

        # Load previous build stats for progress estimation
        build_stats = self._load_previous_build_stats()
        expected_lines = build_stats["total_lines"]

        if expected_lines > 0:
            self.log(f"Expected progress based on previous build: ~{expected_lines} lines")
        else:
            self.log("First build - progress estimation will be available for future builds")

        try:
            import time
            start_time = time.time()

            # Use the current Python interpreter (should be from venv if properly activated)
            cmd = [sys.executable, "-m", "PyInstaller", "--clean", str(self.spec_file)]
            self.log(f"Running command: {' '.join(cmd)}")
            self.log(f"Working directory: {self.root_dir}")
            self.log(f"Using Python executable: {sys.executable}")
            self.log("")  # Empty line before progress

            # Run PyInstaller with real-time output and progress tracking
            process = subprocess.Popen(
                cmd,
                cwd=self.root_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            current_pyinstaller_line = 0
            max_pyinstaller_line = 0
            output_lines = []
            last_progress_line = ""

            # Process output line by line
            for line in process.stdout:
                line = line.rstrip()
                if line:  # Skip empty lines
                    output_lines.append(line)

                    # Extract PyInstaller's line number (format: "64760 INFO: ...")
                    import re
                    line_match = re.match(r'^(\d+)\s+(INFO|WARNING|ERROR|DEBUG):', line)
                    if line_match:
                        current_pyinstaller_line = int(line_match.group(1))
                        max_pyinstaller_line = max(max_pyinstaller_line, current_pyinstaller_line)
                        log_level = line_match.group(2)

                        # Only show progress for INFO messages, print others normally
                        if log_level == "INFO":
                            # Calculate and display progress
                            if current_pyinstaller_line > 0:
                                if expected_lines > 0:
                                    progress_percent = min(100, (current_pyinstaller_line / expected_lines) * 100)
                                    progress_line = f"\r[PROGRESS] ({progress_percent:.1f}%) - {line[:70]}..."
                                else:
                                    progress_line = f"\r[PROGRESS] Line {current_pyinstaller_line} - {line[:60]}..."

                                # Only update if the line changed (to avoid flickering)
                                if progress_line != last_progress_line:
                                    print(progress_line, end='', flush=True)
                                    last_progress_line = progress_line
                        else:
                            # For WARNING, ERROR, DEBUG - print on new line and don't overwrite
                            if last_progress_line:
                                print()  # New line to preserve progress line
                            print(f"[{log_level}] {line}")
                            last_progress_line = ""  # Reset so next INFO line will show

            # Wait for process to complete
            process.wait()
            print()  # New line after progress

            build_time = time.time() - start_time

            if process.returncode == 0:
                self.log(f"{self.emoji.get('check')} PyInstaller completed successfully in {build_time:.1f} seconds")
                self.log(f"{self.emoji.get('info')} Maximum PyInstaller line number reached: {max_pyinstaller_line}")

                # Save build stats for future progress estimation
                self._save_build_stats(max_pyinstaller_line, build_time)

                return True
            else:
                self.log("PyInstaller failed:", "ERROR")
                # Show last few lines of output for debugging
                if output_lines:
                    self.log("Last few lines of output:", "ERROR")
                    for line in output_lines[-10:]:
                        self.log(f"  {line}", "ERROR")
                return False

        except subprocess.TimeoutExpired:
            self.log("PyInstaller timed out after 10 minutes", "ERROR")
            return False
        except Exception as e:
            self.log(f"Error running PyInstaller: {e}", "ERROR")
            return False
    
    def verify_build_success(self) -> bool:
        """Verify that the build was successful"""
        self.log("Verifying build success...")

        # Check if the executable file exists directly in dist folder (with version in name)
        exe_file = self.dist_dir / f"{self.exe_name}.exe"
        if not exe_file.exists():
            self.log(f"Executable file {exe_file} not found", "ERROR")
            return False

        # Check file size (should be reasonable)
        file_size = exe_file.stat().st_size
        if file_size < 1024 * 1024:  # Less than 1MB seems too small
            self.log(f"Executable file seems too small: {file_size:,} bytes", "WARNING")

        self.log(f"Build verification successful. Executable: {self.exe_name}.exe ({file_size:,} bytes)")
        return True
    
    def _copy_assets_from_root(self, versioned_path: Path) -> bool:
        """Copy config and assets folders from root directory (first priority)"""
        assets_copied = False

        # Copy config folder from root if it exists
        if self.root_config_dir.exists():
            self.log(f"{self.emoji.get('package')} Copying config folder from root directory...")
            config_dest = versioned_path / "config"
            try:
                shutil.copytree(self.root_config_dir, config_dest, dirs_exist_ok=True)
                self.log(f"{self.emoji.get('check')} Copied config directory from root")
                assets_copied = True
            except Exception as e:
                self.log(f"{self.emoji.get('error')} Error copying config from root: {e}", "ERROR")
        else:
            self.log(f"{self.emoji.get('warning')} Root config directory not found", "WARNING")

        # Copy assets folder from root if it exists
        if self.root_assets_dir.exists():
            self.log(f"{self.emoji.get('package')} Copying assets folder from root directory...")
            assets_dest = versioned_path / "assets"
            try:
                shutil.copytree(self.root_assets_dir, assets_dest, dirs_exist_ok=True)
                self.log(f"{self.emoji.get('check')} Copied assets directory from root")
                assets_copied = True
            except Exception as e:
                self.log(f"{self.emoji.get('error')} Error copying assets from root: {e}", "ERROR")
        else:
            self.log(f"{self.emoji.get('warning')} Root assets directory not found", "WARNING")

        return assets_copied

    def _copy_assets_from_runtime(self, versioned_path: Path) -> bool:
        """Copy assets from runtime_assets folder (fallback/default assets)"""
        assets_copied = False

        if self.runtime_assets_dir.exists():
            self.log(f"{self.emoji.get('package')} Copying assets from runtime_assets directory...")
            try:
                for item in self.runtime_assets_dir.iterdir():
                    dest_item = versioned_path / item.name
                    if item.is_dir():
                        # Only copy if destination doesn't exist to avoid overwriting root assets
                        if not dest_item.exists():
                            shutil.copytree(item, dest_item, dirs_exist_ok=True)
                            self.log(f"{self.emoji.get('check')} Copied directory from runtime_assets: {item.name}")
                            assets_copied = True
                        else:
                            self.log(f"{self.emoji.get('info')} Skipped runtime_assets directory (already exists from root): {item.name}")
                    else:
                        # Only copy if destination doesn't exist to avoid overwriting root assets
                        if not dest_item.exists():
                            shutil.copy2(item, dest_item)
                            self.log(f"{self.emoji.get('check')} Copied file from runtime_assets: {item.name}")
                            assets_copied = True
                        else:
                            self.log(f"{self.emoji.get('info')} Skipped runtime_assets file (already exists from root): {item.name}")
            except Exception as e:
                self.log(f"{self.emoji.get('error')} Error copying assets from runtime_assets: {e}", "ERROR")
        else:
            self.log(f"{self.emoji.get('warning')} Runtime assets directory not found", "WARNING")

        return assets_copied

    def create_versioned_folder(self, app_name: str, version: str) -> bool:
        """Create versioned folder and organize build artifacts"""
        self.log("Creating versioned folder...")

        # Create versioned folder name
        versioned_name = f"{app_name}_{version}"
        versioned_path = self.builds_dir / versioned_name

        # Move existing versioned folder to backup if it exists
        if versioned_path.exists():
            self._move_existing_version_to_backup(versioned_path, versioned_name)

        try:
            # Create the versioned folder in builds directory
            versioned_path.mkdir(parents=True, exist_ok=True)

            # Copy the executable file (now has version in name)
            exe_source = self.dist_dir / f"{self.exe_name}.exe"
            exe_dest = versioned_path / f"{self.exe_name}.exe"

            if exe_source.exists():
                shutil.copy2(exe_source, exe_dest)
                self.log(f"Copied executable: {self.exe_name}.exe")
            else:
                self.log(f"Executable not found: {exe_source}", "ERROR")
                return False

            # FIRST PRIORITY: Copy config and assets from root folder
            assets_copied = self._copy_assets_from_root(versioned_path)

            # FALLBACK: Use runtime_assets if --default-assets flag is specified OR if no root assets were found
            if self.use_default_assets or not assets_copied:
                if self.use_default_assets:
                    self.log("Using runtime_assets (--default-assets flag specified)...")
                else:
                    self.log("No root assets found, falling back to runtime_assets...")

                runtime_assets_copied = self._copy_assets_from_runtime(versioned_path)
                assets_copied = assets_copied or runtime_assets_copied

            if not assets_copied:
                self.log("No assets were copied to the build. Check your config and assets folders.", "WARNING")

            self.log(f"Created versioned folder: builds/{versioned_name}")

            # Create a zip archive in archives directory
            zip_path = self.archives_dir / f"{versioned_name}.zip"
            if zip_path.exists():
                zip_path.unlink()

            shutil.make_archive(str(zip_path.with_suffix('')), 'zip',
                              str(versioned_path))
            self.log(f"Created zip archive: archives/{versioned_name}.zip")

            # Remove main executable from dist folder after copying
            if exe_source.exists():
                exe_source.unlink()
                self.log(f"Removed main executable from dist: {self.exe_name}.exe")

            return True

        except Exception as e:
            self.log(f"Error creating versioned folder: {e}", "ERROR")
            return False
    
    def cleanup_old_builds(self, keep_count: int = 3):
        """Clean up old build folders and archives, keeping only the most recent ones"""
        self.log(f"Cleaning up old builds (keeping {keep_count} most recent)...")

        # Get base app name (without version)
        base_app_name = self.exe_name.split('_')[0] if '_' in self.exe_name else self.exe_name

        # Clean up versioned folders in builds directory
        if self.builds_dir.exists():
            versioned_folders = []
            for item in self.builds_dir.iterdir():
                if (item.is_dir() and
                    item.name.startswith(f"{base_app_name}_") and
                    "_" in item.name):
                    versioned_folders.append(item)

            # Sort by modification time (newest first)
            versioned_folders.sort(key=lambda x: x.stat().st_mtime, reverse=True)

            # Remove old folders
            for folder in versioned_folders[keep_count:]:
                try:
                    self.log(f"Removing old build: builds/{folder.name}")
                    shutil.rmtree(folder)
                except Exception as e:
                    self.log(f"Error removing {folder.name}: {e}", "WARNING")

        # Clean up zip archives in archives directory
        if self.archives_dir.exists():
            zip_files = []
            for item in self.archives_dir.iterdir():
                if (item.is_file() and
                    item.suffix == '.zip' and
                    item.stem.startswith(f"{base_app_name}_")):
                    zip_files.append(item)

            # Sort by modification time (newest first)
            zip_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

            # Remove old zip files
            for zip_file in zip_files[keep_count:]:
                try:
                    self.log(f"Removing old archive: archives/{zip_file.name}")
                    zip_file.unlink()
                except Exception as e:
                    self.log(f"Error removing {zip_file.name}: {e}", "WARNING")
    
    def print_summary(self, app_name: str, version: str):
        """Print build summary"""
        self.log("=" * 60)
        self.log("BUILD SUMMARY")
        self.log("=" * 60)

        # Check main executable (should be removed after copying)
        exe_file = self.dist_dir / f"{self.exe_name}.exe"
        if exe_file.exists():
            self.log(f"{self.emoji.get('warning')} Main executable still exists: {exe_file}", "WARNING")
        else:
            self.log(f"{self.emoji.get('check')} Main executable removed from dist after copying")

        # Check versioned folder in builds directory
        versioned_dir = self.builds_dir / f"{app_name}_{version}"
        if versioned_dir.exists():
            self.log(f"{self.emoji.get('check')} Versioned folder: builds/{app_name}_{version}")

            # List contents of versioned folder
            contents = list(versioned_dir.iterdir())
            self.log(f"  Contents ({len(contents)} items):")
            for item in contents:
                if item.is_file():
                    size = item.stat().st_size
                    self.log(f"    FILE: {item.name} ({size:,} bytes)")
                else:
                    self.log(f"    DIR:  {item.name}/")

        # Check zip archive in archives directory
        zip_file = self.archives_dir / f"{app_name}_{version}.zip"
        if zip_file.exists():
            size = zip_file.stat().st_size
            self.log(f"{self.emoji.get('check')} Zip archive: archives/{app_name}_{version}.zip")
            self.log(f"  Size: {size:,} bytes ({size / (1024*1024):.1f} MB)")

        # Check for backup folders in backups directory
        if self.backups_dir.exists():
            backup_folders = [item for item in self.backups_dir.iterdir()
                             if item.is_dir()]
            if backup_folders:
                self.log(f"{self.emoji.get('check')} Backup folders: {len(backup_folders)}")
                for backup in backup_folders:
                    backup_type = "moved version" if backup.name.startswith(f"{app_name}_") and not "backup" in backup.name else "previous build"
                    self.log(f"    backups/{backup.name} ({backup_type})")

        # Show organized directory structure
        self.log("")
        self.log(f"{self.emoji.get('folder')} Output Structure:")
        self.log(f"   dist/")
        self.log(f"   {self.emoji.get('tree_branch')} builds/     # Versioned build folders")
        self.log(f"   {self.emoji.get('tree_branch')} archives/   # Zip archives")
        self.log(f"   {self.emoji.get('tree_end')} backups/    # Previous build backups")

        self.log("=" * 60)
    
    def build(self) -> bool:
        """Main build process"""
        self.log("Starting VizCheck build process...")

        # Check prerequisites
        if not self.check_prerequisites():
            return False

        # Get version info
        app_name, build_version = self.get_version_info()
        self.log(f"Building with version: {build_version}")

        # Update app.spec with versioned executable name
        if not self._update_spec_with_version(build_version):
            return False

        try:
            # Create organized output directories
            self._create_output_directories()

            # List current dist contents
            # self.list_dist_contents()

            # Backup existing build (with runtime assets)
            if not self.backup_existing_build(app_name, build_version):
                return False

            # Run PyInstaller
            if not self.run_pyinstaller():
                return False

            # Verify build success
            if not self.verify_build_success():
                return False

            # Create versioned folder using the current version
            if not self.create_versioned_folder(app_name, build_version):
                return False

            # UPDATE VERSION AFTER ALL CONFIG COPYING IS DONE
            self.update_version_after_build()

            # Cleanup old builds
            self.cleanup_old_builds()

            # Print summary
            self.print_summary(app_name, build_version)

            self.log(f"{self.emoji.get('success')} Build process completed successfully!")

            # Show version increment info if applicable
            if self.increment_type:
                self.log("=" * 60)
                self.log(f"{self.emoji.get('gear')} Version incremented in config.ini for next build")
                self.log(f"{self.emoji.get('rocket')} Next build will use the updated version")
                self.log("=" * 60)

            return True

        finally:
            # Always restore the original app.spec file
            self._restore_spec_file()


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="VizCheck Build Script - Automated build with version management",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python build_vizcheck.py                           # Build with default spec (app.spec)
  python build_vizcheck.py --spec console            # Use specs/console.spec
  python build_vizcheck.py --spec gui.spec           # Use specs/gui.spec
  python build_vizcheck.py --increment patch         # Increment patch version
  python build_vizcheck.py --default-assets          # Use runtime_assets as fallback
  python build_vizcheck.py --spec console --increment minor  # Use console.spec and increment minor
        """
    )

    parser.add_argument(
        '--increment',
        choices=['major', 'minor', 'patch'],
        help='Increment version before building (major, minor, or patch)'
    )

    parser.add_argument(
        '--spec',
        help='Specify which spec file to use (from specs/ directory). Default: app.spec'
    )

    parser.add_argument(
        '--list-specs',
        action='store_true',
        help='List all available spec files and exit'
    )

    parser.add_argument(
        '--default-assets',
        action='store_true',
        help='Use runtime_assets folder as fallback when root config/assets are not found'
    )

    return parser.parse_args()

def list_available_specs():
    """List all available spec files"""
    current_dir = Path.cwd()
    if current_dir.name == "build_scripts":
        build_scripts_dir = current_dir
    else:
        build_scripts_dir = current_dir / "build_scripts"

    specs_dir = build_scripts_dir / "specs"

    # Create emoji helper for this function
    emoji = EmojiHelper()
    print(f"{emoji.get('folder')} Available Spec Files:")
    print("=" * 40)

    if specs_dir.exists():
        spec_files = list(specs_dir.glob("*.spec"))
        if spec_files:
            for spec_file in sorted(spec_files):
                print(f"  - {spec_file.name}")
                # Try to extract name from spec file for additional info
                try:
                    with open(spec_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        import re
                        name_match = re.search(r"name\s*=\s*['\"]([^'\"]+)['\"]", content)
                        if name_match:
                            exe_name = name_match.group(1)
                            print(f"    {emoji.get('arrow')} Builds: {exe_name}.exe")
                except:
                    pass
                print()
        else:
            print("  No .spec files found in specs/ directory")
    else:
        print("  specs/ directory does not exist")
        print("  Run the build script once to create it")

    print("=" * 40)
    print("\nUsage:")
    print("  python build_vizcheck.py --spec <filename>")
    print("  python build_vizcheck.py --spec console")
    print("  python build_vizcheck.py --spec gui.spec")

def main():
    """Main entry point"""
    args = parse_arguments()

    # Handle list-specs command
    if args.list_specs:
        list_available_specs()
        sys.exit(0)

    try:
        builder = VizCheckBuilder(
            increment_type=args.increment,
            spec_file=args.spec,
            use_default_assets=args.default_assets
        )
        success = builder.build()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n[Build process interrupted by user]")
        sys.exit(1)
    except FileNotFoundError as e:
        emoji = EmojiHelper()
        print(f"\n{emoji.get('error')} Error: {e}")
        print("\nUse --list-specs to see available spec files")
        sys.exit(1)
    except Exception as e:
        emoji = EmojiHelper()
        print(f"\n{emoji.get('error')} Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
