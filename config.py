from utils import singleton
from dotenv import load_dotenv
import configparser
import os
import logging
import pandas as pd
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)
console_handler.setFormatter(logging.Formatter('[%(asctime)s] %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(console_handler)
logger.propagate = False


@singleton
class Config:
    """
    Config class to manage application configuration settings.
    This class loads configuration from an INI file and allows for overrides
    from environment variables. It provides methods to access various configuration
    settings such as application name, version, description, debug mode, and
    Azure Communication Service credentials.
    """ 
    def __init__(self, config_file=os.path.join('config', 'config.ini')):
        """
        Initializes the Config class, loading configuration from an INI file    
        and applying overrides from environment variables.
        Args:
            config_file (str): Path to the INI configuration file. Defaults to 'config.ini'.
        """
        self.config_file = config_file
        self._parser = configparser.ConfigParser()
        self._load_config()

    def update_config_file(self, config_file: str):
        """
        Updates the configuration file path and reloads the configuration.
        Args:
            config_file (str): New path to the INI configuration file.
        """
        self.config_file = config_file
        self._load_config()

    def _load_config(self):
        """
        Loads configuration from the INI file and then applies overrides
        from environment variables. Sets sensible defaults for everything.
        """
        if not os.path.exists(self.config_file):
            logger.warning(f"Warning: Config file '{self.config_file}' not found. Using default values.")
        else:
            try:
                self._parser.read(self.config_file)
            except configparser.Error as e:
                logger.error(f"Error parsing INI file '{self.config_file}': {e}. Using default values.")

        self._app_name = self._parser.get('App', 'app_name', fallback='VizCheck')
        self._version = self._parser.get('App', 'version', fallback='2.0.0')
        self._description = self._parser.get('App', 'description', fallback='No description provided.')
        self._debug = self._parser.getboolean('App', 'debug', fallback=False)
        self._email_service = self._parser.getboolean('App', 'email_service', fallback=False)
        
        self._start_url = self._parser.get('settings', 'start_url', fallback='https://example.com')
        self._max_num_of_steps = self._parser.getint('settings', 'max_num_of_steps', fallback=15)
        self._preview_image_path = self._parser.get('settings', 'preview_image_path', fallback='./assets/images/white.jpg')
        self._load_in_browser = self._parser.getboolean('settings', 'load_in_browser', fallback=True)
        self._output_dir = self._parser.get('settings', 'output_dir', fallback='./outputs')
        self._empty_df = pd.DataFrame(columns=["Action", "Goal", "XPath", "Value"])

        self._azure_api_key = None
        self._logging_level = 'INFO'.lower()
        self._acs_end_point = None
        self._acs_access_key = None

        self._override_with_env_vars()

    def _override_with_env_vars(self):
        """
        Overrides configuration values with environment variables if present.
        Environment variables take precedence over INI file settings for these keys.
        """
        load_dotenv()
        self._azure_api_key = os.getenv("AZURE_API_KEY", self._azure_api_key)
        self._logging_level = os.getenv("LOGGING_LEVEL", self._logging_level).lower()
        self._acs_end_point = os.getenv("ACS_END_POINT", self._acs_end_point)
        self._acs_access_key = os.getenv("ACS_ACCESS_KEY", self._acs_access_key)

    def get_output_dir(self) -> str:
        """
        Returns the output directory path.
        If not set in the INI file or environment variables, defaults to './outputs'.
        Returns:
            str: The output directory path.
        """
        return self._output_dir
    
    def get_empty_df(self) -> pd.DataFrame:
        """
        Returns an empty DataFrame with predefined columns.
        This is useful for initializing DataFrames in the application.
        Returns:
            pd.DataFrame: An empty DataFrame with columns ["Action", "Goal", "XPath", "Value"].
        """
        return self._empty_df

    def get_start_url(self) -> str:
        """
        Returns the start URL for the application.
        If not set in the INI file or environment variables, defaults to 'https://example.com'.
        Returns:
            str: The start URL.
        """
        return self._start_url

    def get_app_name(self) -> str:
        """
        Returns the application name.   
        If not set in the INI file or environment variables, defaults to 'VizCheck'.
        Returns:
            str: The application name.  
        """
        return self._app_name

    def get_version(self) -> str:
        """
        Returns the application version.
        If not set in the INI file or environment variables, defaults to '2.0.0'.
        Returns:
            str: The application version.
        """
        return self._version

    def get_description(self) -> str:
        """
        Returns the application description.    
        If not set in the INI file or environment variables, defaults to 'No description provided.'.
        Returns:
            str: The application description.
        """
        return self._description

    def get_debug_mode(self) -> bool:
        """
        Returns the debug mode setting.
        If not set in the INI file or environment variables, defaults to False.
        Returns:
            bool: True if debug mode is enabled, False otherwise.
        """
        return self._debug

    def get_azure_api_key(self) -> str | None:
        """
        Returns the Azure API key.
        If not set in the INI file or environment variables, returns None.
        Returns:
            str | None: The Azure API key, or None if not set.
        """
        return self._azure_api_key

    def get_logging_level(self) -> str:
        """
        Returns the logging level.
        If not set in the INI file or environment variables, defaults to 'INFO'.
        Returns:
            str: The logging level, in lowercase.
        """
        return self._logging_level

    def get_acs_end_point(self) -> str | None:
        """
        Returns the Azure Communication Service endpoint.
        If not set in the INI file or environment variables, returns None.
        Returns:
            str | None: The ACS endpoint, or None if not set.
        """
        return self._acs_end_point

    def get_acs_access_key(self) -> str | None:
        """
        Returns the Azure Communication Service access key. 
        If not set in the INI file or environment variables, returns None.
        Returns:
            str | None: The ACS access key, or None if not set.
        """
        return self._acs_access_key
    
    def get_max_num_of_steps(self) -> int:
        """
        Returns the max number of steps for agent execution.
        If not set in the INI file or environment variables, defaults to 15.
        Returns:    
            int: The maximum number of steps for agent execution.
        """
        return self._max_num_of_steps

    def get_preview_image_path(self) -> str:
        """
        Returns the preview image path.
        If not set in the INI file or environment variables, defaults to './assets/images/white.jpg'.
        Returns:
            str: The path to the preview image.
        """
        return self._preview_image_path

    def get_load_in_browser(self) -> bool:
        """
        Returns whether to load in browser.
        If not set in the INI file or environment variables, defaults to True.
        Returns:
            bool: True if loading in browser is enabled, False otherwise.
        """
        return self._load_in_browser

    def get_available_countries(self) -> dict:
        """
        Returns the available countries with their locale codes.
        Returns:
            dict: Dictionary mapping country names to locale codes.
        """
        # This could be loaded from config file or use the constants
        from constants import AVAILABLE_COUNTRIES_WITH_CODES
        return AVAILABLE_COUNTRIES_WITH_CODES

    # def get_all_settings_summary(self) -> dict:
    #     """
    #     Returns a summary of all configuration settings in a structured dictionary format.
    #     This includes application details and environment variables.
    #     Returns:
    #         dict: A dictionary containing all configuration settings.
    #     """
    #     return {
    #         "App": {
    #             "app_name": self.get_app_name(),
    #             "version": self.get_version(),
    #             "description": self.get_description(),
    #             "debug": self.get_debug_mode()
    #         },
    #         "Environment_Variables": {
    #             "AZURE_API_KEY": self.get_azure_api_key(),
    #             "LOGGING_LEVEL": self.get_logging_level(),
    #             "ACS_END_POINT": self.get_acs_end_point(),
    #             "ACS_ACCESS_KEY": self.get_acs_access_key()
    #         }
    #     }
