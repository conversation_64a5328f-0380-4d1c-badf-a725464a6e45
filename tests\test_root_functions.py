"""
Comprehensive test suite for all functions in root Python files.
Tests cover: app.py, config.py, browser_manager.py, utils.py, report_generator.py, 
ticket_api.py, ado_workitem.py, constants.py, email_service.py, logger_config.py
"""

import pytest
import unittest
from unittest.mock import Mock, patch, MagicMock, mock_open
import os
import sys
import tempfile
import shutil
import asyncio
from pathlib import Path
import pandas as pd
import configparser
from PIL import Image
import subprocess

# Add root directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import modules to test
import config
import utils
import browser_manager
import constants
from config import Config


class TestConfigModule(unittest.TestCase):
    """Test cases for config.py module"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.test_config_content = """
[App]
app_name = TestApp
version = 1.0.0
description = Test application
debug = true
email_service = false

[settings]
start_url = https://test.com
max_num_of_steps = 10
preview_image_path = ./test/image.jpg
load_in_browser = false
output_dir = ./test_outputs
"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_config_file = os.path.join(self.temp_dir, 'test_config.ini')
        
        with open(self.test_config_file, 'w') as f:
            f.write(self.test_config_content)
    
    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        # Clear singleton instance
        if hasattr(Config, '_instances'):
            Config._instances.clear()
    
    @patch.dict(os.environ, {
        'AZURE_API_KEY': 'test_key',
        'LOGGING_LEVEL': 'DEBUG',
        'ACS_END_POINT': 'test_endpoint',
        'ACS_ACCESS_KEY': 'test_access_key'
    })
    def test_config_initialization(self):
        """Test Config class initialization"""
        config_instance = Config(self.test_config_file)
        
        # Test app settings
        self.assertEqual(config_instance.get_app_name(), 'TestApp')
        self.assertEqual(config_instance.get_version(), '1.0.0')
        self.assertEqual(config_instance.get_description(), 'Test application')
        self.assertTrue(config_instance.get_debug_mode())
        
        # Test settings
        self.assertEqual(config_instance.get_start_url(), 'https://test.com')
        self.assertEqual(config_instance.get_max_num_of_steps(), 10)
        self.assertEqual(config_instance.get_output_dir(), './test_outputs')
        
        # Test environment variable overrides
        self.assertEqual(config_instance.get_azure_api_key(), 'test_key')
        self.assertEqual(config_instance.get_logging_level(), 'debug')
        self.assertEqual(config_instance.get_acs_end_point(), 'test_endpoint')
        self.assertEqual(config_instance.get_acs_access_key(), 'test_access_key')
    
    def test_config_missing_file(self):
        """Test Config behavior with missing config file"""
        non_existent_file = os.path.join(self.temp_dir, 'missing.ini')
        config_instance = Config(non_existent_file)
        
        # Should use default values
        self.assertEqual(config_instance.get_app_name(), 'VizCheck')
        self.assertEqual(config_instance.get_version(), '2.0.0')
        self.assertEqual(config_instance.get_max_num_of_steps(), 15)
    
    def test_config_update_file(self):
        """Test updating config file path"""
        config_instance = Config(self.test_config_file)
        original_name = config_instance.get_app_name()
        
        # Create new config file
        new_config_content = """
[App]
app_name = UpdatedApp
version = 2.0.0
"""
        new_config_file = os.path.join(self.temp_dir, 'new_config.ini')
        with open(new_config_file, 'w') as f:
            f.write(new_config_content)
        
        config_instance.update_config_file(new_config_file)
        self.assertEqual(config_instance.get_app_name(), 'UpdatedApp')
    
    def test_get_available_countries(self):
        """Test getting available countries"""
        config_instance = Config(self.test_config_file)
        countries = config_instance.get_available_countries()
        
        self.assertIsInstance(countries, dict)
        self.assertIn('United States', countries)
        self.assertEqual(countries['United States'], 'en-US')
    
    def test_get_empty_df(self):
        """Test getting empty DataFrame"""
        config_instance = Config(self.test_config_file)
        df = config_instance.get_empty_df()
        
        self.assertIsInstance(df, pd.DataFrame)
        expected_columns = ["Action", "Goal", "XPath", "Value"]
        self.assertListEqual(list(df.columns), expected_columns)
        self.assertEqual(len(df), 0)


class TestUtilsModule(unittest.TestCase):
    """Test cases for utils.py module"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_singleton_decorator(self):
        """Test singleton decorator functionality"""
        @utils.singleton
        class TestClass:
            def __init__(self, value):
                self.value = value
        
        instance1 = TestClass(1)
        instance2 = TestClass(2)
        
        # Should be the same instance
        self.assertIs(instance1, instance2)
        self.assertEqual(instance1.value, 1)  # First instance value preserved
    
    def test_ensure_folder_exists_new_folder(self):
        """Test creating new folder"""
        test_folder = os.path.join(self.temp_dir, 'new_folder')
        
        with patch('builtins.print') as mock_print:
            utils.ensure_folder_exists(test_folder)
            mock_print.assert_called_with(f"Folder '{test_folder}' created.")
        
        self.assertTrue(os.path.exists(test_folder))
    
    def test_ensure_folder_exists_existing_folder(self):
        """Test with existing folder"""
        test_folder = os.path.join(self.temp_dir, 'existing_folder')
        os.makedirs(test_folder)
        
        with patch('builtins.print') as mock_print:
            utils.ensure_folder_exists(test_folder)
            mock_print.assert_called_with(f"Folder '{test_folder}' already exists.")
    
    @patch('subprocess.run')
    def test_open_pdf(self, mock_subprocess):
        """Test opening PDF file"""
        test_pdf = 'test.pdf'
        utils.open_pdf(test_pdf)
        
        mock_subprocess.assert_called_once_with(['start', test_pdf], shell=True)
    
    def test_extract_country_found(self):
        """Test country extraction when country is found"""
        test_text = "I want to test Xbox Cloud Gaming in Germany"
        result = utils.extract_country(test_text)
        self.assertEqual(result, "Germany")
    
    def test_extract_country_not_found(self):
        """Test country extraction when no country is found"""
        test_text = "I want to test Xbox Cloud Gaming"
        
        with patch.dict(os.environ, {
            'AZURE_DEPLOYMENT': 'test-deployment',
            'OPENAI_API_VERSION': 'test-version',
            'AZURE_ENDPOINT': 'test-endpoint',
            'AZURE_API_KEY': 'test-key'
        }):
            result = utils.extract_country(test_text)
            self.assertIsNone(result)
    
    @patch('PIL.Image.open')
    @patch('os.path.exists')
    @patch('os.remove')
    def test_modify_gif_success(self, mock_remove, mock_exists, mock_image_open):
        """Test successful GIF modification"""
        mock_exists.return_value = True
        
        # Mock PIL Image
        mock_gif = Mock()
        mock_gif.info = {'duration': 100, 'loop': 0}
        mock_gif.tell.side_effect = [1, 2, 3]  # Simulate frame positions
        mock_gif.seek.side_effect = [None, None, None, EOFError()]
        mock_gif.copy.return_value = Mock()
        
        mock_image_open.return_value = mock_gif
        
        with patch('builtins.print') as mock_print:
            utils.modify_gif()
            mock_print.assert_called()
    
    @patch('os.path.exists')
    def test_modify_gif_no_file(self, mock_exists):
        """Test GIF modification when file doesn't exist"""
        mock_exists.return_value = False
        
        # Should return early without error
        result = utils.modify_gif()
        self.assertIsNone(result)


class TestBrowserManagerModule(unittest.TestCase):
    """Test cases for browser_manager.py module"""
    
    @patch('subprocess.run')
    def test_is_chrome_running_true(self, mock_subprocess):
        """Test Chrome running detection when Chrome is running"""
        mock_result = Mock()
        mock_result.stdout = "chrome.exe    1234 Console"
        mock_subprocess.return_value = mock_result
        
        result = browser_manager.is_chrome_running()
        self.assertTrue(result)
    
    @patch('subprocess.run')
    def test_is_chrome_running_false(self, mock_subprocess):
        """Test Chrome running detection when Chrome is not running"""
        mock_result = Mock()
        mock_result.stdout = "No tasks are running"
        mock_subprocess.return_value = mock_result
        
        result = browser_manager.is_chrome_running()
        self.assertFalse(result)
    
    @patch('subprocess.Popen')
    @patch('builtins.print')
    def test_launch_chrome(self, mock_print, mock_popen):
        """Test Chrome launch functionality"""
        mock_process = Mock()
        mock_process.pid = 1234
        mock_popen.return_value = mock_process
        
        browser_manager.launch_chrome()
        
        mock_popen.assert_called_once()
        mock_print.assert_called_with(1234)
    
    @patch('browser_manager.is_chrome_running')
    @patch('subprocess.check_output')
    @patch('subprocess.call')
    @patch('builtins.print')
    def test_close_chrome_with_running_processes(self, mock_print, mock_call, 
                                                mock_check_output, mock_is_running):
        """Test closing Chrome when processes are running"""
        mock_is_running.return_value = True
        mock_check_output.return_value = "ProcessId\n1234\n5678\n"
        
        with patch('playwright.sync_api.sync_playwright') as mock_playwright:
            mock_playwright.return_value.__enter__.return_value.chromium.connect_over_cdp.side_effect = Exception("Connection failed")
            
            browser_manager.close_chrome()
            
            # Should attempt to kill processes
            self.assertEqual(mock_call.call_count, 2)  # Two PIDs to kill
    
    @patch('subprocess.run')
    @patch('builtins.print')
    def test_launch_chrome_on_emulator(self, mock_print, mock_subprocess):
        """Test launching Chrome on Android emulator"""
        browser_manager.launch_chrome_on_emulator()
        
        mock_subprocess.assert_called_once_with([
            "adb", "shell", "am", "start",
            "-n", "com.android.chrome/com.google.android.apps.chrome.Main"
        ], check=True)
        mock_print.assert_called_with("Launching Chrome on the emulator...")
    
    @patch('subprocess.run')
    @patch('builtins.print')
    def test_forward_remote_debugging_port(self, mock_print, mock_subprocess):
        """Test forwarding remote debugging port"""
        browser_manager.forward_remote_debugging_port()
        
        mock_subprocess.assert_called_once_with([
            "adb", "forward", "tcp:9222", "localabstract:chrome_devtools_remote"
        ], check=True)
        mock_print.assert_any_call("Forwarding port 9222 for remote debugging...")
        mock_print.assert_any_call("Remote debugging available at http://localhost:9222")
    
    @patch('os.path.exists')
    def test_start_android_emulator_missing_executable(self, mock_exists):
        """Test starting Android emulator when executable is missing"""
        mock_exists.return_value = False
        
        with self.assertRaises(FileNotFoundError):
            browser_manager.start_android_emulator()
    
    @patch('browser_manager.start_android_emulator')
    @patch('browser_manager.launch_chrome_on_emulator')
    @patch('browser_manager.forward_remote_debugging_port')
    @patch('time.sleep')
    def test_setup_emulator_chrome_debugging(self, mock_sleep, mock_forward, 
                                           mock_launch, mock_start):
        """Test complete emulator Chrome debugging setup"""
        browser_manager.setup_emulator_chrome_debugging()
        
        mock_start.assert_called_once()
        mock_launch.assert_called_once()
        mock_sleep.assert_called_once_with(5)
        mock_forward.assert_called_once()


class TestConstantsModule(unittest.TestCase):
    """Test cases for constants.py module"""
    
    def test_available_countries_structure(self):
        """Test the structure of AVAILABLE_COUNTRIES_WITH_CODES"""
        countries = constants.AVAILABLE_COUNTRIES_WITH_CODES
        
        self.assertIsInstance(countries, dict)
        self.assertGreater(len(countries), 0)
        
        # Test some known countries
        expected_countries = {
            'United States': 'en-US',
            'United Kingdom': 'en-GB',
            'Germany': 'de-DE',
            'France': 'fr-FR',
            'Japan': 'ja-JP'
        }
        
        for country, code in expected_countries.items():
            self.assertIn(country, countries)
            self.assertEqual(countries[country], code)
    
    def test_country_codes_format(self):
        """Test that all country codes follow the expected format"""
        countries = constants.AVAILABLE_COUNTRIES_WITH_CODES
        
        for country, code in countries.items():
            # Country codes should be in format: xx-XX
            self.assertRegex(code, r'^[a-z]{2}-[A-Z]{2}$', 
                           f"Invalid format for {country}: {code}")


class TestAppModule(unittest.TestCase):
    """Test cases for app.py module functions"""
    
    @patch('app.config')
    @patch('app.extract_country')
    @patch('app.Agent')
    @patch('app.AzureChatOpenAI')
    @patch('app.Browser')
    @patch('os.getenv')
    async def test_run_agent(self, mock_getenv, mock_browser, mock_llm, 
                           mock_agent, mock_extract_country, mock_config):
        """Test run_agent function"""
        # Setup mocks
        mock_getenv.side_effect = lambda key, default=None: {
            'AZURE_DEPLOYMENT': 'gpt-4o',
            'OPENAI_API_VERSION': '2024-05-01-preview',
            'AZURE_ENDPOINT': 'https://test.openai.azure.com/',
            'AZURE_API_KEY': 'test-key'
        }.get(key, default)
        
        mock_extract_country.return_value = 'United States'
        mock_config.get_max_num_of_steps.return_value = 10
        mock_config.get_output_dir.return_value = './outputs'
        
        # Mock agent
        mock_agent_instance = Mock()
        mock_agent_instance.run = AsyncMock(return_value="test_result")
        mock_agent_instance.controller = Mock()
        mock_agent.return_value = mock_agent_instance
        
        # Import and test (need to import here to avoid circular imports)
        from app import run_agent
        
        result, exec_time = await run_agent("Test task in United States")
        
        self.assertEqual(result, "test_result")
        self.assertIsInstance(exec_time, int)
        mock_agent.assert_called_once()
    
    @patch('app.run_agent')
    @patch('app.parse_result')
    @patch('app.modify_gif')
    @patch('app.close_chrome')
    @patch('asyncio.new_event_loop')
    def test_execute_task_success(self, mock_new_loop, mock_close_chrome, 
                                mock_modify_gif, mock_parse_result, mock_run_agent):
        """Test successful task execution"""
        # Setup mocks
        mock_loop = Mock()
        mock_new_loop.return_value = mock_loop
        mock_loop.run_until_complete.return_value = ("test_result", 5)
        mock_parse_result.return_value = "parsed_data"
        
        from app import execute_task
        
        result = execute_task("Test task")
        
        mock_new_loop.assert_called_once()
        mock_parse_result.assert_called_once()
        mock_modify_gif.assert_called_once()


# Helper function for async tests
class AsyncMock(Mock):
    async def __call__(self, *args, **kwargs):
        return super(AsyncMock, self).__call__(*args, **kwargs)


if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestConfigModule,
        TestUtilsModule,
        TestBrowserManagerModule,
        TestConstantsModule,
        TestAppModule
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Exit with appropriate code
    sys.exit(0 if result.wasSuccessful() else 1)
