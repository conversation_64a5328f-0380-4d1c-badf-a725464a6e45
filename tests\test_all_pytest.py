#!/usr/bin/env python3
"""
VizCheck Core Test Suite

Professional pytest-based testing for VizCheck core functionality.

Usage:
    pytest tests/test_all_pytest.py -v
    pytest tests/test_all_pytest.py::TestConfig -v
    pytest -k "config" -v
"""

import pytest
import sys
import os
import tempfile
import shutil
from unittest.mock import Mock, patch, mock_open
import pandas as pd
from pathlib import Path

# Add root directory to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import modules to test
import config
import utils
import browser_manager
import constants
import report_generator


class TestConfig:
    """Test suite for config.py - Configuration management functionality"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_config_content = """
[App]
app_name = TestApp
version = 1.0.0
debug = true

[settings]
start_url = https://test.com
max_num_of_steps = 10
output_dir = ./test_outputs
"""
        self.test_config_file = os.path.join(self.temp_dir, 'test_config.ini')
        with open(self.test_config_file, 'w') as f:
            f.write(self.test_config_content)
    
    def teardown_method(self):
        """Cleanup after each test method"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_config_basic_functionality(self):
        """Test basic config functionality"""
        config_instance = config.Config(self.test_config_file)
        
        assert config_instance.get_app_name() == 'TestApp'
        assert config_instance.get_version() == '1.0.0'
        assert config_instance.get_debug_mode() == True
        assert config_instance.get_start_url() == 'https://test.com'
        assert config_instance.get_max_num_of_steps() == 10
    
    def test_config_environment_variables(self):
        """Test environment variable override"""
        with patch.dict(os.environ, {'AZURE_API_KEY': 'test_key'}):
            config_instance = config.Config(self.test_config_file)
            assert config_instance.get_azure_api_key() == 'test_key'
    
    def test_config_missing_file(self):
        """Test config with missing file uses defaults"""
        non_existent_file = os.path.join(self.temp_dir, 'missing.ini')
        config_instance = config.Config(non_existent_file)
        
        # Should use default values
        assert isinstance(config_instance.get_app_name(), str)
        assert isinstance(config_instance.get_version(), str)


class TestUtils:
    """Test suite for utils.py - Utility functions and decorators"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.temp_dir = tempfile.mkdtemp()
    
    def teardown_method(self):
        """Cleanup after each test method"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_singleton_decorator(self):
        """Test singleton decorator"""
        @utils.singleton
        class TestClass:
            def __init__(self, value):
                self.value = value
        
        instance1 = TestClass(1)
        instance2 = TestClass(2)
        
        assert instance1 is instance2
        assert instance1.value == 1
    
    def test_ensure_folder_exists(self):
        """Test folder creation"""
        test_folder = os.path.join(self.temp_dir, 'new_folder')
        
        assert not os.path.exists(test_folder)
        utils.ensure_folder_exists(test_folder)
        assert os.path.exists(test_folder)
    
    def test_extract_country_found(self):
        """Test country extraction"""
        test_text = "I want to test Xbox Cloud Gaming in Germany"
        result = utils.extract_country(test_text)
        assert result == "Germany"
    
    @patch('os.path.exists')
    def test_modify_gif_no_file(self, mock_exists):
        """Test GIF modification when file doesn't exist"""
        mock_exists.return_value = False
        result = utils.modify_gif()
        assert result is None


class TestBrowserManager:
    """Test suite for browser_manager.py - Chrome browser management"""
    
    @patch('subprocess.run')
    def test_is_chrome_running_true(self, mock_subprocess):
        """Test Chrome detection when running"""
        mock_result = Mock()
        mock_result.stdout = "chrome.exe    1234 Console"
        mock_subprocess.return_value = mock_result
        
        result = browser_manager.is_chrome_running()
        assert result == True
    
    @patch('subprocess.run')
    def test_is_chrome_running_false(self, mock_subprocess):
        """Test Chrome detection when not running"""
        mock_result = Mock()
        mock_result.stdout = "No tasks are running"
        mock_subprocess.return_value = mock_result
        
        result = browser_manager.is_chrome_running()
        assert result == False
    
    @patch('subprocess.Popen')
    def test_launch_chrome(self, mock_popen):
        """Test Chrome launch"""
        mock_process = Mock()
        mock_process.pid = 1234
        mock_popen.return_value = mock_process
        
        browser_manager.launch_chrome()
        mock_popen.assert_called_once()


class TestConstants:
    """Test suite for constants.py - Application constants and validation"""
    
    def test_available_countries_structure(self):
        """Test constants structure"""
        countries = constants.AVAILABLE_COUNTRIES_WITH_CODES
        
        assert isinstance(countries, dict)
        assert len(countries) > 0
        assert 'United States' in countries
        assert countries['United States'] == 'en-US'
    
    def test_country_codes_format(self):
        """Test country code format"""
        countries = constants.AVAILABLE_COUNTRIES_WITH_CODES
        
        for country, code in countries.items():
            # Country codes should be in format: xx-XX
            assert len(code) == 5
            assert code[2] == '-'
            assert code[:2].islower()
            assert code[3:].isupper()


class TestReportGenerator:
    """Test suite for report_generator.py - Report generation and processing"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.temp_dir = tempfile.mkdtemp()
        self.mock_config = Mock()
        self.mock_config.get_output_dir.return_value = self.temp_dir
    
    def teardown_method(self):
        """Cleanup after each test method"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @patch('report_generator.config')
    def test_parse_result_with_empty_history(self, mock_config_module):
        """Test parsing result with empty history"""
        mock_config_module.return_value = self.mock_config
        
        mock_result = Mock()
        mock_result.history = []
        
        with patch('report_generator.parse_and_display_result') as mock_parse_display:
            mock_parse_display.return_value = pd.DataFrame({
                'Action': [],
                'Goal': [],
                'XPath': [],
                'Value': []
            })
            
            result = report_generator.parse_result(mock_result, "Test task", 10)
            
            assert isinstance(result, pd.DataFrame)
            mock_parse_display.assert_called_once()
    
    def test_action_extraction_logic(self):
        """Test action extraction from model output"""
        action_types = [
            ('click_element', '//button[@id="test"]'),
            ('input_text', '//input[@name="username"]'),
        ]
        
        for action_type, xpath in action_types:
            mock_action = Mock()
            setattr(mock_action, action_type, Mock())
            getattr(mock_action, action_type).xpath = xpath
            
            assert hasattr(mock_action, action_type)
            assert getattr(mock_action, action_type).xpath == xpath


# Test markers for categorization
pytestmark = pytest.mark.core


if __name__ == "__main__":
    # Run tests with pytest when executed directly
    pytest.main([__file__, "-v", "--tb=short"])
