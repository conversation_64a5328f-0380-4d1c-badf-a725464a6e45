# WizCheck Build Scripts

The `build_scripts/` directory contains automated build scripts for WizCheck that handle the complete build process including:

1. ✅ Checking existing files in the `dist` folder
2. ✅ Running PyInstaller with `app.spec`
3. ✅ Verifying successful build creation
4. ✅ Reading version from `config/config.ini`
5. ✅ Creating versioned folders with format `WizCheck_<version>`
6. ✅ Creating zip archives of builds
7. ✅ Cleaning up old builds (keeps 3 most recent)
8. ✅ Comprehensive logging and error handling

## Available Scripts

### 1. Python Script (Cross-platform)
**File:** `build_scripts/build_wizcheck.py`

The main build script written in Python. Works on all platforms.

```bash
cd build_scripts
python build_wizcheck.py                    # Build with current version
python build_wizcheck.py --increment patch  # Build + increment patch for next time
python build_wizcheck.py --increment minor  # Build + increment minor for next time
python build_wizcheck.py --increment major  # Build + increment major for next time
```

### 2. Windows Batch File
**File:** `build_scripts/build_wizcheck.bat`

Simple Windows batch file for easy execution.

```cmd
cd build_scripts
build_wizcheck.bat          # Build with current version
build_wizcheck.bat patch    # Build + increment patch for next time
build_wizcheck.bat minor    # Build + increment minor for next time
build_wizcheck.bat major    # Build + increment major for next time
```

### 3. PowerShell Script (Windows)
**File:** `build_scripts/build_wizcheck.ps1`

Enhanced PowerShell script with better error handling and colored output.

```powershell
cd build_scripts
.\build_wizcheck.ps1                    # Build with current version
.\build_wizcheck.ps1 -Increment patch  # Build + increment patch for next time
.\build_wizcheck.ps1 -Increment minor  # Build + increment minor for next time
.\build_wizcheck.ps1 -Increment major  # Build + increment major for next time
```

**PowerShell Options:**
- `-Increment`: Version increment type (major, minor, patch)
- `-Verbose`: Show detailed output
- `-NoCleanup`: Skip cleanup of old builds

### 4. Shell Script (Unix/Linux/macOS)
**File:** `build_scripts/build_wizcheck.sh`

Bash script for Unix-like systems.

```bash
cd build_scripts
chmod +x build_wizcheck.sh
./build_wizcheck.sh         # Build with current version
./build_wizcheck.sh patch   # Build + increment patch for next time
./build_wizcheck.sh minor   # Build + increment minor for next time
./build_wizcheck.sh major   # Build + increment major for next time
```

## Prerequisites

Before running any build script, ensure you have:

1. **Python** installed and available in PATH
2. **PyInstaller** installed: `pip install pyinstaller`
3. **app.spec** file in the `build_scripts` directory
4. **config/config.ini** file with version information in root directory
5. All project dependencies installed

## Build Process

The build scripts perform the following steps:

### 1. Prerequisites Check
- Verifies Python and PyInstaller are available
- Checks for required files (`build_scripts/app.spec`, `config/config.ini`)

### 2. Pre-build Analysis
- Lists current contents of `dist` folder
- Backs up existing builds with timestamp

### 3. PyInstaller Execution
- Runs `pyinstaller --clean app.spec`
- Captures and logs all output
- Handles timeouts (10-minute limit)

### 4. Build Verification
- Checks if executable directory was created
- Verifies executable file exists and has reasonable size
- Validates build integrity

### 5. Version Management
- Reads app name and version from `config/config.ini`
- Creates versioned folder: `WizCheck_<version>`
- Creates zip archive: `WizCheck_<version>.zip`

### 6. Cleanup
- Removes old builds (keeps 3 most recent)
- Cleans up backup folders older than current session

### 7. Summary Report
- Shows build results with file sizes
- Lists created artifacts
- Provides success/failure status

## Version Increment Behavior

**Important**: The version increment feature works as follows:

1. **Current build uses existing version** from config.ini
2. **Version increment updates config.ini for next build**

### Example Workflow:

**Initial state**: `config.ini` has version `0.1.0`

```bash
# Build with patch increment
python build_wizcheck.py --increment patch
```

**What happens**:
1. ✅ Reads current version: `0.1.0`
2. ✅ Builds with version `0.1.0` → creates `WizCheck_0.1.0/`
3. ✅ Updates config.ini to version `0.1.1`
4. ✅ Next build will use `0.1.1`

**Result**:
- Build artifacts: `WizCheck_0.1.0/` and `WizCheck_0.1.0.zip`
- Config updated: `version = 0.1.1`

### Version Increment Types:
- **patch**: `0.1.0` → `0.1.1` (bug fixes)
- **minor**: `0.1.0` → `0.2.0` (new features)
- **major**: `0.1.0` → `1.0.0` (breaking changes)

## Configuration

The build process reads configuration from `config/config.ini`:

```ini
[App]
app_name = WizCheck
version = 0.1.0
description = A browser automation app.
```

## Output Structure

After a successful build, the `dist` folder will contain:

```
dist/
├── WizCheck/                    # Main build directory
│   ├── WizCheck.exe            # Main executable
│   ├── assets/                 # Application assets
│   └── config/                 # Configuration files
├── WizCheck_0.1.0/             # Versioned copy
│   └── [same structure as above]
├── WizCheck_0.1.0.zip          # Zip archive
└── [other builds and backups]
```

## Error Handling

The scripts include comprehensive error handling for:

- **Missing Prerequisites**: Python, PyInstaller, required files
- **Build Failures**: PyInstaller errors, compilation issues
- **File System Errors**: Permission issues, disk space
- **Timeout Issues**: Long-running builds
- **Configuration Errors**: Invalid config.ini format

## Logging

All scripts provide detailed logging with timestamps:

```
[2025-01-11 10:30:15] INFO: Starting WizCheck build process...
[2025-01-11 10:30:16] INFO: Checking prerequisites...
[2025-01-11 10:30:16] INFO: PyInstaller is available
[2025-01-11 10:30:16] INFO: App: WizCheck, Version: 0.1.0
[2025-01-11 10:30:17] INFO: Starting PyInstaller build...
[2025-01-11 10:32:45] INFO: PyInstaller completed successfully
[2025-01-11 10:32:46] INFO: Build verification successful. Executable size: 45,123,456 bytes
[2025-01-11 10:32:47] INFO: Created versioned folder: WizCheck_0.1.0
[2025-01-11 10:32:48] INFO: Build process completed successfully!
```

## Troubleshooting

### Common Issues

1. **PyInstaller Not Found**
   ```
   pip install pyinstaller
   ```

2. **Permission Errors**
   - Run as administrator (Windows)
   - Check file permissions (Unix/Linux)

3. **Build Timeout**
   - Increase timeout in script (default: 10 minutes)
   - Check for hanging processes

4. **Missing Dependencies**
   ```
   pip install -r requirements.txt
   ```

5. **Config File Issues**
   - Verify `config/config.ini` exists
   - Check file format and encoding

### Debug Mode

For detailed debugging, you can run the Python script directly and modify logging levels:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Platform-Specific Notes

### Windows
- Use `build_wizcheck.bat` for simplest execution
- Use `build_wizcheck.ps1` for enhanced features
- PowerShell execution policy may need adjustment:
  ```powershell
  Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
  ```

### macOS/Linux
- Use `build_wizcheck.sh` after making it executable
- Ensure Python 3 is available as `python3` or `python`
- May need to install additional dependencies for PyInstaller

## Continuous Integration

These scripts can be integrated into CI/CD pipelines:

```yaml
# Example GitHub Actions step
- name: Build WizCheck
  run: |
    cd build_scripts
    python build_wizcheck.py
```

## Support

For issues with the build scripts:

1. Check the detailed logs for error messages
2. Verify all prerequisites are met
3. Try running PyInstaller manually: `pyinstaller app.spec`
4. Check the WizCheck documentation for platform-specific requirements

---

**Last Updated:** January 2025  
**Compatible with:** WizCheck v0.1.0+
