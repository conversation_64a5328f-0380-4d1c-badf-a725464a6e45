@echo off
REM WizCheck Build Script for Windows
REM This batch file activates the virtual environment and runs the Python build script

echo ========================================
echo WizCheck Build Script
echo ========================================
echo.

REM Get the parent directory (project root)
for %%I in ("%~dp0..") do set "PROJECT_ROOT=%%~fI"

REM Check if virtual environment exists
if not exist "%PROJECT_ROOT%\venv\Scripts\activate.bat" (
    echo ERROR: Virtual environment not found at %PROJECT_ROOT%\venv
    echo Please create a virtual environment first:
    echo   cd %PROJECT_ROOT%
    echo   python -m venv venv
    echo   venv\Scripts\activate
    echo   pip install -r requirements.txt
    pause
    exit /b 1
)

echo Activating virtual environment...
call "%PROJECT_ROOT%\venv\Scripts\activate.bat"

REM Check if Python is available in venv
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not available in virtual environment
    echo Please check your virtual environment setup
    pause
    exit /b 1
)

echo Using Python from virtual environment:
python --version

REM Determine if we're in root or build_scripts folder and set paths accordingly
set "BUILD_SCRIPT_PATH="
set "WORKING_DIR="

REM Check if we're in the build_scripts folder
if exist "build_wizcheck.py" (
    echo Running from build_scripts folder
    set "BUILD_SCRIPT_PATH=build_wizcheck.py"
    set "WORKING_DIR=%CD%"
) else (
    REM Check if we're in the root folder
    if exist "build_scripts\build_wizcheck.py" (
        echo Running from project root folder
        set "BUILD_SCRIPT_PATH=build_wizcheck.py"
        set "WORKING_DIR=%CD%\build_scripts"
    ) else (
        echo ERROR: build_wizcheck.py not found
        echo This script must be run from either:
        echo   - Project root folder ^(where app.py is located^)
        echo   - build_scripts folder ^(where build_wizcheck.py is located^)
        echo.
        echo Current directory: %CD%
        pause
        exit /b 1
    )
)

REM Parse command line arguments
set INCREMENT_ARG=
set SPEC_ARG=
set DEFAULT_ASSETS_ARG=
set SHOW_HELP=
set NO_PAUSE=
set LIST_SPECS=

REM Check for help, list, and flags first (before other parsing)
for %%a in (%*) do (
    if "%%a"=="help" set SHOW_HELP=1
    if "%%a"=="--help" set SHOW_HELP=1
    if "%%a"=="-h" set SHOW_HELP=1
    if "%%a"=="list" set LIST_SPECS=1
    if "%%a"=="--default-assets" set DEFAULT_ASSETS_ARG=--default-assets
    if "%%a"=="--no-pause" set NO_PAUSE=1
)

if "%SHOW_HELP%"=="1" (
    echo Usage: build_wizcheck.bat [increment] [spec] [--default-assets] [--no-pause]
    echo.
    echo Arguments:
    echo   increment:        major^|minor^|patch  - Version increment type
    echo   spec:             spec_file_name       - Spec file to use ^(without .spec^)
    echo   --default-assets: Use runtime_assets as fallback
    echo   --no-pause:       Skip pause at end ^(for automated scripts^)
    echo.
    echo Examples:
    echo   build_wizcheck.bat                    # Default build
    echo   build_wizcheck.bat patch              # Increment patch version
    echo   build_wizcheck.bat minor console      # Increment minor, use console.spec
    echo   build_wizcheck.bat "" debug           # Use debug.spec, no increment
    echo   build_wizcheck.bat --default-assets   # Use runtime_assets as fallback
    echo.
    echo Available commands:
    echo   build_wizcheck.bat list               # List available spec files
    if not "%NO_PAUSE%"=="1" pause
    exit /b 0
)

REM Handle list command
if "%LIST_SPECS%"=="1" (
    cd /d "%WORKING_DIR%"
    python "%BUILD_SCRIPT_PATH%" --list-specs
    if not "%NO_PAUSE%"=="1" pause
    exit /b 0
)

REM Parse increment argument
if "%1"=="major" set INCREMENT_ARG=--increment major
if "%1"=="minor" set INCREMENT_ARG=--increment minor
if "%1"=="patch" set INCREMENT_ARG=--increment patch

REM Parse spec argument (can be first or second argument) - but skip if help/list commands
if not "%2"=="" (
    if not "%2"=="--default-assets" if not "%2"=="--no-pause" if not "%2"=="help" if not "%2"=="--help" if not "%2"=="-h" if not "%2"=="list" (
        set SPEC_ARG=--spec %2
    )
) else (
    if not "%1"=="major" if not "%1"=="minor" if not "%1"=="patch" if not "%1"=="--default-assets" if not "%1"=="--no-pause" if not "%1"=="help" if not "%1"=="--help" if not "%1"=="-h" if not "%1"=="list" if not "%1"=="" (
        set SPEC_ARG=--spec %1
        set INCREMENT_ARG=
    )
)

echo Running WizCheck build process...
if not "%INCREMENT_ARG%"=="" echo Version increment: %1
if not "%SPEC_ARG%"=="" echo Using spec file: %2
if not "%DEFAULT_ASSETS_ARG%"=="" echo Using default assets fallback
echo.

REM Change to the correct working directory and run the Python build script
cd /d "%WORKING_DIR%"
python "%BUILD_SCRIPT_PATH%" %INCREMENT_ARG% %SPEC_ARG% %DEFAULT_ASSETS_ARG%

REM Check if build was successful
if errorlevel 1 (
    echo.
    echo BUILD FAILED!
    echo Check the error messages above for details.
    if not "%NO_PAUSE%"=="1" pause
    exit /b 1
) else (
    echo.
    echo BUILD COMPLETED SUCCESSFULLY!
    echo.
    echo The executable and versioned folder have been created in the dist directory.
    echo.
)

if not "%NO_PAUSE%"=="1" pause
