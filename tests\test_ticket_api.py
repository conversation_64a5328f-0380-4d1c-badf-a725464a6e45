"""
Test suite for ticket_api.py module.
Tests FastAPI endpoints for Azure DevOps ticket management.
"""

import pytest
import unittest
from unittest.mock import Mock, patch, MagicMock
import os
import sys
import json
from fastapi.testclient import TestClient

# Add root directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the FastAPI app
from ticket_api import app, ADO_MANAGER, CreateWorkItemRequest, UpdateWorkItemRequest, WIQLQueryRequest


class TestTicketAPI(unittest.TestCase):
    """Test cases for ticket_api.py FastAPI endpoints"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.client = TestClient(app)
        
        # Mock ADO manager responses
        self.mock_work_item = {
            "id": 12345,
            "fields": {
                "System.Title": "Test Work Item",
                "System.Description": "Test Description",
                "System.WorkItemType": "Task",
                "System.State": "New"
            }
        }
        
        self.mock_query_result = {
            "workItems": [
                {"id": 12345, "url": "https://dev.azure.com/test/workItems/12345"}
            ]
        }
    
    def test_root_endpoint(self):
        """Test root endpoint returns welcome message"""
        response = self.client.get("/")
        
        self.assertEqual(response.status_code, 200)
        self.assertIn("Azure DevOps Ticket API", response.json()["message"])
    
    @patch.object(ADO_MANAGER, 'create_work_item')
    def test_create_work_item_success(self, mock_create):
        """Test successful work item creation"""
        mock_create.return_value = self.mock_work_item
        
        request_data = {
            "work_item_type": "Task",
            "title": "Test Task",
            "description": "Test Description",
            "fields": {"Priority": "2"}
        }
        
        response = self.client.post("/work-items", json=request_data)
        
        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertEqual(response_data["id"], 12345)
        self.assertEqual(response_data["fields"]["System.Title"], "Test Work Item")
        
        mock_create.assert_called_once_with(
            work_item_type="Task",
            title="Test Task",
            description="Test Description",
            additional_fields={"Priority": "2"}
        )
    
    @patch.object(ADO_MANAGER, 'create_work_item')
    def test_create_work_item_failure(self, mock_create):
        """Test work item creation failure"""
        mock_create.side_effect = Exception("ADO API Error")
        
        request_data = {
            "work_item_type": "Task",
            "title": "Test Task"
        }
        
        response = self.client.post("/work-items", json=request_data)
        
        self.assertEqual(response.status_code, 500)
        self.assertIn("Failed to create work item", response.json()["detail"])
    
    @patch.object(ADO_MANAGER, 'get_work_item')
    def test_get_work_item_success(self, mock_get):
        """Test successful work item retrieval"""
        mock_get.return_value = self.mock_work_item
        
        response = self.client.get("/work-items/12345")
        
        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertEqual(response_data["id"], 12345)
        
        mock_get.assert_called_once_with(12345)
    
    @patch.object(ADO_MANAGER, 'get_work_item')
    def test_get_work_item_not_found(self, mock_get):
        """Test work item not found"""
        mock_get.return_value = None
        
        response = self.client.get("/work-items/99999")
        
        self.assertEqual(response.status_code, 404)
        self.assertIn("Work item not found", response.json()["detail"])
    
    @patch.object(ADO_MANAGER, 'get_work_item')
    def test_get_work_item_error(self, mock_get):
        """Test work item retrieval error"""
        mock_get.side_effect = Exception("ADO API Error")
        
        response = self.client.get("/work-items/12345")
        
        self.assertEqual(response.status_code, 500)
        self.assertIn("Failed to retrieve work item", response.json()["detail"])
    
    @patch.object(ADO_MANAGER, 'update_work_item')
    def test_update_work_item_success(self, mock_update):
        """Test successful work item update"""
        updated_work_item = self.mock_work_item.copy()
        updated_work_item["fields"]["System.State"] = "In Progress"
        mock_update.return_value = updated_work_item
        
        request_data = {
            "fields_to_update": {
                "System.State": "In Progress",
                "System.AssignedTo": "<EMAIL>"
            }
        }
        
        response = self.client.put("/work-items/12345", json=request_data)
        
        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertEqual(response_data["fields"]["System.State"], "In Progress")
        
        mock_update.assert_called_once_with(12345, request_data["fields_to_update"])
    
    @patch.object(ADO_MANAGER, 'update_work_item')
    def test_update_work_item_failure(self, mock_update):
        """Test work item update failure"""
        mock_update.side_effect = Exception("Update failed")
        
        request_data = {
            "fields_to_update": {"System.State": "In Progress"}
        }
        
        response = self.client.put("/work-items/12345", json=request_data)
        
        self.assertEqual(response.status_code, 500)
        self.assertIn("Failed to update work item", response.json()["detail"])
    
    @patch.object(ADO_MANAGER, 'delete_work_item')
    def test_delete_work_item_success(self, mock_delete):
        """Test successful work item deletion"""
        mock_delete.return_value = True
        
        response = self.client.delete("/work-items/12345")
        
        self.assertEqual(response.status_code, 200)
        self.assertIn("successfully deleted", response.json()["message"])
        
        mock_delete.assert_called_once_with(12345)
    
    @patch.object(ADO_MANAGER, 'delete_work_item')
    def test_delete_work_item_failure(self, mock_delete):
        """Test work item deletion failure"""
        mock_delete.side_effect = Exception("Delete failed")
        
        response = self.client.delete("/work-items/12345")
        
        self.assertEqual(response.status_code, 500)
        self.assertIn("Failed to delete work item", response.json()["detail"])
    
    @patch.object(ADO_MANAGER, 'execute_wiql_query')
    def test_query_work_items_success(self, mock_query):
        """Test successful WIQL query execution"""
        mock_query.return_value = self.mock_query_result
        
        request_data = {
            "query": "SELECT [System.Id] FROM WorkItems WHERE [System.WorkItemType] = 'Task'"
        }
        
        response = self.client.post("/work-items/query", json=request_data)
        
        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertIn("workItems", response_data)
        self.assertEqual(len(response_data["workItems"]), 1)
        
        mock_query.assert_called_once_with(request_data["query"])
    
    @patch.object(ADO_MANAGER, 'execute_wiql_query')
    def test_query_work_items_failure(self, mock_query):
        """Test WIQL query execution failure"""
        mock_query.side_effect = Exception("Query failed")
        
        request_data = {
            "query": "INVALID QUERY"
        }
        
        response = self.client.post("/work-items/query", json=request_data)
        
        self.assertEqual(response.status_code, 500)
        self.assertIn("Failed to execute query", response.json()["detail"])
    
    def test_create_work_item_invalid_data(self):
        """Test work item creation with invalid data"""
        # Missing required fields
        request_data = {
            "work_item_type": "",  # Empty work item type
            "title": ""  # Empty title
        }
        
        response = self.client.post("/work-items", json=request_data)
        
        # Should return validation error
        self.assertEqual(response.status_code, 422)
    
    def test_update_work_item_invalid_data(self):
        """Test work item update with invalid data"""
        # Empty fields to update
        request_data = {
            "fields_to_update": {}
        }
        
        response = self.client.put("/work-items/12345", json=request_data)
        
        # Should return validation error or handle gracefully
        self.assertIn(response.status_code, [400, 422, 500])
    
    def test_query_work_items_invalid_data(self):
        """Test WIQL query with invalid data"""
        # Empty query
        request_data = {
            "query": ""
        }
        
        response = self.client.post("/work-items/query", json=request_data)
        
        # Should return validation error
        self.assertEqual(response.status_code, 422)


class TestTicketAPIModels(unittest.TestCase):
    """Test cases for Pydantic models in ticket_api.py"""
    
    def test_create_work_item_request_model(self):
        """Test CreateWorkItemRequest model validation"""
        # Valid data
        valid_data = {
            "work_item_type": "Task",
            "title": "Test Task",
            "description": "Test Description",
            "fields": {"Priority": "2"}
        }
        
        request = CreateWorkItemRequest(**valid_data)
        self.assertEqual(request.work_item_type, "Task")
        self.assertEqual(request.title, "Test Task")
        self.assertEqual(request.description, "Test Description")
        self.assertEqual(request.fields["Priority"], "2")
    
    def test_create_work_item_request_minimal(self):
        """Test CreateWorkItemRequest with minimal required fields"""
        minimal_data = {
            "work_item_type": "Bug",
            "title": "Test Bug"
        }
        
        request = CreateWorkItemRequest(**minimal_data)
        self.assertEqual(request.work_item_type, "Bug")
        self.assertEqual(request.title, "Test Bug")
        self.assertIsNone(request.description)
        self.assertIsNone(request.fields)
    
    def test_update_work_item_request_model(self):
        """Test UpdateWorkItemRequest model validation"""
        valid_data = {
            "fields_to_update": {
                "System.State": "In Progress",
                "System.AssignedTo": "<EMAIL>"
            }
        }
        
        request = UpdateWorkItemRequest(**valid_data)
        self.assertEqual(request.fields_to_update["System.State"], "In Progress")
        self.assertEqual(request.fields_to_update["System.AssignedTo"], "<EMAIL>")
    
    def test_wiql_query_request_model(self):
        """Test WIQLQueryRequest model validation"""
        valid_data = {
            "query": "SELECT [System.Id] FROM WorkItems WHERE [System.State] = 'New'"
        }
        
        request = WIQLQueryRequest(**valid_data)
        self.assertEqual(request.query, valid_data["query"])


class TestTicketAPIIntegration(unittest.TestCase):
    """Integration tests for ticket API"""
    
    def setUp(self):
        """Set up integration test fixtures"""
        self.client = TestClient(app)
    
    @patch.object(ADO_MANAGER, 'create_work_item')
    @patch.object(ADO_MANAGER, 'get_work_item')
    @patch.object(ADO_MANAGER, 'update_work_item')
    @patch.object(ADO_MANAGER, 'delete_work_item')
    def test_work_item_lifecycle(self, mock_delete, mock_update, mock_get, mock_create):
        """Test complete work item lifecycle"""
        # Setup mocks
        work_item_id = 12345
        
        # Create
        mock_create.return_value = {
            "id": work_item_id,
            "fields": {"System.Title": "Test Task", "System.State": "New"}
        }
        
        # Get
        mock_get.return_value = {
            "id": work_item_id,
            "fields": {"System.Title": "Test Task", "System.State": "New"}
        }
        
        # Update
        mock_update.return_value = {
            "id": work_item_id,
            "fields": {"System.Title": "Test Task", "System.State": "In Progress"}
        }
        
        # Delete
        mock_delete.return_value = True
        
        # Test create
        create_response = self.client.post("/work-items", json={
            "work_item_type": "Task",
            "title": "Test Task"
        })
        self.assertEqual(create_response.status_code, 200)
        
        # Test get
        get_response = self.client.get(f"/work-items/{work_item_id}")
        self.assertEqual(get_response.status_code, 200)
        
        # Test update
        update_response = self.client.put(f"/work-items/{work_item_id}", json={
            "fields_to_update": {"System.State": "In Progress"}
        })
        self.assertEqual(update_response.status_code, 200)
        
        # Test delete
        delete_response = self.client.delete(f"/work-items/{work_item_id}")
        self.assertEqual(delete_response.status_code, 200)


if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestTicketAPI,
        TestTicketAPIModels,
        TestTicketAPIIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Exit with appropriate code
    sys.exit(0 if result.wasSuccessful() else 1)
