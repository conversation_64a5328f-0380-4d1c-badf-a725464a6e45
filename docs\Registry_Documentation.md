# Registry Module Documentation

## Overview

The Registry module is the action management system of VizCheck that handles registration, validation, and execution of browser automation actions. It provides a flexible framework for defining custom actions, managing their parameters, and executing them with proper validation and error handling.

## Table of Contents

1. [Architecture](#architecture)
2. [Core Components](#core-components)
3. [Registry Service](#registry-service)
4. [Action Models](#action-models)
5. [Action Registration](#action-registration)
6. [Action Execution](#action-execution)
7. [Parameter Validation](#parameter-validation)
8. [Usage Examples](#usage-examples)
9. [Best Practices](#best-practices)
10. [Troubleshooting](#troubleshooting)

## Architecture

### Module Structure
```
browser_use/controller/registry/
├── service.py          # Core Registry implementation
└── views.py           # Data models and action structures
```

### Component Interaction Flow
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Controller    │────│     Registry     │────│  Action Models  │
│   (service.py)  │    │   (service.py)   │    │   (views.py)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Agent         │────│ Action Execution │────│   Browser       │
│   System        │    │   & Validation   │    │   Context       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │   Telemetry &    │
                       │   Monitoring     │
                       └──────────────────┘
```

### High-Level Architecture

The Registry module implements a sophisticated action management architecture that serves as the central orchestration system for browser automation actions, providing dynamic registration, intelligent validation, and secure execution capabilities.

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           VizCheck Registry System                              │
├─────────────────────────────────────────────────────────────────────────────────┤
│                              Application Layer                                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │     Agent       │  │   Controller    │  │   External      │  │    LLM      │ │
│  │   Service       │  │    Service      │  │   Integrations  │  │  Prompts    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│                            Registry Management Layer                            │
│  ┌────────────────────────────────────────────────────────────────────────────┐ │
│  │                           Registry Core                                    │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌────────────────────┐ │ │
│  │  │   Action    │  │  Parameter  │  │   Dynamic   │  │     Execution      │ │ │
│  │  │Registration │  │ Validation  │  │   Model     │  │   Orchestration    │ │ │
│  │  │             │  │             │  │ Generation  │  │                    │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └────────────────────┘ │ │
│  └────────────────────────────────────────────────────────────────────────────┘ │
│  ┌────────────────────────────────────────────────────────────────────────────┐ │
│  │                        Action Processing                                   │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌────────────────────┐ │ │
│  │  │  Decorator  │  │  Dependency │  │  Sensitive  │  │     Telemetry      │ │ │
│  │  │   System    │  │  Injection  │  │    Data     │  │   Integration      │ │ │
│  │  │             │  │             │  │  Handling   │  │                    │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └────────────────────┘ │ │
│  └────────────────────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│                            Validation & Security Layer                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   Pydantic      │  │     Type        │  │    Security     │  │    Error    │ │
│  │  Validation     │  │   Checking      │  │   Filtering     │  │  Handling   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│                            Execution Engine Layer                               │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   Browser       │  │     Gaming      │  │   Navigation    │  │   Content   │ │
│  │  Automation     │  │   Controls      │  │   Actions       │  │ Extraction  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│                            Analytics & Monitoring Layer                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   PostHog       │  │   Performance   │  │     Usage       │  │    Error    │ │
│  │  Telemetry      │  │   Monitoring    │  │   Analytics     │  │  Tracking   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### Architectural Principles

##### 1. Dynamic Action Management
The Registry operates on dynamic action registration principles:
- **Runtime Registration**: Actions can be registered at runtime using decorators
- **Automatic Model Generation**: Parameter models automatically generated from function signatures
- **Type Safety**: Full type checking and validation for all action parameters
- **Flexible Inclusion**: Actions can be selectively included or excluded

##### 2. Decorator-Driven Architecture
The system uses Python decorators for clean action registration:
- **Declarative Registration**: Simple `@registry.action()` decorator syntax
- **Metadata Extraction**: Automatic extraction of function signatures and types
- **Documentation Integration**: Action descriptions automatically included in prompts
- **Zero-Boilerplate**: Minimal code required for action registration

##### 3. Context-Aware Execution
The Registry supports generic context types for flexible execution:
- **Generic Context Support**: `Registry[Context]` supports any context type
- **Dependency Injection**: Automatic injection of required dependencies
- **Context Isolation**: Each execution maintains independent context
- **Resource Management**: Proper cleanup and resource management

##### 4. Security-First Design
Built-in security features protect sensitive data:
- **Sensitive Data Filtering**: Automatic replacement of sensitive information
- **Parameter Validation**: Strict validation of all input parameters
- **Type Enforcement**: Runtime type checking for all parameters
- **Error Isolation**: Errors contained within action execution scope

#### Action Registration Architecture

```
Function Definition → Decorator Application → Signature Analysis → Model Generation → Registry Storage
        ↓                    ↓                     ↓                  ↓                ↓
   def action_func()  → @registry.action() → inspect.signature() → create_model() → ActionRegistry
        ↓                    ↓                     ↓                  ↓                ↓
   Type Annotations → Metadata Extraction → Parameter Types → Pydantic Model → Registered Action
```

**Registration Flow:**
1. **Function Definition**: Developer defines action function with type hints
2. **Decorator Application**: `@registry.action()` decorator applied to function
3. **Signature Analysis**: Function signature analyzed for parameters and types
4. **Model Generation**: Pydantic model automatically generated for parameters
5. **Registry Storage**: Action stored in registry with metadata and validation model

#### Dynamic Model Generation Architecture

```
Function Signature → Parameter Analysis → Type Extraction → Model Creation → Validation Setup
       ↓                    ↓                ↓               ↓               ↓
inspect.signature() → Parameter List → Type Annotations → create_model() → Pydantic Validator
       ↓                    ↓                ↓               ↓               ↓
Parameter Names → Default Values → Optional Types → Field Definitions → Runtime Validation
```

##### Model Generation Process

```
Registry.create_action_model()
├── Action Selection
│   ├── Include/Exclude Filtering
│   ├── Action Availability Check
│   └── Permission Validation
├── Field Generation
│   ├── Parameter Model Extraction
│   ├── Field Type Mapping
│   └── Optional Field Handling
├── Model Assembly
│   ├── Dynamic Class Creation
│   ├── Field Combination
│   └── Validation Rules
└── Model Registration
    ├── Type Registration
    ├── Validation Setup
    └── Runtime Availability
```

#### Execution Architecture

```
Action Request → Parameter Validation → Dependency Injection → Function Execution → Result Processing
      ↓                ↓                      ↓                    ↓                  ↓
Action Name    → Pydantic Check      → Context Resolution → Function Call → Result Capture
      ↓                ↓                      ↓                    ↓                  ↓
Parameter Dict → Type Validation → Dependency Lookup → Execute Action → Error Handling
```

**Execution Pipeline:**
1. **Action Lookup**: Find registered action by name
2. **Parameter Validation**: Validate parameters against Pydantic model
3. **Dependency Resolution**: Inject required dependencies (browser, LLM, etc.)
4. **Sensitive Data Handling**: Replace sensitive data with secure values
5. **Function Execution**: Execute action function with validated parameters
6. **Result Processing**: Process and return execution results
7. **Telemetry Capture**: Record execution metrics and performance data

#### Dependency Injection Architecture

```
Function Signature → Dependency Analysis → Context Resolution → Parameter Injection
       ↓                    ↓                    ↓                   ↓
Parameter Names → Required Dependencies → Available Context → Injected Parameters
       ↓                    ↓                    ↓                   ↓
Type Annotations → Dependency Mapping → Context Lookup → Function Call
```

##### Supported Dependencies

```
Dependency Injection System
├── Browser Context
│   ├── browser: BrowserContext
│   ├── page: Page
│   └── context: BrowserContext
├── AI/LLM Services
│   ├── page_extraction_llm: LLM
│   ├── llm: LLM
│   └── model: LLM
├── Data & Configuration
│   ├── sensitive_data: dict
│   ├── available_file_paths: list
│   └── context: Any
├── Registry Services
│   ├── registry: Registry
│   ├── telemetry: ProductTelemetry
│   └── action_registry: ActionRegistry
└── Custom Dependencies
    ├── User-defined Types
    ├── Service Instances
    └── Configuration Objects
```

#### Security Architecture

```
Input Parameters → Sensitive Data Detection → Data Replacement → Secure Execution
       ↓                    ↓                      ↓                ↓
Parameter Values → Pattern Matching → Value Substitution → Protected Function Call
       ↓                    ↓                      ↓                ↓
Raw Input → Security Scan → Sanitized Input → Safe Execution
```

##### Security Measures

```
Security Layer
├── Input Validation
│   ├── Type Checking
│   ├── Range Validation
│   └── Format Verification
├── Sensitive Data Protection
│   ├── Pattern Detection
│   ├── Value Replacement
│   └── Secure Storage
├── Execution Isolation
│   ├── Context Boundaries
│   ├── Error Containment
│   └── Resource Limits
└── Output Sanitization
    ├── Result Filtering
    ├── Data Masking
    └── Safe Serialization
```

#### Telemetry Integration Architecture

```
Action Execution → Metrics Collection → Data Processing → Analytics Transmission
       ↓                ↓                   ↓                ↓
Function Call → Performance Data → Anonymization → PostHog API
       ↓                ↓                   ↓                ↓
Registry Events → Usage Statistics → Privacy Filter → Analytics Dashboard
```

##### Telemetry Data Flow

```
Registry Operations
├── Action Registration Events
│   ├── Function Registration
│   ├── Parameter Schema
│   └── Registration Timestamp
├── Execution Events
│   ├── Action Execution
│   ├── Performance Metrics
│   └── Error Events
├── Usage Analytics
│   ├── Action Popularity
│   ├── Parameter Usage
│   └── Success Rates
└── Performance Monitoring
    ├── Execution Times
    ├── Memory Usage
    └── Error Rates
```

## Core Components

### 1. Registry Service (service.py)

**Purpose**: Central action management and execution system

**Key Responsibilities**:
- Action registration and storage
- Parameter model creation and validation
- Action execution with dependency injection
- Sensitive data handling and replacement
- Telemetry and monitoring integration

#### Registry Class

```python
class Registry(Generic[Context]):
    def __init__(self, exclude_actions: list[str] = []):
        self.registry = ActionRegistry()           # Action storage
        self.telemetry = ProductTelemetry()        # Telemetry service
        self.exclude_actions = exclude_actions     # Actions to exclude
```

#### Key Methods

- **`action(description: str, param_model: Type[BaseModel] = None)`**: Decorator for action registration
- **`execute_action(action_name: str, params: dict, ...)`**: Execute registered action
- **`create_action_model(include_actions: list[str] = None)`**: Create dynamic action model
- **`get_prompt_description() -> str`**: Get action descriptions for LLM prompts
- **`_create_param_model(function: Callable)`**: Auto-generate parameter models
- **`_replace_sensitive_data(params: BaseModel, sensitive_data: dict)`**: Handle sensitive data

### 2. Action Models (views.py)

#### RegisteredAction
```python
class RegisteredAction(BaseModel):
    name: str                    # Action identifier
    description: str             # Human-readable description
    function: Callable           # Action implementation function
    param_model: Type[BaseModel] # Parameter validation model
    
    def prompt_description(self) -> str:
        """Generate LLM-friendly action description"""
```

#### ActionModel
```python
class ActionModel(BaseModel):
    """Base model for dynamically created action models"""
    
    def get_index(self) -> int | None:
        """Extract element index from action parameters"""
    
    def set_index(self, index: int):
        """Update element index in action parameters"""
```

#### ActionRegistry
```python
class ActionRegistry(BaseModel):
    actions: Dict[str, RegisteredAction] = {}
    
    def get_prompt_description(self) -> str:
        """Get descriptions of all registered actions"""
```

## Action Registration

### Using the @action Decorator

The registry provides a decorator for registering actions with automatic parameter model generation:

```python
from browser_use.controller.registry.service import Registry
from browser_use.browser.context import BrowserContext

registry = Registry()

@registry.action("Navigate to a specific URL")
async def go_to_url(url: str, browser: BrowserContext):
    """Navigate to the specified URL"""
    page = await browser.get_current_page()
    await page.goto(url)
    return f"Navigated to {url}"

@registry.action("Click on an element by its index")
async def click_element(index: int, browser: BrowserContext):
    """Click on a DOM element"""
    element = await browser.get_dom_element_by_index(index)
    await browser._click_element_node(element)
    return f"Clicked element at index {index}"
```

### Custom Parameter Models

You can provide custom parameter models for more complex validation:

```python
from pydantic import BaseModel, Field

class SearchAction(BaseModel):
    query: str = Field(..., description="Search query")
    max_results: int = Field(10, description="Maximum number of results")
    filter_type: str = Field("all", description="Type of results to filter")

@registry.action("Search for content", param_model=SearchAction)
async def search_content(params: SearchAction, browser: BrowserContext):
    """Perform a search with the given parameters"""
    # Implementation here
    return f"Searched for '{params.query}' with {params.max_results} results"
```

### Excluding Actions

You can exclude specific actions from registration:

```python
# Exclude certain actions during registry initialization
registry = Registry(exclude_actions=["dangerous_action", "deprecated_function"])

@registry.action("This action will be skipped")
async def dangerous_action():
    """This action won't be registered"""
    pass
```

## Action Execution

### Direct Execution

```python
# Execute an action directly
result = await registry.execute_action(
    action_name="go_to_url",
    params={"url": "https://example.com"},
    browser=browser_context
)
```

### Execution with Dependencies

The registry automatically injects required dependencies:

```python
# Action requiring multiple dependencies
@registry.action("Extract page content using LLM")
async def extract_content(
    query: str,
    browser: BrowserContext,
    page_extraction_llm: BaseChatModel,
    available_file_paths: list[str]
):
    """Extract content from page using AI"""
    # Implementation with all dependencies available
    pass

# Execute with dependency injection
result = await registry.execute_action(
    action_name="extract_content",
    params={"query": "Find contact information"},
    browser=browser_context,
    page_extraction_llm=llm_model,
    available_file_paths=["./uploads/file1.txt"]
)
```

### Sensitive Data Handling

The registry automatically handles sensitive data replacement:

```python
# Action with sensitive data
@registry.action("Login to system")
async def login(username: str, password: str, browser: BrowserContext):
    """Login with credentials"""
    # Implementation here
    pass

# Execute with sensitive data placeholders
sensitive_data = {
    "user_password": "actual_secret_password",
    "api_key": "real_api_key_value"
}

result = await registry.execute_action(
    action_name="login",
    params={
        "username": "testuser",
        "password": "<secret>user_password</secret>"
    },
    browser=browser_context,
    sensitive_data=sensitive_data
)
# The <secret>user_password</secret> will be replaced with "actual_secret_password"
```

## Parameter Validation

### Automatic Model Generation

The registry automatically creates Pydantic models from function signatures:

```python
@registry.action("Example action with auto-generated model")
async def example_action(
    text: str,                    # Required string parameter
    count: int = 5,              # Optional integer with default
    enabled: bool = True,        # Optional boolean with default
    browser: BrowserContext      # Excluded from parameter model
):
    """Example action demonstrating auto-generation"""
    pass

# Generated model equivalent:
class ExampleActionParameters(ActionModel):
    text: str
    count: int = 5
    enabled: bool = True
```

### Validation Features

- **Type Checking**: Automatic type validation based on annotations
- **Default Values**: Support for default parameter values
- **Required Fields**: Automatic detection of required vs optional parameters
- **Nested Models**: Support for complex nested parameter structures

### Excluded Parameters

Certain parameters are automatically excluded from parameter models:
- `browser`: BrowserContext dependency
- `page_extraction_llm`: LLM model dependency
- `available_file_paths`: File path list dependency
- `context`: Custom context object

## Usage Examples

### Basic Action Registration and Execution

```python
from browser_use.controller.registry.service import Registry
from browser_use.browser.context import BrowserContext
from browser_use.agent.views import ActionResult

# Initialize registry
registry = Registry()

# Register a simple action
@registry.action("Take a screenshot of the current page")
async def take_screenshot(browser: BrowserContext):
    """Capture a screenshot"""
    screenshot = await browser.take_screenshot()
    return ActionResult(
        extracted_content="Screenshot captured",
        include_in_memory=True
    )

# Register action with parameters
@registry.action("Fill out a form field")
async def fill_form_field(
    field_index: int,
    value: str,
    browser: BrowserContext
):
    """Fill a form field with the specified value"""
    element = await browser.get_dom_element_by_index(field_index)
    await browser._input_text_element_node(element, value)
    return ActionResult(
        extracted_content=f"Filled field {field_index} with '{value}'",
        include_in_memory=True
    )

# Execute actions
async def run_automation():
    # Take screenshot
    result1 = await registry.execute_action(
        "take_screenshot",
        {},
        browser=browser_context
    )
    
    # Fill form field
    result2 = await registry.execute_action(
        "fill_form_field",
        {"field_index": 5, "value": "<EMAIL>"},
        browser=browser_context
    )
```

### Gaming-Specific Actions

```python
# Register Xbox controller actions
@registry.action("Connect to Xbox controller")
async def connect_xbox_controller():
    """Initialize Xbox controller connection"""
    # Controller initialization logic
    return ActionResult(
        extracted_content="Xbox controller connected",
        include_in_memory=True
    )

@registry.action("Execute Xbox game controls")
async def xbox_game_control(
    actions: list[str],
    is_play_ten_min: bool = False
):
    """Execute Xbox controller actions"""
    for action in actions:
        # Execute controller action
        await execute_controller_action(action)
    
    if is_play_ten_min:
        await asyncio.sleep(600)  # Play for 10 minutes
    
    return ActionResult(
        extracted_content=f"Executed Xbox actions: {actions}",
        include_in_memory=True
    )
```

### Custom Context Actions

```python
from typing import TypeVar

# Define custom context type
GameContext = TypeVar('GameContext')

class GameRegistry(Registry[GameContext]):
    """Registry with game-specific context"""
    pass

game_registry = GameRegistry()

@game_registry.action("Save game progress")
async def save_game(
    save_slot: int,
    context: GameContext,
    browser: BrowserContext
):
    """Save game to specified slot"""
    # Use game context for save logic
    context.save_to_slot(save_slot)
    return ActionResult(
        extracted_content=f"Game saved to slot {save_slot}",
        include_in_memory=True
    )
```

### Dynamic Action Model Creation

```python
# Create action model for specific actions only
action_model = registry.create_action_model(
    include_actions=["go_to_url", "click_element", "fill_form_field"]
)

# Use in agent or LLM integration
agent_output = AgentOutput(
    current_state=agent_brain,
    action=[action_model(go_to_url={"url": "https://example.com"})]
)
```

## Best Practices

### 1. **Action Design**
```python
# Good: Clear, specific action descriptions
@registry.action("Click the submit button on the contact form")
async def click_submit_button(browser: BrowserContext):
    pass

# Avoid: Vague descriptions
@registry.action("Click something")
async def click_thing(browser: BrowserContext):
    pass
```

### 2. **Parameter Validation**
```python
from pydantic import Field, validator

class EmailAction(BaseModel):
    email: str = Field(..., description="Valid email address")
    subject: str = Field(..., min_length=1, description="Email subject")
    
    @validator('email')
    def validate_email(cls, v):
        if '@' not in v:
            raise ValueError('Invalid email format')
        return v

@registry.action("Send email", param_model=EmailAction)
async def send_email(params: EmailAction):
    pass
```

### 3. **Error Handling**
```python
@registry.action("Safe navigation with error handling")
async def safe_navigate(url: str, browser: BrowserContext):
    """Navigate with proper error handling"""
    try:
        await browser.navigate_to(url)
        return ActionResult(
            extracted_content=f"Successfully navigated to {url}",
            include_in_memory=True
        )
    except Exception as e:
        return ActionResult(
            extracted_content=f"Navigation failed: {str(e)}",
            include_in_memory=False,
            error=str(e)
        )
```

### 4. **Resource Management**
```python
@registry.action("Action with resource cleanup")
async def action_with_cleanup(browser: BrowserContext):
    """Action that properly manages resources"""
    resource = None
    try:
        resource = await acquire_resource()
        # Perform action
        result = await perform_operation(resource)
        return ActionResult(extracted_content=result, include_in_memory=True)
    finally:
        if resource:
            await release_resource(resource)
```

## Troubleshooting

### Common Issues

#### 1. Action Registration Failures
**Symptoms**: Actions not appearing in registry or execution failures

**Solutions**:
```python
# Debug action registration
def debug_registry(registry):
    print("Registered actions:")
    for name, action in registry.registry.actions.items():
        print(f"  {name}: {action.description}")
        print(f"    Parameters: {action.param_model.model_fields}")

# Check if action is excluded
registry = Registry(exclude_actions=[])  # Remove exclusions for debugging

# Verify function signature
import inspect
def check_function_signature(func):
    sig = inspect.signature(func)
    print(f"Function: {func.__name__}")
    print(f"Parameters: {list(sig.parameters.keys())}")
    print(f"Annotations: {func.__annotations__}")
```

#### 2. Parameter Validation Errors
**Symptoms**: `ValidationError` or parameter type mismatches

**Solutions**:
```python
# Debug parameter model creation
def debug_param_model(registry, function_name):
    action = registry.registry.actions[function_name]
    schema = action.param_model.model_json_schema()
    print(f"Parameter schema for {function_name}:")
    print(json.dumps(schema, indent=2))

# Test parameter validation
def test_parameters(registry, action_name, test_params):
    try:
        action = registry.registry.actions[action_name]
        validated = action.param_model(**test_params)
        print(f"Validation successful: {validated}")
    except Exception as e:
        print(f"Validation failed: {e}")

# Example usage
test_parameters(registry, "fill_form_field", {
    "field_index": "invalid",  # Should be int
    "value": "test"
})
```

#### 3. Dependency Injection Issues
**Symptoms**: Missing required dependencies or injection failures

**Solutions**:
```python
# Check required dependencies
def check_action_dependencies(registry, action_name):
    action = registry.registry.actions[action_name]
    sig = inspect.signature(action.function)

    required_deps = []
    for param_name, param in sig.parameters.items():
        if param_name in ['browser', 'page_extraction_llm', 'available_file_paths', 'context']:
            required_deps.append(param_name)

    print(f"Action '{action_name}' requires: {required_deps}")

# Ensure all dependencies are provided
async def execute_with_all_deps(registry, action_name, params):
    try:
        result = await registry.execute_action(
            action_name=action_name,
            params=params,
            browser=browser_context,           # Always provide if needed
            page_extraction_llm=llm_model,     # Provide if action needs LLM
            available_file_paths=file_paths,   # Provide if action needs files
            context=custom_context             # Provide if action needs context
        )
        return result
    except ValueError as e:
        print(f"Dependency error: {e}")
        return None
```

#### 4. Sensitive Data Replacement Issues
**Symptoms**: Sensitive data not being replaced or incorrect replacements

**Solutions**:
```python
# Debug sensitive data replacement
def debug_sensitive_data_replacement():
    test_params = {
        "username": "testuser",
        "password": "<secret>user_password</secret>",
        "api_key": "<secret>api_key</secret>"
    }

    sensitive_data = {
        "user_password": "actual_password",
        "api_key": "real_api_key"
    }

    # Test the replacement pattern
    import re
    secret_pattern = re.compile(r'<secret>(.*?)</secret>')

    for key, value in test_params.items():
        if isinstance(value, str):
            matches = secret_pattern.findall(value)
            print(f"Field '{key}': found secrets {matches}")

            for placeholder in matches:
                if placeholder in sensitive_data:
                    new_value = value.replace(
                        f'<secret>{placeholder}</secret>',
                        sensitive_data[placeholder]
                    )
                    print(f"  Replaced: {value} -> {new_value}")

# Test with actual registry
class TestModel(BaseModel):
    password: str
    api_key: str

test_model = TestModel(
    password="<secret>user_password</secret>",
    api_key="<secret>api_key</secret>"
)

sensitive_data = {"user_password": "secret123", "api_key": "key456"}
replaced_model = registry._replace_sensitive_data(test_model, sensitive_data)
print(f"Replaced model: {replaced_model.model_dump()}")
```

#### 5. Action Execution Timeouts
**Symptoms**: Actions hanging or taking too long to execute

**Solutions**:
```python
import asyncio
from functools import wraps

# Add timeout wrapper to actions
def with_timeout(seconds: int):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await asyncio.wait_for(func(*args, **kwargs), timeout=seconds)
            except asyncio.TimeoutError:
                raise RuntimeError(f"Action {func.__name__} timed out after {seconds} seconds")
        return wrapper
    return decorator

# Apply to action registration
@registry.action("Action with timeout protection")
@with_timeout(30)  # 30 second timeout
async def protected_action(browser: BrowserContext):
    # Long-running action implementation
    await some_long_operation()
    return ActionResult(extracted_content="Completed", include_in_memory=True)

# Monitor action execution time
import time

async def execute_with_timing(registry, action_name, params, **kwargs):
    start_time = time.time()
    try:
        result = await registry.execute_action(action_name, params, **kwargs)
        execution_time = time.time() - start_time
        print(f"Action '{action_name}' completed in {execution_time:.2f} seconds")
        return result
    except Exception as e:
        execution_time = time.time() - start_time
        print(f"Action '{action_name}' failed after {execution_time:.2f} seconds: {e}")
        raise
```

#### 6. Memory Leaks in Action Execution
**Symptoms**: Increasing memory usage over time

**Solutions**:
```python
import gc
import psutil
import os

# Monitor memory usage
def monitor_memory_usage():
    process = psutil.Process(os.getpid())
    memory_mb = process.memory_info().rss / 1024 / 1024
    return memory_mb

# Action with memory monitoring
@registry.action("Memory-monitored action")
async def memory_monitored_action(browser: BrowserContext):
    initial_memory = monitor_memory_usage()

    try:
        # Action implementation
        result = await perform_action()
        return result
    finally:
        # Force garbage collection
        gc.collect()

        final_memory = monitor_memory_usage()
        memory_diff = final_memory - initial_memory

        if memory_diff > 50:  # Alert if memory increased by more than 50MB
            print(f"Warning: Memory increased by {memory_diff:.2f}MB")

# Registry cleanup
def cleanup_registry(registry):
    """Clean up registry resources"""
    # Clear action cache if any
    registry.registry.actions.clear()
    gc.collect()
```

### Debug Mode

Enable detailed logging for registry operations:

```python
import logging

# Enable registry logging
logging.getLogger('browser_use.controller.registry').setLevel(logging.DEBUG)

# Custom debug registry
class DebugRegistry(Registry):
    async def execute_action(self, action_name: str, params: dict, **kwargs):
        print(f"Executing action: {action_name}")
        print(f"Parameters: {params}")
        print(f"Dependencies: {list(kwargs.keys())}")

        try:
            result = await super().execute_action(action_name, params, **kwargs)
            print(f"Action completed successfully: {result}")
            return result
        except Exception as e:
            print(f"Action failed: {e}")
            raise

# Use debug registry
debug_registry = DebugRegistry()
```

### Performance Monitoring

```python
from browser_use.utils import time_execution_async

# Monitor action performance
@time_execution_async('custom_action_timing')
async def timed_action_execution(registry, action_name, params, **kwargs):
    return await registry.execute_action(action_name, params, **kwargs)

# Batch action performance testing
async def test_action_performance(registry, test_cases):
    results = {}

    for action_name, params in test_cases.items():
        times = []
        for _ in range(5):  # Run 5 times for average
            start = time.time()
            try:
                await registry.execute_action(action_name, params)
                times.append(time.time() - start)
            except Exception as e:
                print(f"Action {action_name} failed: {e}")
                continue

        if times:
            avg_time = sum(times) / len(times)
            results[action_name] = avg_time
            print(f"{action_name}: {avg_time:.3f}s average")

    return results
```

### Testing Registry Components

```python
import pytest

# Test action registration
def test_action_registration():
    registry = Registry()

    @registry.action("Test action")
    async def test_action(param: str):
        return f"Executed with {param}"

    assert "test_action" in registry.registry.actions
    action = registry.registry.actions["test_action"]
    assert action.description == "Test action"
    assert hasattr(action.param_model, 'param')

# Test parameter validation
def test_parameter_validation():
    registry = Registry()

    @registry.action("Test validation")
    async def test_validation(count: int, text: str = "default"):
        return f"{text}: {count}"

    action = registry.registry.actions["test_validation"]

    # Valid parameters
    valid_params = action.param_model(count=5, text="test")
    assert valid_params.count == 5
    assert valid_params.text == "test"

    # Invalid parameters should raise ValidationError
    with pytest.raises(ValidationError):
        action.param_model(count="invalid")

# Run tests
async def run_registry_tests():
    test_action_registration()
    test_parameter_validation()
    print("All registry tests passed!")
```

---

**Document Version**: 1.0
**Last Updated**: July 2025
**Component Version**: VizCheck 2.0.0
