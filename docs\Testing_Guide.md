# VizCheck Testing Framework Guide

Comprehensive guide for test automation and validation using VizCheck, covering test creation, execution, and framework usage.

## Table of Contents

1. [Testing Overview](#testing-overview)
2. [Test Framework Architecture](#test-framework-architecture)
3. [Test Creation](#test-creation)
4. [Test Execution](#test-execution)
5. [Report Generation](#report-generation)
6. [Unit Testing](#unit-testing)
7. [Integration Testing](#integration-testing)
8. [Performance Testing](#performance-testing)
9. [Best Practices](#best-practices)
10. [Troubleshooting](#troubleshooting)

## Testing Overview

VizCheck provides a comprehensive testing framework for browser automation, combining AI-driven test execution with traditional testing methodologies.

### Testing Capabilities
- **Natural Language Test Cases**: Write tests in plain English
- **Automated Report Generation**: PDF reports with execution details
- **Visual Validation**: Screenshot-based verification
- **Performance Monitoring**: Execution time tracking
- **Cross-browser Testing**: Multiple browser configurations
- **Gaming Test Automation**: Xbox Cloud Gaming specific tests

### Test Types Supported
- **Functional Testing**: UI interaction and workflow validation
- **Regression Testing**: Automated re-testing of existing functionality
- **Performance Testing**: Load time and responsiveness measurement
- **Gaming Testing**: Game launch and gameplay validation
- **Cross-platform Testing**: Multi-region and multi-device testing

## Test Framework Architecture

### Core Components

#### 1. Test Execution Engine
- **Agent-based Testing**: AI agent executes test scenarios
- **Action Registry**: Standardized test actions and validations
- **Browser Management**: Automated browser lifecycle management
- **State Management**: Test state tracking and validation

#### 2. Report Generation System
- **PDF Reports**: Comprehensive test execution reports
- **HTML Templates**: Customizable report formats
- **Data Extraction**: Test result parsing and analysis
- **Visual Documentation**: Screenshot and GIF generation

#### 3. Validation Framework
- **DOM Validation**: Element presence and state verification
- **Content Validation**: Text and data extraction verification
- **Visual Validation**: Screenshot comparison and analysis
- **Performance Validation**: Timing and resource usage checks

## Test Creation

### Natural Language Test Cases

#### Basic Test Structure
```python
# Simple navigation test
test_case = """
Navigate to Google homepage.
Search for 'VizCheck automation'.
Verify search results are displayed.
Click on the first result.
"""
```

#### Complex Workflow Test
```python
# E-commerce test scenario
test_case = """
1. Navigate to the e-commerce website
2. Search for 'laptop computers'
3. Filter results by price range $500-$1000
4. Add the first item to cart
5. Proceed to checkout
6. Fill in shipping information
7. Verify order summary is correct
"""
```

#### Gaming Test Case
```python
# Xbox Cloud Gaming test
test_case = """
Launch Minecraft from Xbox Cloud Gaming in United States.
Wait for game to load completely.
Use Xbox controller to move character forward for 30 seconds.
Take screenshot of gameplay.
Return to main menu.
"""
```

### Test Configuration

#### Test Settings
```python
from config import Config

config = Config()
test_config = {
    'max_steps': config.get_max_num_of_steps(),
    'output_dir': config.get_output_dir(),
    'generate_gif': True,
    'take_screenshots': True,
    'timeout': 300  # 5 minutes
}
```

#### Browser Configuration for Testing
```python
from browser_use.browser.browser import BrowserConfig

test_browser_config = BrowserConfig(
    headless=False,  # Visual testing requires non-headless
    disable_security=True,
    window_width=1280,
    window_height=720,
    save_recording_path='./test_recordings/',
    trace_path='./test_traces/'
)
```

## Test Execution

### Running Individual Tests

#### Basic Test Execution
```python
from app import execute_task

def run_test(test_description):
    """Execute a single test case"""
    try:
        result, execution_time = execute_task(test_description)
        return {
            'status': 'PASSED' if result else 'FAILED',
            'execution_time': execution_time,
            'result': result
        }
    except Exception as e:
        return {
            'status': 'ERROR',
            'error': str(e),
            'execution_time': 0
        }

# Execute test
test_result = run_test("Navigate to Google and search for Python")
```

#### Async Test Execution
```python
import asyncio
from app import run_agent

async def run_async_test(test_description):
    """Execute test asynchronously"""
    try:
        result, execution_time = await run_agent(test_description)
        return result, execution_time
    except Exception as e:
        print(f"Test failed: {e}")
        return None, 0

# Run async test
result = asyncio.run(run_async_test("Test login functionality"))
```

### Test Suite Execution

#### Test Suite Structure
```python
class TestSuite:
    def __init__(self, name):
        self.name = name
        self.tests = []
        self.results = []
    
    def add_test(self, test_name, test_description):
        self.tests.append({
            'name': test_name,
            'description': test_description
        })
    
    def run_all_tests(self):
        """Execute all tests in the suite"""
        for test in self.tests:
            print(f"Running test: {test['name']}")
            result = run_test(test['description'])
            result['test_name'] = test['name']
            self.results.append(result)
        
        return self.results

# Example test suite
suite = TestSuite("E-commerce Regression Tests")
suite.add_test("Login Test", "Navigate to login page and sign in with valid credentials")
suite.add_test("Search Test", "Search for products and verify results")
suite.add_test("Cart Test", "Add items to cart and verify cart contents")

# Execute suite
results = suite.run_all_tests()
```

### Parallel Test Execution

#### Concurrent Testing
```python
import concurrent.futures
import threading

def run_parallel_tests(test_cases, max_workers=3):
    """Run multiple tests in parallel"""
    results = []
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tests
        future_to_test = {
            executor.submit(run_test, test['description']): test 
            for test in test_cases
        }
        
        # Collect results
        for future in concurrent.futures.as_completed(future_to_test):
            test = future_to_test[future]
            try:
                result = future.result()
                result['test_name'] = test['name']
                results.append(result)
            except Exception as e:
                results.append({
                    'test_name': test['name'],
                    'status': 'ERROR',
                    'error': str(e)
                })
    
    return results
```

## Report Generation

### Automated PDF Reports

VizCheck automatically generates comprehensive PDF reports for each test execution.

#### Report Components
- **Test Summary**: Overall test status and metrics
- **Action Details**: Step-by-step execution log
- **Screenshots**: Visual documentation of test execution
- **Performance Metrics**: Execution times and resource usage
- **Error Details**: Detailed error information for failed tests

#### Report Configuration
```python
from report_generator import parse_result

def generate_test_report(result, task_description, execution_time):
    """Generate comprehensive test report"""
    report_data = parse_result(result, task_description, execution_time)
    
    # Report includes:
    # - Action list with XPath details
    # - Goal evaluation for each step
    # - Final result summary
    # - Execution timestamp
    # - Performance metrics
    
    return report_data
```

#### Custom Report Templates

Modify `assets/html/test_report.html` to customize report appearance:

```html
<!DOCTYPE html>
<html>
<head>
    <title>VizCheck Test Report</title>
    <style>
        /* Custom styling for test reports */
        .test-header { background-color: #f0f0f0; }
        .passed { color: green; }
        .failed { color: red; }
        .error { color: orange; }
    </style>
</head>
<body>
    <!-- Report content populated by report_generator.py -->
</body>
</html>
```

## Unit Testing

### Framework Unit Tests

VizCheck includes comprehensive unit tests using pytest.

#### Running Unit Tests
```bash
# Run all tests
pytest browser_use/agent/tests.py

# Run specific test
pytest browser_use/agent/tests.py::test_action_creation

# Run with verbose output
pytest -v browser_use/agent/tests.py

# Run with coverage
pytest --cov=browser_use browser_use/agent/tests.py
```

#### Test Structure Example
```python
import pytest
from browser_use.agent.service import Agent
from browser_use.browser.views import BrowserState

@pytest.fixture
def sample_browser_state():
    return BrowserState(
        url='https://example.com',
        title='Example Page',
        tabs=[],
        screenshot='test.png',
        element_tree=None,
        selector_map={}
    )

def test_agent_initialization():
    """Test agent initialization"""
    agent = Agent(
        task="Test task",
        llm=mock_llm,
        browser=mock_browser
    )
    assert agent.task == "Test task"
    assert agent.state is not None

def test_action_execution(sample_browser_state):
    """Test action execution"""
    # Test implementation
    pass
```

### Custom Test Development

#### Creating Custom Tests
```python
def test_custom_functionality():
    """Test custom VizCheck functionality"""
    # Setup
    test_config = Config()
    
    # Execute
    result = execute_task("Custom test scenario")
    
    # Verify
    assert result is not None
    assert 'error' not in str(result).lower()
```

## Integration Testing

### Browser Integration Tests

#### DOM Testing
```python
from browser_use.dom.service import DomService

async def test_dom_extraction():
    """Test DOM element extraction"""
    browser = Browser()
    async with browser.new_context() as context:
        page = await context.get_current_page()
        await page.goto('https://example.com')
        
        dom_service = DomService(page)
        dom_state = await dom_service.get_clickable_elements()
        
        assert dom_state.element_tree is not None
        assert len(dom_state.selector_map) > 0
```

#### Controller Integration Tests
```python
from browser_use.controller.service import Controller

def test_controller_actions():
    """Test controller action execution"""
    controller = Controller()
    
    # Test action registration
    actions = controller.registry.get_action_names()
    assert 'click_element' in actions
    assert 'input_text' in actions
    assert 'go_to_url' in actions
```

### End-to-End Testing

#### Complete Workflow Tests
```python
async def test_complete_workflow():
    """Test complete automation workflow"""
    task = """
    Navigate to https://httpbin.org/forms/post
    Fill in the customer name field with 'Test User'
    Fill in the telephone field with '************'
    Click the submit button
    Verify the form was submitted successfully
    """
    
    result, execution_time = await run_agent(task)
    
    # Verify successful execution
    assert result is not None
    assert execution_time > 0
    
    # Check final result
    final_result = result.history[-1].result[0] if result.history else None
    assert final_result is not None
```

## Performance Testing

### Execution Time Monitoring

#### Performance Metrics Collection
```python
import time
from datetime import datetime

class PerformanceMonitor:
    def __init__(self):
        self.metrics = []
    
    def start_test(self, test_name):
        return {
            'test_name': test_name,
            'start_time': time.time(),
            'start_datetime': datetime.now()
        }
    
    def end_test(self, test_context, result):
        end_time = time.time()
        execution_time = end_time - test_context['start_time']
        
        metrics = {
            'test_name': test_context['test_name'],
            'execution_time': execution_time,
            'start_time': test_context['start_datetime'],
            'end_time': datetime.now(),
            'status': 'PASSED' if result else 'FAILED'
        }
        
        self.metrics.append(metrics)
        return metrics

# Usage
monitor = PerformanceMonitor()
test_context = monitor.start_test("Login Performance Test")
result = execute_task("Login with valid credentials")
metrics = monitor.end_test(test_context, result)
```

### Load Testing

#### Concurrent User Simulation
```python
import asyncio
from concurrent.futures import ThreadPoolExecutor

async def simulate_concurrent_users(num_users, test_scenario):
    """Simulate multiple concurrent users"""
    
    async def user_session(user_id):
        print(f"User {user_id} starting test")
        start_time = time.time()
        
        try:
            result, exec_time = await run_agent(test_scenario)
            return {
                'user_id': user_id,
                'status': 'SUCCESS',
                'execution_time': exec_time,
                'total_time': time.time() - start_time
            }
        except Exception as e:
            return {
                'user_id': user_id,
                'status': 'FAILED',
                'error': str(e),
                'total_time': time.time() - start_time
            }
    
    # Run concurrent sessions
    tasks = [user_session(i) for i in range(num_users)]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    return results

# Run load test
load_test_results = asyncio.run(
    simulate_concurrent_users(5, "Navigate to homepage and search")
)
```

## Best Practices

### Test Design Principles

1. **Clear Test Objectives**: Define specific, measurable test goals
2. **Atomic Tests**: Each test should focus on a single functionality
3. **Repeatable Tests**: Tests should produce consistent results
4. **Independent Tests**: Tests should not depend on other tests
5. **Comprehensive Coverage**: Cover both positive and negative scenarios

### Test Data Management

#### Test Data Organization
```python
# test_data.py
TEST_SCENARIOS = {
    'login': {
        'valid_user': "Login with username 'testuser' and password 'password123'",
        'invalid_user': "Login with username 'invalid' and password 'wrong'",
        'empty_fields': "Attempt login with empty username and password"
    },
    'search': {
        'basic_search': "Search for 'automation testing'",
        'no_results': "Search for 'xyznonexistentterm'",
        'special_chars': "Search for '@#$%^&*()'"
    }
}
```

### Error Handling

#### Robust Test Implementation
```python
def execute_test_with_retry(test_description, max_retries=3):
    """Execute test with retry logic"""
    for attempt in range(max_retries):
        try:
            result, execution_time = execute_task(test_description)
            return result, execution_time
        except Exception as e:
            print(f"Attempt {attempt + 1} failed: {e}")
            if attempt == max_retries - 1:
                raise
            time.sleep(5)  # Wait before retry
```

### Test Environment Management

#### Environment Configuration
```python
# test_config.py
TEST_ENVIRONMENTS = {
    'development': {
        'base_url': 'https://dev.example.com',
        'timeout': 30,
        'headless': False
    },
    'staging': {
        'base_url': 'https://staging.example.com',
        'timeout': 60,
        'headless': True
    },
    'production': {
        'base_url': 'https://example.com',
        'timeout': 120,
        'headless': True
    }
}
```

## Troubleshooting

### Common Testing Issues

#### Test Execution Failures
```
Issue: Tests fail intermittently
Solutions:
1. Add explicit waits for dynamic content
2. Increase timeout values
3. Check network connectivity
4. Verify element selectors
```

#### Report Generation Issues
```
Issue: PDF reports not generating
Solutions:
1. Check output directory permissions
2. Verify Chrome/Chromium installation
3. Ensure sufficient disk space
4. Check HTML template validity
```

#### Performance Issues
```
Issue: Tests running slowly
Solutions:
1. Optimize browser configuration
2. Reduce screenshot frequency
3. Use headless mode when possible
4. Implement parallel execution
```

### Debugging Techniques

#### Debug Mode Testing
```python
# Enable debug mode for detailed logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Run test with debug information
result = execute_task("Debug test scenario")
```

#### Test Isolation
```python
def isolated_test(test_function):
    """Decorator for test isolation"""
    def wrapper(*args, **kwargs):
        # Setup clean environment
        setup_test_environment()
        
        try:
            return test_function(*args, **kwargs)
        finally:
            # Cleanup after test
            cleanup_test_environment()
    
    return wrapper

@isolated_test
def test_with_isolation():
    # Test implementation
    pass
```

---

**Next Steps**:
- Review [Agent Documentation](Agent_Documentation.md) for advanced automation
- Check [Browser Documentation](Browser_Documentation.md) for browser configuration
- See [Performance Optimization Guide](Performance_Guide.md) for optimization strategies
