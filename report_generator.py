from utils import open_pdf
from browser_manager import launch_chrome, close_chrome
import os   
import datetime
import pandas as pd 
from playwright.sync_api import sync_playwright
from config import Config

config = Config()

def parse_result(result, task_text, exec_time):
    action_list = []
    for history in result.history:
        try:
            if "model_output" in history.model_fields_set and history.model_output.model_fields_set and "action" in history.model_output.model_fields_set:
                action_dict = None
                try:
                    for index, action in enumerate(history.model_output.action):
                        try:
                            for step in action.model_fields_set:
                                action_step = getattr(action, step)
                                xpath = None
                                if hasattr(action_step, "index"):
                                    try:
                                        for interacted_element in history.state.interacted_element:
                                            if interacted_element.highlight_index == action_step.index:
                                                xpath = interacted_element.xpath
                                    except Exception as e:
                                        pass
                                if action_dict is None:
                                    try:
                                        action_dict = {"action": action_step, "xpath": xpath, "goal": history.model_output.current_state.next_goal}
                                    except Exception as e:
                                        action_dict = {"action": action_step, "xpath": xpath}
                                else:
                                    action_dict = {"action": action_step, "xpath": xpath}
                                try:
                                    action_dict["evaluation_previous_goal"] = history.model_output.current_state.evaluation_previous_goal
                                except Exception as e:
                                    pass
                                action_list.append(action_dict)
                        except Exception as e:
                            pass
                except Exception as e:
                    pass
        except Exception as e:
            pass
                    
    try:
        final_result = result.history[-1].result[0]
    except Exception as e:
        final_result = ""

    return parse_and_display_result(action_list, task_text, exec_time, final_result)

def parse_and_display_result(result, task_text, exec_time, final_result):
    parsed_data = []
    test_report_data = []
    final_result_summary = ""
    prev_item = None
    if not isinstance(result, list):
        print("Result is not a list.")
        return empty_df
    for item in result:
        try:
            if hasattr(item["action"], "url"):
                value = item["action"].url
            elif hasattr(item["action"], "text"):
                value = item["action"].text
            else:
                value = ""
            xpath = item.get("xpath", "")
            goal = item.get("goal", "")
            evaluation_previous_goal = item.get("evaluation_previous_goal", "")
            xpath = xpath if xpath is not None else ""
            goal = goal if goal is not None else ""
            if prev_item is not None:
                if 'Success' in evaluation_previous_goal:
                    prev_item["evaluation"] = "Success"
                    prev_item["result"] = evaluation_previous_goal.strip().replace("Success - ", "").strip()
                elif 'Failed' in evaluation_previous_goal:
                    prev_item["evaluation"] = "Failed"
                    prev_item["result"] = evaluation_previous_goal.strip().replace("Failed - ", "").strip()
                else:
                    prev_item["evaluation"] = "Unknown"
                    prev_item["result"] = evaluation_previous_goal.strip()
                test_report_data.append(prev_item)
            prev_item = {"Action": str(item["action"].__class__.__name__).removesuffix("Action"), "Goal": goal}
            parsed_data.append({"Action": str(item["action"].__class__.__name__).removesuffix("Action"), "Goal": goal, "XPath": xpath, "Value": value})
        except Exception as e:
            pass
    status = False
    if isinstance(final_result, str):
        final_result_summary = final_result
    elif hasattr(final_result, "extracted_content"):
        final_result_summary = str(final_result.extracted_content)
    else:
        final_result_summary = ""
    if prev_item is not None:
        if hasattr(final_result, "is_done") and final_result.is_done:
            status = final_result.success if hasattr(final_result, "success") else False
            if status:
                prev_item["evaluation"] = "Success"
                prev_item["result"] = "Task completed successfully"
            else:
                prev_item["evaluation"] = "Failed"
                prev_item["result"] = "Task execution was not successful"
        else:
            prev_item["evaluation"] = "Failed"
            prev_item["result"] = "Task execution was not successful"
            final_result_summary = "The test case execution failed due to a disruption in the intended procedural flow, resulting in a deviation from the expected execution path as defined in the test case design"
        test_report_data.append(prev_item)

    current_time = datetime.datetime.now()
    test_report = {
        "action_list": test_report_data, 
        "task": task_text, 
        "execution_time": str(exec_time) + "seconds", 
        "final_result_summary": final_result_summary, 
        "status": status, 
        "current_time" : current_time.strftime("%m/%d/%Y, %I:%M:%S %p").lstrip("0").replace("/0", "/")
    }
    try:
        pdf_file_path = os.path.join(config.get_output_dir(), f"tc_report_{current_time.strftime('%Y-%m-%d_%H-%M-%S')}.pdf")
        update_html_report(test_report, pdf_file_path)
        open_pdf(pdf_file_path)
    except Exception as e:
        print(f"Error:{e}")
    if parsed_data:
        print("Data Parsed.")
        return pd.DataFrame(parsed_data)
    else:
        print(parsed_data)
        print(result)
        return empty_df
    
def update_html_report(report_data, pdf_file_path="./outputs/test_case_report.pdf", input_path="./assets/html/test_report.html", output_path=os.path.join(config.get_output_dir(), "updated_test_report.html")):
    with sync_playwright() as p:
        try:
            CHROME_PATH = "C:/Program Files/Google/Chrome/Application/chrome.exe" 
            browser = p.chromium.launch(executable_path=CHROME_PATH, headless=True)
            print("Headless Chrome Loaded...")
        except Exception as e:
            print(f"Error:{e}")
            print("Using Existing Chrome Browser...")
            close_chrome()
            launch_chrome()
            browser = p.chromium.connect_over_cdp("http://localhost:9222")
        page = browser.new_page()

        with open(input_path, "r", encoding="utf-8") as f:
            content = f.read()

        page.set_content(content)

        try:
            page.evaluate('document.querySelector(".container p:nth-child(2)").innerHTML = "<strong>Task:</strong>' + str(report_data["task"]).replace('\t', ' ').replace('\n', '<br>') +'";') # str(report_data["task"]).split(":")[0]
        except Exception as e:
            pass
        try:
            page.evaluate(f'''
    const statusElement = document.querySelector(".summary p:nth-child(1) span");
    statusElement.innerHTML = "{'Success' if report_data["status"] else 'Failed'}";
    statusElement.className = "{'status-success' if report_data["status"] else 'status-fail'}";
''')
        except Exception as e:
            pass
        try:
            page.evaluate(f'document.querySelector(".summary p:nth-child(2)").innerHTML = "<strong>Execution Time:</strong> {report_data["execution_time"]}";')
        except Exception as e:
            pass
        try:
            page.evaluate(f'document.querySelector(".summary p:nth-child(3)").innerHTML = "<strong>Final Result:</strong> {report_data["final_result_summary"]}";')
        except Exception as e:
            try:
                print(report_data["final_result_summary"])
            except Exception as e:
                pass

        page.evaluate("document.querySelector('tbody').innerHTML = '';")

        rows_html = ""
        for i, action in enumerate(report_data["action_list"], start=1):
            if action["Action"] == "ClickElement" and action["evaluation"] != "Success" or action["evaluation"] == "Unknown":
                continue
            rows_html += f"""
            <tr>
                <td>{i}</td>
                <td>{action['Action']}</td>
                <td>{action['Goal']}</td>
                <td class="{'status-success' if action['evaluation'] == 'Success' else 'status-fail'}">{action['evaluation']}</td>
                <td>{action['result']}</td>
            </tr>
            """

        try:
            page.evaluate(f"document.querySelector('tbody').innerHTML = `{rows_html}`;")
        except Exception as e:
            pass

        try:
            page.evaluate(f'document.querySelector(".footer p strong").innerText = "{report_data["current_time"]}";')
        except Exception as e:
            pass

        updated_content = page.content()

        page.pdf(path=pdf_file_path)

        with open(output_path, "w", encoding="utf-8") as f:
            f.write(updated_content)

        # static_folder = "static"
        # if not os.path.exists(static_folder):
        #     os.makedirs(static_folder)
        
        # # Example HTML content (replace this with actual content)
        # file_path = os.path.join(static_folder, "updated_test_report.html")
        # with open(file_path, 'w') as f:
        #     f.write(updated_content)

        browser.close()