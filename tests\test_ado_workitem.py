"""
Test suite for ado_workitem.py module.
Tests Azure DevOps work item management functionality.
"""

import pytest
import unittest
from unittest.mock import Mock, patch, MagicMock
import os
import sys
import json
import requests

# Add root directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the module to test
try:
    from ado_workitem import ADOWorkItemManager
except ImportError:
    # Create a mock class if the module doesn't exist
    class ADOWorkItemManager:
        def __init__(self, organization_url, project_name, personal_access_token=None):
            self.organization_url = organization_url
            self.project_name = project_name
            self.personal_access_token = personal_access_token


class TestADOWorkItemManager(unittest.TestCase):
    """Test cases for ADOWorkItemManager class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.organization_url = "https://dev.azure.com/testorg"
        self.project_name = "TestProject"
        self.personal_access_token = "test_token"
        
        self.manager = ADOWorkItemManager(
            organization_url=self.organization_url,
            project_name=self.project_name,
            personal_access_token=self.personal_access_token
        )
        
        # Mock work item response
        self.mock_work_item_response = {
            "id": 12345,
            "rev": 1,
            "fields": {
                "System.Id": 12345,
                "System.Title": "Test Work Item",
                "System.Description": "Test Description",
                "System.WorkItemType": "Task",
                "System.State": "New",
                "System.CreatedDate": "2025-07-11T10:00:00Z",
                "System.CreatedBy": {
                    "displayName": "Test User",
                    "uniqueName": "<EMAIL>"
                }
            },
            "_links": {
                "self": {
                    "href": f"{self.organization_url}/{self.project_name}/_apis/wit/workItems/12345"
                }
            }
        }
    
    def test_manager_initialization(self):
        """Test ADOWorkItemManager initialization"""
        self.assertEqual(self.manager.organization_url, self.organization_url)
        self.assertEqual(self.manager.project_name, self.project_name)
        self.assertEqual(self.manager.personal_access_token, self.personal_access_token)
    
    @patch('requests.post')
    def test_create_work_item_success(self, mock_post):
        """Test successful work item creation"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = self.mock_work_item_response
        mock_post.return_value = mock_response
        
        # Skip if method doesn't exist
        if not hasattr(self.manager, 'create_work_item'):
            self.skipTest("create_work_item method not implemented")
        
        result = self.manager.create_work_item(
            work_item_type="Task",
            title="Test Task",
            description="Test Description"
        )
        
        self.assertEqual(result["id"], 12345)
        self.assertEqual(result["fields"]["System.Title"], "Test Work Item")
        mock_post.assert_called_once()
    
    @patch('requests.post')
    def test_create_work_item_failure(self, mock_post):
        """Test work item creation failure"""
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.text = "Bad Request"
        mock_post.return_value = mock_response
        
        # Skip if method doesn't exist
        if not hasattr(self.manager, 'create_work_item'):
            self.skipTest("create_work_item method not implemented")
        
        with self.assertRaises(Exception):
            self.manager.create_work_item(
                work_item_type="Task",
                title="Test Task"
            )
    
    @patch('requests.get')
    def test_get_work_item_success(self, mock_get):
        """Test successful work item retrieval"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = self.mock_work_item_response
        mock_get.return_value = mock_response
        
        # Skip if method doesn't exist
        if not hasattr(self.manager, 'get_work_item'):
            self.skipTest("get_work_item method not implemented")
        
        result = self.manager.get_work_item(12345)
        
        self.assertEqual(result["id"], 12345)
        self.assertEqual(result["fields"]["System.Title"], "Test Work Item")
        mock_get.assert_called_once()
    
    @patch('requests.get')
    def test_get_work_item_not_found(self, mock_get):
        """Test work item not found"""
        mock_response = Mock()
        mock_response.status_code = 404
        mock_response.text = "Work item not found"
        mock_get.return_value = mock_response
        
        # Skip if method doesn't exist
        if not hasattr(self.manager, 'get_work_item'):
            self.skipTest("get_work_item method not implemented")
        
        result = self.manager.get_work_item(99999)
        self.assertIsNone(result)
    
    @patch('requests.patch')
    def test_update_work_item_success(self, mock_patch):
        """Test successful work item update"""
        updated_response = self.mock_work_item_response.copy()
        updated_response["fields"]["System.State"] = "In Progress"
        
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = updated_response
        mock_patch.return_value = mock_response
        
        # Skip if method doesn't exist
        if not hasattr(self.manager, 'update_work_item'):
            self.skipTest("update_work_item method not implemented")
        
        fields_to_update = {"System.State": "In Progress"}
        result = self.manager.update_work_item(12345, fields_to_update)
        
        self.assertEqual(result["fields"]["System.State"], "In Progress")
        mock_patch.assert_called_once()
    
    @patch('requests.delete')
    def test_delete_work_item_success(self, mock_delete):
        """Test successful work item deletion"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_delete.return_value = mock_response
        
        # Skip if method doesn't exist
        if not hasattr(self.manager, 'delete_work_item'):
            self.skipTest("delete_work_item method not implemented")
        
        result = self.manager.delete_work_item(12345)
        self.assertTrue(result)
        mock_delete.assert_called_once()
    
    @patch('requests.delete')
    def test_delete_work_item_failure(self, mock_delete):
        """Test work item deletion failure"""
        mock_response = Mock()
        mock_response.status_code = 404
        mock_response.text = "Work item not found"
        mock_delete.return_value = mock_response
        
        # Skip if method doesn't exist
        if not hasattr(self.manager, 'delete_work_item'):
            self.skipTest("delete_work_item method not implemented")
        
        with self.assertRaises(Exception):
            self.manager.delete_work_item(99999)
    
    @patch('requests.post')
    def test_execute_wiql_query_success(self, mock_post):
        """Test successful WIQL query execution"""
        mock_query_response = {
            "queryType": "flat",
            "queryResultType": "workItem",
            "workItems": [
                {"id": 12345, "url": f"{self.organization_url}/_apis/wit/workItems/12345"},
                {"id": 12346, "url": f"{self.organization_url}/_apis/wit/workItems/12346"}
            ]
        }
        
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = mock_query_response
        mock_post.return_value = mock_response
        
        # Skip if method doesn't exist
        if not hasattr(self.manager, 'execute_wiql_query'):
            self.skipTest("execute_wiql_query method not implemented")
        
        query = "SELECT [System.Id] FROM WorkItems WHERE [System.WorkItemType] = 'Task'"
        result = self.manager.execute_wiql_query(query)
        
        self.assertEqual(len(result["workItems"]), 2)
        self.assertEqual(result["workItems"][0]["id"], 12345)
        mock_post.assert_called_once()
    
    @patch('requests.post')
    def test_execute_wiql_query_failure(self, mock_post):
        """Test WIQL query execution failure"""
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.text = "Invalid query"
        mock_post.return_value = mock_response
        
        # Skip if method doesn't exist
        if not hasattr(self.manager, 'execute_wiql_query'):
            self.skipTest("execute_wiql_query method not implemented")
        
        with self.assertRaises(Exception):
            self.manager.execute_wiql_query("INVALID QUERY")
    
    def test_build_api_url(self):
        """Test API URL building"""
        # Skip if method doesn't exist
        if not hasattr(self.manager, '_build_api_url'):
            self.skipTest("_build_api_url method not implemented")
        
        expected_base_url = f"{self.organization_url}/{self.project_name}/_apis/wit"
        
        # Test work items URL
        work_items_url = self.manager._build_api_url("workitems")
        self.assertTrue(work_items_url.startswith(expected_base_url))
        
        # Test specific work item URL
        work_item_url = self.manager._build_api_url("workitems/12345")
        self.assertIn("12345", work_item_url)
    
    def test_build_headers(self):
        """Test HTTP headers building"""
        # Skip if method doesn't exist
        if not hasattr(self.manager, '_build_headers'):
            self.skipTest("_build_headers method not implemented")
        
        headers = self.manager._build_headers()
        
        self.assertIn("Authorization", headers)
        self.assertIn("Content-Type", headers)
        self.assertEqual(headers["Content-Type"], "application/json-patch+json")
    
    def test_authentication_setup(self):
        """Test authentication setup"""
        # Test with personal access token
        manager_with_token = ADOWorkItemManager(
            organization_url=self.organization_url,
            project_name=self.project_name,
            personal_access_token="test_token"
        )
        
        self.assertEqual(manager_with_token.personal_access_token, "test_token")
        
        # Test without personal access token (should use environment variable)
        with patch.dict(os.environ, {'AZURE_DEVOPS_PAT': 'env_token'}):
            manager_env_token = ADOWorkItemManager(
                organization_url=self.organization_url,
                project_name=self.project_name
            )
            
            # Implementation would depend on actual code
            self.assertIsNotNone(manager_env_token)


class TestADOWorkItemManagerIntegration(unittest.TestCase):
    """Integration tests for ADOWorkItemManager"""
    
    def setUp(self):
        """Set up integration test fixtures"""
        self.manager = ADOWorkItemManager(
            organization_url="https://dev.azure.com/testorg",
            project_name="TestProject",
            personal_access_token="test_token"
        )
    
    @patch('requests.post')
    @patch('requests.get')
    @patch('requests.patch')
    @patch('requests.delete')
    def test_work_item_lifecycle_integration(self, mock_delete, mock_patch, mock_get, mock_post):
        """Test complete work item lifecycle integration"""
        work_item_id = 12345
        
        # Mock create response
        create_response = Mock()
        create_response.status_code = 200
        create_response.json.return_value = {
            "id": work_item_id,
            "fields": {"System.Title": "Integration Test Task"}
        }
        mock_post.return_value = create_response
        
        # Mock get response
        get_response = Mock()
        get_response.status_code = 200
        get_response.json.return_value = {
            "id": work_item_id,
            "fields": {"System.Title": "Integration Test Task", "System.State": "New"}
        }
        mock_get.return_value = get_response
        
        # Mock update response
        update_response = Mock()
        update_response.status_code = 200
        update_response.json.return_value = {
            "id": work_item_id,
            "fields": {"System.Title": "Integration Test Task", "System.State": "In Progress"}
        }
        mock_patch.return_value = update_response
        
        # Mock delete response
        delete_response = Mock()
        delete_response.status_code = 200
        mock_delete.return_value = delete_response
        
        # Skip if methods don't exist
        required_methods = ['create_work_item', 'get_work_item', 'update_work_item', 'delete_work_item']
        for method in required_methods:
            if not hasattr(self.manager, method):
                self.skipTest(f"{method} method not implemented")
        
        # Test lifecycle
        # 1. Create
        created_item = self.manager.create_work_item("Task", "Integration Test Task")
        self.assertEqual(created_item["id"], work_item_id)
        
        # 2. Get
        retrieved_item = self.manager.get_work_item(work_item_id)
        self.assertEqual(retrieved_item["id"], work_item_id)
        
        # 3. Update
        updated_item = self.manager.update_work_item(work_item_id, {"System.State": "In Progress"})
        self.assertEqual(updated_item["fields"]["System.State"], "In Progress")
        
        # 4. Delete
        delete_result = self.manager.delete_work_item(work_item_id)
        self.assertTrue(delete_result)


class TestADOWorkItemManagerErrorHandling(unittest.TestCase):
    """Test error handling in ADOWorkItemManager"""
    
    def setUp(self):
        """Set up error handling test fixtures"""
        self.manager = ADOWorkItemManager(
            organization_url="https://dev.azure.com/testorg",
            project_name="TestProject",
            personal_access_token="test_token"
        )
    
    @patch('requests.post')
    def test_network_error_handling(self, mock_post):
        """Test handling of network errors"""
        mock_post.side_effect = requests.exceptions.ConnectionError("Network error")
        
        # Skip if method doesn't exist
        if not hasattr(self.manager, 'create_work_item'):
            self.skipTest("create_work_item method not implemented")
        
        with self.assertRaises((requests.exceptions.ConnectionError, Exception)):
            self.manager.create_work_item("Task", "Test Task")
    
    @patch('requests.post')
    def test_timeout_error_handling(self, mock_post):
        """Test handling of timeout errors"""
        mock_post.side_effect = requests.exceptions.Timeout("Request timeout")
        
        # Skip if method doesn't exist
        if not hasattr(self.manager, 'create_work_item'):
            self.skipTest("create_work_item method not implemented")
        
        with self.assertRaises((requests.exceptions.Timeout, Exception)):
            self.manager.create_work_item("Task", "Test Task")
    
    @patch('requests.post')
    def test_authentication_error_handling(self, mock_post):
        """Test handling of authentication errors"""
        mock_response = Mock()
        mock_response.status_code = 401
        mock_response.text = "Unauthorized"
        mock_post.return_value = mock_response
        
        # Skip if method doesn't exist
        if not hasattr(self.manager, 'create_work_item'):
            self.skipTest("create_work_item method not implemented")
        
        with self.assertRaises(Exception):
            self.manager.create_work_item("Task", "Test Task")


if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestADOWorkItemManager,
        TestADOWorkItemManagerIntegration,
        TestADOWorkItemManagerErrorHandling
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Exit with appropriate code
    sys.exit(0 if result.wasSuccessful() else 1)
