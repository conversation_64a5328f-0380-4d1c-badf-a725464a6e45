# Xbox Cloud Gaming Automation Guide

Comprehensive guide for automating Xbox Cloud Gaming using VizCheck, covering gaming-specific features, configurations, and best practices.

## Table of Contents

1. [Overview](#overview)
2. [Gaming Features](#gaming-features)
3. [Supported Countries](#supported-countries)
4. [Controller Support](#controller-support)
5. [Gaming Actions](#gaming-actions)
6. [Configuration](#configuration)
7. [Common Gaming Tasks](#common-gaming-tasks)
8. [Best Practices](#best-practices)
9. [Troubleshooting](#troubleshooting)

## Overview

VizCheck provides specialized automation capabilities for Xbox Cloud Gaming, enabling AI-driven game testing, gameplay automation, and quality assurance for cloud gaming experiences.

### Key Capabilities
- **Multi-region Support**: Automated testing across 35+ countries
- **Dual Input Methods**: Xbox controller and keyboard simulation
- **Game State Management**: Intelligent game navigation and control
- **Extended Gameplay**: Automated 10-minute gameplay sessions
- **Cross-platform Testing**: Windows, browser-based gaming

## Gaming Features

### Xbox Controller Simulation
VizCheck includes a virtual Xbox controller that can simulate all standard Xbox gamepad inputs:

- **Analog Sticks**: Left and right thumbstick movement
- **D-Pad**: Directional pad controls
- **Face Buttons**: A, B, X, Y button presses
- **Shoulder Buttons**: Left/Right bumpers and triggers
- **System Buttons**: Start, Back, Guide buttons

### Keyboard Gaming Controls
Alternative keyboard-based gaming controls for games that support keyboard input:

- **Movement**: WASD or arrow key movement
- **Mouse Look**: Mouse movement for camera control
- **Action Keys**: Customizable key bindings

### Intelligent Game Detection
The AI agent can:
- Detect game launch screens
- Navigate game menus automatically
- Identify gameplay states
- Handle loading screens and transitions

## Supported Countries

VizCheck supports Xbox Cloud Gaming automation across 35 countries with localized URLs:

| Country        | Locale Code | Xbox URL                              |
|----------------|-------------|---------------------------------------|
| United States  | en-US       | https://www.xbox.com/en-US/play       |
| United Kingdom | en-GB       | https://www.xbox.com/en-GB/play       |
| Germany        | de-DE       | https://www.xbox.com/de-DE/play       |
| France         | fr-FR       | https://www.xbox.com/fr-FR/play       |
| Japan          | ja-JP       | https://www.xbox.com/ja-JP/play       |
| Australia      | en-AU       | https://www.xbox.com/en-AU/play       |
| Canada         | en-CA       | https://www.xbox.com/en-CA/play       |
| Brazil         | pt-BR       | https://www.xbox.com/pt-BR/play       |
| Mexico         | es-MX       | https://www.xbox.com/es-MX/play       |
| Spain          | es-ES       | https://www.xbox.com/es-ES/play       |

*See [constants.py](../constants.py) for the complete list of supported countries.*

### Country-Specific URLs

The system automatically provides region-specific URLs for different Xbox sections:

```python
xbox_urls = {
    "Home Page": f"https://www.xbox.com/{country_code}/play",
    "User Profile": f"https://www.xbox.com/{country_code}/play/user",
    "Gallery": f"https://www.xbox.com/{country_code}/play/gallery/all-games",
    "All Games": f"https://www.xbox.com/{country_code}/play/gallery/all-games",
    "Indies": f"https://www.xbox.com/{country_code}/play/gallery/indies",
    "Strategies": f"https://www.xbox.com/{country_code}/play/gallery/strategies",
    "Shooters": f"https://www.xbox.com/{country_code}/play/gallery/shooters",
    "Simulations": f"https://www.xbox.com/{country_code}/play/gallery/simulations"
}
```

## Controller Support

### Xbox Controller Actions

VizCheck provides comprehensive Xbox controller simulation through the `XboxGameControlAction` class:

#### Available Actions
- **Movement**: `forward`, `backward`, `left`, `right`
- **Camera**: `view_left`, `view_right`
- **Face Buttons**: `a`, `b`, `x`, `y`
- **Extended Play**: `is_play_ten_min` for 10-minute gameplay sessions

#### Button Mapping
```python
BUTTON_MAP = {
    "a": XUSB_BUTTON.XUSB_GAMEPAD_A,
    "b": XUSB_BUTTON.XUSB_GAMEPAD_B,
    "x": XUSB_BUTTON.XUSB_GAMEPAD_X,
    "y": XUSB_BUTTON.XUSB_GAMEPAD_Y,
    "back": XUSB_BUTTON.XUSB_GAMEPAD_BACK,
    "start": XUSB_BUTTON.XUSB_GAMEPAD_START,
    "guide": XUSB_BUTTON.XUSB_GAMEPAD_GUIDE,
    "left shoulder": XUSB_BUTTON.XUSB_GAMEPAD_LEFT_SHOULDER,
    "right shoulder": XUSB_BUTTON.XUSB_GAMEPAD_RIGHT_SHOULDER,
    "left thumb": XUSB_BUTTON.XUSB_GAMEPAD_LEFT_THUMB,
    "right thumb": XUSB_BUTTON.XUSB_GAMEPAD_RIGHT_THUMB
}
```

### Keyboard Gaming Controls

For games supporting keyboard input, VizCheck provides `KeyGameControlAction`:

#### Available Actions
- **Movement**: `forward`, `backward`, `left`, `right`
- **Mouse Look**: `mouse_move_left`, `mouse_move_right`
- **Extended Play**: `is_play_ten_min` for 10-minute gameplay sessions

## Gaming Actions

### Controller Connection
```python
# Connect to Xbox controller
await connect_xbox_controller(ConnectXboxController(msg="Controller connected"))
```

### Basic Movement
```python
# Xbox controller movement
await in_game_action_seq_xbox(XboxGameControlAction(
    is_play_ten_min=False,
    actions=["forward", "right", "a"]
))

# Keyboard movement
await in_game_action_seq_keyboard(KeyGameControlAction(
    is_play_ten_min=False,
    actions=["forward", "mouse_move_right"]
))
```

### Extended Gameplay
```python
# 10-minute automated gameplay
await in_game_action_seq_xbox(XboxGameControlAction(
    is_play_ten_min=True,
    actions=["forward", "backward", "left", "right", "a", "b"]
))
```

### Input Method Switching
```python
# Switch from keyboard to Xbox controller
await in_game_action_seq_change_xbox(XboxGameControlAction(
    is_play_ten_min=False,
    actions=["forward", "a"]
))

# Switch from Xbox controller to keyboard
await in_game_action_seq_change_keyboard(KeyGameControlAction(
    is_play_ten_min=False,
    actions=["forward", "mouse_move_left"]
))
```

## Configuration

### Gaming-Specific Configuration

#### config.ini Settings
```ini
[settings]
start_url = https://www.xbox.com/en-US/play
max_num_of_steps = 30
output_dir = ./gaming_outputs
```

#### Browser Configuration
```python
chrome_config = ChromeConfig(
    headless=False,  # Required for gaming
    disable_security=True,
    window_width=1280,
    window_height=720,
    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
)
```

### Build Configuration

VizCheck includes specialized build configurations for gaming:

#### XboxCloudGaming.spec
- **Purpose**: Xbox Cloud Gaming specific build
- **Executable**: `XboxCloudGaming.exe`
- **Optimizations**: Gaming-focused optimizations
- **Use Case**: Dedicated gaming automation

#### app_no_console.spec
- **Purpose**: Clean gaming interface
- **Executable**: `XboxCloudGaming.exe`
- **Console**: Disabled for cleaner experience
- **Use Case**: Production gaming automation

## Common Gaming Tasks

### Game Launch and Setup
```python
# Natural language task for game launch
task = """
Launch Minecraft from Xbox Cloud Gaming in Germany.
Wait for the game to load completely.
Navigate to single player mode.
Create a new world with default settings.
"""
```

### Gameplay Automation
```python
# Extended gameplay session
task = """
Launch Forza Horizon from Xbox Cloud Gaming.
Start a quick race.
Use Xbox controller to drive for 10 minutes.
Focus on staying on track and avoiding obstacles.
"""
```

### Multi-Game Testing
```python
# Cross-game testing
task = """
Test 3 different games from the Xbox Cloud Gaming library:
1. Launch each game and verify it loads properly
2. Play each game for 2 minutes using controller
3. Return to the main menu and exit cleanly
"""
```

### Regional Testing
```python
# Country-specific testing
task = """
Test Xbox Cloud Gaming in France:
1. Navigate to the French Xbox Cloud Gaming site
2. Browse the game library
3. Launch a popular game
4. Verify French language support
5. Test controller responsiveness
"""
```

## Best Practices

### Performance Optimization
1. **Use Non-Headless Mode**: Gaming requires visual feedback
2. **Optimize Network**: Ensure stable, high-speed internet
3. **Monitor Resources**: Gaming can be resource-intensive
4. **Set Appropriate Timeouts**: Games may have longer load times

### Controller Best Practices
1. **Test Both Input Methods**: Verify keyboard and controller support
2. **Handle Input Switching**: Some games require input method changes
3. **Use Realistic Timing**: Add appropriate delays between actions
4. **Reset Controller State**: Clear controller state between tests

### Regional Testing
1. **Test Multiple Regions**: Verify functionality across countries
2. **Check Localization**: Ensure proper language support
3. **Validate URLs**: Confirm region-specific URLs work correctly
4. **Monitor Performance**: Different regions may have varying performance

### Automation Strategies
1. **Start Simple**: Begin with basic navigation and game launch
2. **Build Complexity**: Gradually add more complex gameplay
3. **Handle Errors**: Implement robust error handling for game states
4. **Document Results**: Capture screenshots and gameplay recordings

## Troubleshooting

### Common Issues

#### Controller Not Responding
```
Issue: Xbox controller inputs not registering in game
Solutions:
1. Verify controller connection: connect_xbox_controller()
2. Check game controller support
3. Try switching input methods
4. Restart browser session
```

#### Game Launch Failures
```
Issue: Games fail to launch or load
Solutions:
1. Check internet connection stability
2. Verify Xbox Game Pass subscription
3. Clear browser cache and cookies
4. Try different browser or incognito mode
```

#### Regional Access Issues
```
Issue: Cannot access games in specific regions
Solutions:
1. Verify country code in constants.py
2. Check Xbox Cloud Gaming availability in region
3. Use VPN if necessary for testing
4. Validate locale-specific URLs
```

#### Performance Issues
```
Issue: Slow gameplay or input lag
Solutions:
1. Check network bandwidth and latency
2. Close unnecessary applications
3. Optimize browser settings
4. Use wired internet connection
```

### Debugging Commands

#### Test Controller Connection
```python
# Test Xbox controller
from browser_use.controller.service import Controller
controller = Controller()
controller.handle_action("tap", "a")  # Test A button
```

#### Verify Country Configuration
```python
# Check available countries
from config import Config
config = Config()
countries = config.get_available_countries()
print(countries)
```

#### Test Gaming URLs
```python
# Verify Xbox URLs for specific country
country_code = "en-US"
xbox_url = f"https://www.xbox.com/{country_code}/play"
print(f"Testing URL: {xbox_url}")
```

### Performance Monitoring

#### Gaming Metrics
- **Input Latency**: Time between command and game response
- **Frame Rate**: Visual smoothness during gameplay
- **Load Times**: Game and level loading performance
- **Network Stability**: Connection quality during gameplay

#### Logging Gaming Events
```python
import logging
logger = logging.getLogger('xbox_gaming')

# Log gaming actions
logger.info(f"Launching game: {game_name}")
logger.info(f"Controller action: {action}")
logger.info(f"Gameplay duration: {duration} minutes")
```

---

**Next Steps**:
- Review [Controller Documentation](Controller_Documentation.md) for detailed action information
- Check [Browser Documentation](Browser_Documentation.md) for browser optimization
- See [Configuration Guide](Configuration_Guide.md) for advanced settings
