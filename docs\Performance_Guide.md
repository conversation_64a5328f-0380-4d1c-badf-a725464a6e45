# VizCheck Performance Optimization Guide

Comprehensive guide for optimizing VizCheck performance, covering strategies, monitoring, and tuning recommendations.

## Table of Contents

1. [Performance Overview](#performance-overview)
2. [Browser Optimization](#browser-optimization)
3. [Agent Configuration](#agent-configuration)
4. [System Resource Management](#system-resource-management)
5. [Network Optimization](#network-optimization)
6. [Memory Management](#memory-management)
7. [Monitoring and Metrics](#monitoring-and-metrics)
8. [Gaming Performance](#gaming-performance)
9. [Scaling Strategies](#scaling-strategies)
10. [Troubleshooting Performance Issues](#troubleshooting-performance-issues)

## Performance Overview

VizCheck performance depends on several key factors:

- **Browser Configuration**: Headless mode, security settings, resource limits
- **Agent Settings**: Token limits, retry policies, timeout values
- **System Resources**: CPU, memory, disk I/O, network bandwidth
- **Task Complexity**: Number of steps, page complexity, interaction types
- **External Services**: Azure OpenAI API response times, target website performance

### Performance Metrics

Key metrics to monitor:
- **Task Execution Time**: Total time to complete automation tasks
- **API Response Time**: Azure OpenAI API call latency
- **Browser Load Time**: Page loading and rendering performance
- **Memory Usage**: RAM consumption during execution
- **CPU Utilization**: Processing load during automation
- **Network Throughput**: Data transfer rates

## Browser Optimization

### Headless Mode Configuration

**Recommended for Production**:
```python
from browser_use.browser.browser import BrowserConfig

# Optimized headless configuration
optimized_config = BrowserConfig(
    headless=True,  # Significantly faster
    disable_security=True,  # Reduces overhead
    window_width=1280,
    window_height=720,
    
    # Timing optimizations
    minimum_wait_page_load_time=0.1,  # Reduce from default 0.25
    wait_for_network_idle_page_load_time=0.3,  # Reduce from default 0.5
    maximum_wait_page_load_time=3,  # Reduce from default 5
    wait_between_actions=0.2,  # Reduce from default 0.5
    
    # Resource optimizations
    disable_images=True,  # Skip image loading
    disable_javascript=False,  # Keep JS for functionality
    disable_css=False,  # Keep CSS for layout
)
```

**Development Configuration**:
```python
# Development configuration with visual feedback
dev_config = BrowserConfig(
    headless=False,  # Visual debugging
    disable_security=True,
    window_width=1280,
    window_height=720,
    
    # Moderate timing for debugging
    minimum_wait_page_load_time=0.25,
    wait_for_network_idle_page_load_time=0.5,
    maximum_wait_page_load_time=5,
    wait_between_actions=0.5,
)
```

### Browser Resource Limits

```python
# Chrome-specific optimizations
chrome_config = BrowserConfig(
    browser_type='chromium',
    browser_args=[
        '--no-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--disable-extensions',
        '--disable-plugins',
        '--disable-images',  # Skip image loading
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--memory-pressure-off',
        '--max_old_space_size=4096',  # Increase V8 memory limit
    ]
)
```

### Page Loading Optimization

```python
# Optimized page loading strategy
browser_config = BrowserConfig(
    # Reduce wait times
    minimum_wait_page_load_time=0.1,
    wait_for_network_idle_page_load_time=0.2,
    maximum_wait_page_load_time=2,
    
    # Skip unnecessary resources
    block_resources=['image', 'media', 'font'],
    
    # Optimize viewport
    no_viewport=True,  # Skip viewport setup
    browser_window_size={'width': 1024, 'height': 768}  # Smaller viewport
)
```

## Agent Configuration

### Token Optimization

```python
from browser_use.agent.views import AgentSettings

# Optimized agent settings
optimized_agent_settings = AgentSettings(
    # Reduce token usage
    max_input_tokens=64000,  # Reduce from default 128000
    use_vision=True,  # Keep for accuracy
    use_vision_for_planner=False,  # Disable to save tokens
    
    # Optimize retry behavior
    max_failures=2,  # Reduce from default 3
    retry_delay=5,  # Reduce from default 10
    
    # Disable expensive features
    validate_output=False,
    generate_gif=False,  # Disable for production
    
    # Optimize DOM attributes
    include_attributes=[
        'title', 'type', 'name', 'role',  # Essential attributes only
        'aria-label', 'placeholder', 'value'
    ]
)
```

### Task Optimization

```python
# Optimize task configuration
config_optimizations = {
    'max_num_of_steps': 20,  # Reduce from default 45
    'max_actions_per_step': 1,  # Keep focused
    'timeout_per_step': 30,  # Reduce timeout
}

# Apply optimizations
from config import Config
config = Config()
# Update config.ini with optimized values
```

### Prompt Optimization

```python
# Optimized task descriptions
def optimize_task_prompt(task):
    """Optimize task prompt for better performance"""
    optimized_prompt = f"""
    {task}
    
    Performance guidelines:
    - Complete the task efficiently with minimal steps
    - Avoid unnecessary navigation or interactions
    - Use direct element targeting when possible
    - Skip optional verification steps unless critical
    """
    return optimized_prompt

# Usage
task = "Login to the website and navigate to dashboard"
optimized_task = optimize_task_prompt(task)
```

## System Resource Management

### Memory Optimization

```python
import gc
import psutil
import os

class MemoryManager:
    def __init__(self, max_memory_mb=2048):
        self.max_memory_mb = max_memory_mb
        self.process = psutil.Process(os.getpid())
    
    def check_memory_usage(self):
        """Check current memory usage"""
        memory_mb = self.process.memory_info().rss / 1024 / 1024
        return memory_mb
    
    def cleanup_if_needed(self):
        """Cleanup memory if usage is high"""
        current_memory = self.check_memory_usage()
        if current_memory > self.max_memory_mb:
            gc.collect()  # Force garbage collection
            return True
        return False
    
    def monitor_memory(self):
        """Monitor memory usage during execution"""
        memory_mb = self.check_memory_usage()
        cpu_percent = self.process.cpu_percent()
        
        return {
            'memory_mb': memory_mb,
            'cpu_percent': cpu_percent,
            'memory_percent': memory_mb / self.max_memory_mb * 100
        }

# Usage
memory_manager = MemoryManager(max_memory_mb=2048)
```

### CPU Optimization

```python
import multiprocessing
import concurrent.futures

# Optimize for CPU-bound tasks
def optimize_cpu_usage():
    """Configure optimal CPU usage"""
    cpu_count = multiprocessing.cpu_count()
    
    # Use 75% of available CPUs
    optimal_workers = max(1, int(cpu_count * 0.75))
    
    return {
        'max_workers': optimal_workers,
        'cpu_count': cpu_count,
        'recommended_parallel_tasks': optimal_workers
    }

# Parallel task execution
def run_parallel_tasks(tasks, max_workers=None):
    """Run multiple tasks in parallel"""
    if max_workers is None:
        max_workers = optimize_cpu_usage()['max_workers']
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = [executor.submit(execute_task, task) for task in tasks]
        results = [future.result() for future in concurrent.futures.as_completed(futures)]
    
    return results
```

### Disk I/O Optimization

```python
import tempfile
import shutil

class DiskOptimizer:
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp(prefix='vizcheck_')
    
    def optimize_output_directory(self):
        """Use temporary directory for outputs"""
        return {
            'output_dir': self.temp_dir,
            'cleanup_on_exit': True
        }
    
    def cleanup_temp_files(self):
        """Clean up temporary files"""
        try:
            shutil.rmtree(self.temp_dir)
        except Exception as e:
            print(f"Cleanup error: {e}")
    
    def optimize_screenshot_storage(self):
        """Optimize screenshot storage"""
        return {
            'screenshot_format': 'jpeg',  # Smaller than PNG
            'screenshot_quality': 80,  # Reduce quality for size
            'max_screenshots': 10,  # Limit screenshot count
        }

# Usage
disk_optimizer = DiskOptimizer()
optimized_config = disk_optimizer.optimize_output_directory()
```

## Network Optimization

### API Request Optimization

```python
import asyncio
import aiohttp
from tenacity import retry, stop_after_attempt, wait_exponential

class APIOptimizer:
    def __init__(self):
        self.session = None
    
    async def create_session(self):
        """Create optimized HTTP session"""
        connector = aiohttp.TCPConnector(
            limit=10,  # Connection pool size
            limit_per_host=5,  # Connections per host
            ttl_dns_cache=300,  # DNS cache TTL
            use_dns_cache=True,
        )
        
        timeout = aiohttp.ClientTimeout(
            total=30,  # Total timeout
            connect=10,  # Connection timeout
            sock_read=10  # Socket read timeout
        )
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout
        )
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def optimized_api_call(self, url, data):
        """Make optimized API call with retry logic"""
        if not self.session:
            await self.create_session()
        
        async with self.session.post(url, json=data) as response:
            return await response.json()
    
    async def close_session(self):
        """Close HTTP session"""
        if self.session:
            await self.session.close()

# Usage
api_optimizer = APIOptimizer()
```

### Bandwidth Optimization

```python
# Optimize network usage
network_config = {
    'compress_requests': True,
    'batch_api_calls': True,
    'cache_responses': True,
    'minimize_screenshots': True,
    'reduce_dom_data': True
}

def optimize_network_usage(agent_config):
    """Apply network optimizations"""
    agent_config.update({
        'use_vision': True,  # Keep for accuracy
        'generate_gif': False,  # Disable to save bandwidth
        'save_conversation_path': None,  # Disable conversation saving
        'include_attributes': ['title', 'type', 'name'],  # Minimal attributes
    })
    return agent_config
```

## Memory Management

### Garbage Collection Optimization

```python
import gc
import weakref

class MemoryOptimizer:
    def __init__(self):
        self.cleanup_threshold = 100  # Cleanup every 100 operations
        self.operation_count = 0
    
    def periodic_cleanup(self):
        """Perform periodic memory cleanup"""
        self.operation_count += 1
        
        if self.operation_count >= self.cleanup_threshold:
            # Force garbage collection
            collected = gc.collect()
            self.operation_count = 0
            
            return {
                'objects_collected': collected,
                'memory_freed': True
            }
        
        return {'memory_freed': False}
    
    def optimize_object_lifecycle(self):
        """Optimize object lifecycle management"""
        # Configure garbage collection
        gc.set_threshold(700, 10, 10)  # Optimize GC thresholds
        
        # Enable automatic cleanup
        gc.enable()
        
        return {
            'gc_enabled': True,
            'thresholds_optimized': True
        }

# Usage
memory_optimizer = MemoryOptimizer()
memory_optimizer.optimize_object_lifecycle()
```

### Browser Memory Management

```python
class BrowserMemoryManager:
    def __init__(self, browser):
        self.browser = browser
        self.max_tabs = 3  # Limit concurrent tabs
        self.cleanup_interval = 10  # Cleanup every 10 operations
    
    async def optimize_browser_memory(self):
        """Optimize browser memory usage"""
        # Close unnecessary tabs
        contexts = await self.browser.get_contexts()
        
        for context in contexts:
            pages = await context.get_pages()
            
            # Keep only essential pages
            if len(pages) > self.max_tabs:
                for page in pages[self.max_tabs:]:
                    await page.close()
        
        # Clear browser cache
        await self.clear_browser_cache()
    
    async def clear_browser_cache(self):
        """Clear browser cache and storage"""
        contexts = await self.browser.get_contexts()
        
        for context in contexts:
            # Clear storage
            await context.clear_cookies()
            await context.clear_permissions()
            
            # Clear cache via CDP
            pages = await context.get_pages()
            for page in pages:
                await page.evaluate('() => { caches.keys().then(names => names.forEach(name => caches.delete(name))); }')

# Usage
browser_memory_manager = BrowserMemoryManager(browser)
await browser_memory_manager.optimize_browser_memory()
```

## Monitoring and Metrics

### Performance Monitoring

```python
import time
import psutil
from dataclasses import dataclass
from typing import Dict, List

@dataclass
class PerformanceMetrics:
    execution_time: float
    memory_usage_mb: float
    cpu_percent: float
    api_calls: int
    api_response_time: float
    page_load_time: float
    actions_completed: int

class PerformanceMonitor:
    def __init__(self):
        self.metrics_history: List[PerformanceMetrics] = []
        self.start_time = None
        self.api_call_count = 0
        self.api_response_times = []
    
    def start_monitoring(self):
        """Start performance monitoring"""
        self.start_time = time.time()
        self.api_call_count = 0
        self.api_response_times = []
    
    def record_api_call(self, response_time: float):
        """Record API call metrics"""
        self.api_call_count += 1
        self.api_response_times.append(response_time)
    
    def get_current_metrics(self) -> PerformanceMetrics:
        """Get current performance metrics"""
        process = psutil.Process()
        
        return PerformanceMetrics(
            execution_time=time.time() - self.start_time if self.start_time else 0,
            memory_usage_mb=process.memory_info().rss / 1024 / 1024,
            cpu_percent=process.cpu_percent(),
            api_calls=self.api_call_count,
            api_response_time=sum(self.api_response_times) / len(self.api_response_times) if self.api_response_times else 0,
            page_load_time=0,  # To be measured separately
            actions_completed=0  # To be tracked by agent
        )
    
    def generate_performance_report(self) -> Dict:
        """Generate performance report"""
        current_metrics = self.get_current_metrics()
        
        return {
            'current_metrics': current_metrics,
            'recommendations': self.get_optimization_recommendations(current_metrics),
            'trends': self.analyze_trends()
        }
    
    def get_optimization_recommendations(self, metrics: PerformanceMetrics) -> List[str]:
        """Get performance optimization recommendations"""
        recommendations = []
        
        if metrics.memory_usage_mb > 1024:
            recommendations.append("Consider reducing memory usage - current: {:.2f}MB".format(metrics.memory_usage_mb))
        
        if metrics.api_response_time > 2.0:
            recommendations.append("API response time is high: {:.2f}s - check network connectivity".format(metrics.api_response_time))
        
        if metrics.cpu_percent > 80:
            recommendations.append("High CPU usage: {:.1f}% - consider reducing parallel tasks".format(metrics.cpu_percent))
        
        if metrics.execution_time > 300:  # 5 minutes
            recommendations.append("Long execution time: {:.2f}s - consider task optimization".format(metrics.execution_time))
        
        return recommendations
    
    def analyze_trends(self) -> Dict:
        """Analyze performance trends"""
        if len(self.metrics_history) < 2:
            return {'trend_analysis': 'Insufficient data for trend analysis'}
        
        recent_metrics = self.metrics_history[-5:]  # Last 5 measurements
        
        avg_execution_time = sum(m.execution_time for m in recent_metrics) / len(recent_metrics)
        avg_memory_usage = sum(m.memory_usage_mb for m in recent_metrics) / len(recent_metrics)
        
        return {
            'avg_execution_time': avg_execution_time,
            'avg_memory_usage': avg_memory_usage,
            'trend_analysis': 'Performance trends calculated'
        }

# Usage
monitor = PerformanceMonitor()
monitor.start_monitoring()

# During execution
monitor.record_api_call(1.5)  # Record 1.5s API response time

# Generate report
report = monitor.generate_performance_report()
print(report)
```

### Real-time Monitoring

```python
import threading
import time

class RealTimeMonitor:
    def __init__(self, update_interval=5):
        self.update_interval = update_interval
        self.monitoring = False
        self.monitor_thread = None
    
    def start_monitoring(self):
        """Start real-time monitoring"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """Stop real-time monitoring"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
    
    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.monitoring:
            metrics = self._collect_metrics()
            self._log_metrics(metrics)
            
            # Check for performance issues
            self._check_performance_alerts(metrics)
            
            time.sleep(self.update_interval)
    
    def _collect_metrics(self):
        """Collect current system metrics"""
        process = psutil.Process()
        
        return {
            'timestamp': time.time(),
            'memory_mb': process.memory_info().rss / 1024 / 1024,
            'cpu_percent': process.cpu_percent(),
            'threads': process.num_threads(),
            'open_files': len(process.open_files())
        }
    
    def _log_metrics(self, metrics):
        """Log metrics to console or file"""
        print(f"[{time.strftime('%H:%M:%S')}] "
              f"Memory: {metrics['memory_mb']:.1f}MB, "
              f"CPU: {metrics['cpu_percent']:.1f}%, "
              f"Threads: {metrics['threads']}")
    
    def _check_performance_alerts(self, metrics):
        """Check for performance alerts"""
        if metrics['memory_mb'] > 2048:
            print(f"⚠️  HIGH MEMORY USAGE: {metrics['memory_mb']:.1f}MB")
        
        if metrics['cpu_percent'] > 90:
            print(f"⚠️  HIGH CPU USAGE: {metrics['cpu_percent']:.1f}%")

# Usage
real_time_monitor = RealTimeMonitor(update_interval=10)
real_time_monitor.start_monitoring()

# Your VizCheck execution here

real_time_monitor.stop_monitoring()
```

## Gaming Performance

### Xbox Cloud Gaming Optimization

```python
# Gaming-specific optimizations
gaming_config = BrowserConfig(
    headless=False,  # Required for gaming
    disable_security=True,
    window_width=1920,  # Full HD for gaming
    window_height=1080,
    
    # Gaming-optimized timing
    minimum_wait_page_load_time=0.5,  # Longer for game loading
    wait_for_network_idle_page_load_time=1.0,
    maximum_wait_page_load_time=10,  # Games take longer to load
    wait_between_actions=0.3,  # Faster for gaming
    
    # Gaming-specific settings
    disable_images=False,  # Keep images for gaming
    disable_javascript=False,  # Required for games
    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
)
```

### Controller Performance

```python
from browser_use.controller.service import Controller

class GamingPerformanceOptimizer:
    def __init__(self, controller: Controller):
        self.controller = controller
        self.action_timing = 0.1  # Faster gaming actions
    
    def optimize_controller_performance(self):
        """Optimize controller for gaming performance"""
        # Reduce action delays
        self.controller.action_delay = self.action_timing
        
        # Pre-initialize gamepad
        self.controller.gamepad.update()
        
        # Optimize button mapping for speed
        return {
            'controller_optimized': True,
            'action_timing': self.action_timing
        }
    
    async def fast_gaming_sequence(self, actions):
        """Execute gaming actions with optimized timing"""
        for action in actions:
            await self.controller.execute_action(action)
            await asyncio.sleep(self.action_timing)  # Minimal delay

# Usage
gaming_optimizer = GamingPerformanceOptimizer(controller)
gaming_optimizer.optimize_controller_performance()
```

## Scaling Strategies

### Horizontal Scaling

```python
import asyncio
from concurrent.futures import ProcessPoolExecutor

class HorizontalScaler:
    def __init__(self, max_processes=4):
        self.max_processes = max_processes
    
    async def scale_task_execution(self, tasks):
        """Scale task execution across multiple processes"""
        
        # Split tasks into chunks
        chunk_size = max(1, len(tasks) // self.max_processes)
        task_chunks = [tasks[i:i + chunk_size] for i in range(0, len(tasks), chunk_size)]
        
        # Execute chunks in parallel processes
        with ProcessPoolExecutor(max_workers=self.max_processes) as executor:
            loop = asyncio.get_event_loop()
            
            futures = [
                loop.run_in_executor(executor, self._execute_task_chunk, chunk)
                for chunk in task_chunks
            ]
            
            results = await asyncio.gather(*futures)
        
        # Flatten results
        return [result for chunk_results in results for result in chunk_results]
    
    def _execute_task_chunk(self, task_chunk):
        """Execute a chunk of tasks in a separate process"""
        results = []
        for task in task_chunk:
            try:
                result = execute_task(task)
                results.append(result)
            except Exception as e:
                results.append({'error': str(e)})
        
        return results

# Usage
scaler = HorizontalScaler(max_processes=4)
results = await scaler.scale_task_execution(task_list)
```

### Load Balancing

```python
import random
from typing import List, Dict

class LoadBalancer:
    def __init__(self, worker_configs: List[Dict]):
        self.workers = worker_configs
        self.worker_loads = {i: 0 for i in range(len(worker_configs))}
    
    def get_optimal_worker(self):
        """Get worker with lowest current load"""
        min_load_worker = min(self.worker_loads.items(), key=lambda x: x[1])
        return min_load_worker[0]
    
    def assign_task(self, task):
        """Assign task to optimal worker"""
        worker_id = self.get_optimal_worker()
        self.worker_loads[worker_id] += 1
        
        return {
            'worker_id': worker_id,
            'worker_config': self.workers[worker_id],
            'task': task
        }
    
    def complete_task(self, worker_id):
        """Mark task as completed for worker"""
        if worker_id in self.worker_loads:
            self.worker_loads[worker_id] = max(0, self.worker_loads[worker_id] - 1)

# Usage
worker_configs = [
    {'headless': True, 'max_steps': 20},
    {'headless': True, 'max_steps': 30},
    {'headless': False, 'max_steps': 15}  # Debug worker
]

load_balancer = LoadBalancer(worker_configs)
assignment = load_balancer.assign_task("Navigate to website")
```

## Troubleshooting Performance Issues

### Performance Profiling

```python
import cProfile
import pstats
from functools import wraps

def profile_performance(func):
    """Decorator to profile function performance"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        profiler = cProfile.Profile()
        profiler.enable()
        
        try:
            result = func(*args, **kwargs)
        finally:
            profiler.disable()
            
            # Generate performance report
            stats = pstats.Stats(profiler)
            stats.sort_stats('cumulative')
            stats.print_stats(20)  # Top 20 functions
        
        return result
    
    return wrapper

# Usage
@profile_performance
def execute_optimized_task(task):
    return execute_task(task)

# Profile task execution
result = execute_optimized_task("Test task")
```

### Memory Leak Detection

```python
import tracemalloc
import gc

class MemoryLeakDetector:
    def __init__(self):
        self.snapshots = []
    
    def start_tracing(self):
        """Start memory tracing"""
        tracemalloc.start()
        self.take_snapshot("start")
    
    def take_snapshot(self, label):
        """Take memory snapshot"""
        snapshot = tracemalloc.take_snapshot()
        self.snapshots.append((label, snapshot))
    
    def analyze_memory_growth(self):
        """Analyze memory growth between snapshots"""
        if len(self.snapshots) < 2:
            return "Need at least 2 snapshots for analysis"
        
        start_label, start_snapshot = self.snapshots[0]
        end_label, end_snapshot = self.snapshots[-1]
        
        top_stats = end_snapshot.compare_to(start_snapshot, 'lineno')
        
        print(f"Memory growth from {start_label} to {end_label}:")
        for stat in top_stats[:10]:
            print(stat)
    
    def stop_tracing(self):
        """Stop memory tracing"""
        tracemalloc.stop()

# Usage
leak_detector = MemoryLeakDetector()
leak_detector.start_tracing()

# Execute tasks
execute_task("Task 1")
leak_detector.take_snapshot("after_task_1")

execute_task("Task 2")
leak_detector.take_snapshot("after_task_2")

# Analyze memory growth
leak_detector.analyze_memory_growth()
leak_detector.stop_tracing()
```

---

**Related Documentation**:
- [Configuration Guide](Configuration_Guide.md) - Optimize configuration settings
- [Troubleshooting Guide](Troubleshooting.md) - Resolve performance issues
- [Deployment Guide](Deployment_Guide.md) - Production optimization strategies
