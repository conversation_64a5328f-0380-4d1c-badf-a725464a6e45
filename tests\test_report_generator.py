"""
Test suite for report_generator.py module.
Tests report generation, PDF creation, and HTML processing functionality.
"""

import pytest
import unittest
from unittest.mock import Mock, patch, MagicMock, mock_open
import os
import sys
import tempfile
import shutil
import pandas as pd
from datetime import datetime

# Add root directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import report_generator


class TestReportGenerator(unittest.TestCase):
    """Test cases for report_generator.py module"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.mock_config = Mock()
        self.mock_config.get_output_dir.return_value = self.temp_dir
        
        # Mock result structure
        self.mock_result = Mock()
        self.mock_result.history = []
        
        # Create mock history entry
        mock_history = Mock()
        mock_history.model_output = Mock()
        mock_history.model_output.current_state = Mock()
        mock_history.model_output.current_state.next_goal = "Test goal"
        mock_history.model_output.current_state.evaluation_previous_goal = "Previous goal evaluation"
        mock_history.result = [Mock()]
        mock_history.result[0].extracted_content = "Test result"
        
        # Mock action with XPath
        mock_action = Mock()
        mock_action.click_element = Mock()
        mock_action.click_element.xpath = "//button[@id='test']"
        mock_history.model_output.action = mock_action
        
        self.mock_result.history = [mock_history]
    
    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @patch('report_generator.config')
    def test_parse_result_with_valid_data(self, mock_config_module):
        """Test parsing result with valid data structure"""
        mock_config_module.return_value = self.mock_config
        
        task_text = "Test automation task"
        exec_time = 30
        
        with patch('report_generator.parse_and_display_result') as mock_parse_display:
            mock_parse_display.return_value = pd.DataFrame({
                'Action': ['click_element'],
                'Goal': ['Test goal'],
                'XPath': ['//button[@id="test"]'],
                'Value': ['']
            })
            
            result = report_generator.parse_result(self.mock_result, task_text, exec_time)
            
            self.assertIsInstance(result, pd.DataFrame)
            mock_parse_display.assert_called_once()
    
    @patch('report_generator.config')
    def test_parse_result_with_empty_history(self, mock_config_module):
        """Test parsing result with empty history"""
        mock_config_module.return_value = self.mock_config
        
        empty_result = Mock()
        empty_result.history = []
        
        task_text = "Test task"
        exec_time = 10
        
        with patch('report_generator.parse_and_display_result') as mock_parse_display:
            mock_parse_display.return_value = pd.DataFrame()
            
            result = report_generator.parse_result(empty_result, task_text, exec_time)
            
            mock_parse_display.assert_called_once()
    
    @patch('report_generator.config')
    def test_parse_result_exception_handling(self, mock_config_module):
        """Test parse_result handles exceptions gracefully"""
        mock_config_module.return_value = self.mock_config
        
        # Create result that will cause exception
        broken_result = Mock()
        broken_result.history = [Mock()]
        broken_result.history[0].model_output = None  # This will cause AttributeError
        
        task_text = "Test task"
        exec_time = 10
        
        with patch('report_generator.parse_and_display_result') as mock_parse_display:
            mock_parse_display.return_value = pd.DataFrame()
            
            # Should not raise exception
            result = report_generator.parse_result(broken_result, task_text, exec_time)
            
            mock_parse_display.assert_called_once()
    
    @patch('report_generator.update_html_report')
    @patch('report_generator.open_pdf')
    @patch('report_generator.config')
    def test_parse_and_display_result_success(self, mock_config_module, mock_open_pdf, mock_update_html):
        """Test successful parse and display result"""
        mock_config_module.return_value = self.mock_config
        
        action_list = [
            {
                'action': 'click_element',
                'xpath': '//button[@id="test"]',
                'goal': 'Click test button',
                'evaluation_previous_goal': 'Successfully navigated'
            }
        ]
        
        task_text = "Test automation"
        exec_time = 25
        final_result = "Task completed successfully"
        
        result = report_generator.parse_and_display_result(
            action_list, task_text, exec_time, final_result
        )
        
        self.assertIsInstance(result, pd.DataFrame)
        self.assertEqual(len(result), 1)
        self.assertEqual(result.iloc[0]['Action'], 'click_element')
        
        mock_update_html.assert_called_once()
        mock_open_pdf.assert_called_once()
    
    @patch('report_generator.update_html_report')
    @patch('report_generator.open_pdf')
    @patch('report_generator.config')
    def test_parse_and_display_result_with_exception(self, mock_config_module, mock_open_pdf, mock_update_html):
        """Test parse and display result when PDF generation fails"""
        mock_config_module.return_value = self.mock_config
        mock_update_html.side_effect = Exception("PDF generation failed")
        
        action_list = []
        task_text = "Test task"
        exec_time = 10
        final_result = "Test result"
        
        with patch('builtins.print') as mock_print:
            result = report_generator.parse_and_display_result(
                action_list, task_text, exec_time, final_result
            )
            
            # Should handle exception gracefully
            mock_print.assert_called()
    
    @patch('playwright.sync_api.sync_playwright')
    @patch('builtins.open', new_callable=mock_open, read_data='<html><body>Test</body></html>')
    @patch('report_generator.close_chrome')
    @patch('report_generator.launch_chrome')
    @patch('report_generator.config')
    def test_update_html_report_with_headless_chrome(self, mock_config_module, mock_launch, 
                                                   mock_close, mock_file, mock_playwright):
        """Test HTML report update with headless Chrome"""
        mock_config_module.return_value = self.mock_config
        
        # Mock playwright
        mock_p = Mock()
        mock_browser = Mock()
        mock_page = Mock()
        
        mock_p.chromium.launch.return_value = mock_browser
        mock_browser.new_page.return_value = mock_page
        mock_page.content.return_value = '<html>Updated content</html>'
        
        mock_playwright.return_value.__enter__.return_value = mock_p
        
        report_data = {
            'action_list': [],
            'task': 'Test task',
            'execution_time': '10seconds',
            'final_result_summary': 'Success',
            'status': 'PASSED',
            'current_time': '07/11/2025, 2:30:45 PM'
        }
        
        pdf_path = os.path.join(self.temp_dir, 'test_report.pdf')
        input_path = os.path.join(self.temp_dir, 'test_template.html')
        output_path = os.path.join(self.temp_dir, 'output.html')
        
        report_generator.update_html_report(report_data, pdf_path, input_path, output_path)
        
        mock_p.chromium.launch.assert_called_once()
        mock_page.set_content.assert_called_once()
        mock_page.pdf.assert_called_once_with(path=pdf_path)
    
    @patch('playwright.sync_api.sync_playwright')
    @patch('builtins.open', new_callable=mock_open, read_data='<html><body>Test</body></html>')
    @patch('report_generator.close_chrome')
    @patch('report_generator.launch_chrome')
    @patch('report_generator.config')
    def test_update_html_report_fallback_to_cdp(self, mock_config_module, mock_launch, 
                                              mock_close, mock_file, mock_playwright):
        """Test HTML report update fallback to CDP connection"""
        mock_config_module.return_value = self.mock_config
        
        # Mock playwright to fail on launch, succeed on connect
        mock_p = Mock()
        mock_p.chromium.launch.side_effect = Exception("Launch failed")
        
        mock_browser = Mock()
        mock_page = Mock()
        mock_p.chromium.connect_over_cdp.return_value = mock_browser
        mock_browser.new_page.return_value = mock_page
        mock_page.content.return_value = '<html>Updated content</html>'
        
        mock_playwright.return_value.__enter__.return_value = mock_p
        
        report_data = {
            'action_list': [],
            'task': 'Test task',
            'execution_time': '10seconds',
            'final_result_summary': 'Success',
            'status': 'PASSED',
            'current_time': '07/11/2025, 2:30:45 PM'
        }
        
        pdf_path = os.path.join(self.temp_dir, 'test_report.pdf')
        
        report_generator.update_html_report(report_data, pdf_path)
        
        # Should fallback to CDP connection
        mock_close.assert_called_once()
        mock_launch.assert_called_once()
        mock_p.chromium.connect_over_cdp.assert_called_once_with("http://localhost:9222")
    
    def test_extract_action_from_model_output(self):
        """Test extracting action information from model output"""
        # Create mock model output with different action types
        mock_output = Mock()
        
        # Test click_element action
        mock_output.action = Mock()
        mock_output.action.click_element = Mock()
        mock_output.action.click_element.xpath = "//button[@id='submit']"
        
        # This would be tested within parse_result function
        # The actual extraction logic is embedded in the parse_result function
        self.assertTrue(hasattr(mock_output.action, 'click_element'))
        self.assertEqual(mock_output.action.click_element.xpath, "//button[@id='submit']")
    
    def test_report_data_structure(self):
        """Test the structure of report data"""
        expected_keys = [
            'action_list',
            'task',
            'execution_time',
            'final_result_summary',
            'status',
            'current_time'
        ]
        
        # Mock report data structure
        report_data = {
            'action_list': [],
            'task': 'Test task',
            'execution_time': '30seconds',
            'final_result_summary': 'Task completed',
            'status': 'PASSED',
            'current_time': datetime.now().strftime("%m/%d/%Y, %I:%M:%S %p")
        }
        
        for key in expected_keys:
            self.assertIn(key, report_data)
        
        # Test time format
        self.assertRegex(report_data['current_time'], r'\d{1,2}/\d{1,2}/\d{4}, \d{1,2}:\d{2}:\d{2} [AP]M')


class TestReportGeneratorIntegration(unittest.TestCase):
    """Integration tests for report generator"""
    
    def setUp(self):
        """Set up integration test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up integration test fixtures"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @patch('report_generator.config')
    def test_end_to_end_report_generation(self, mock_config_module):
        """Test complete report generation flow"""
        mock_config = Mock()
        mock_config.get_output_dir.return_value = self.temp_dir
        mock_config_module.return_value = mock_config
        
        # Create comprehensive mock result
        mock_result = Mock()
        mock_history = Mock()
        
        # Mock model output
        mock_model_output = Mock()
        mock_model_output.current_state = Mock()
        mock_model_output.current_state.next_goal = "Complete the form"
        mock_model_output.current_state.evaluation_previous_goal = "Successfully navigated to form"
        
        # Mock action
        mock_action = Mock()
        mock_action.input_text = Mock()
        mock_action.input_text.xpath = "//input[@name='username']"
        mock_model_output.action = mock_action
        
        mock_history.model_output = mock_model_output
        mock_history.result = [Mock()]
        mock_history.result[0].extracted_content = "Form filled successfully"
        
        mock_result.history = [mock_history]
        
        with patch('report_generator.parse_and_display_result') as mock_parse_display:
            mock_parse_display.return_value = pd.DataFrame({
                'Action': ['input_text'],
                'Goal': ['Complete the form'],
                'XPath': ['//input[@name="username"]'],
                'Value': ['test_user']
            })
            
            result = report_generator.parse_result(mock_result, "Fill login form", 45)
            
            self.assertIsInstance(result, pd.DataFrame)
            self.assertGreater(len(result), 0)


if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestReportGenerator,
        TestReportGeneratorIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Exit with appropriate code
    sys.exit(0 if result.wasSuccessful() else 1)
