# VizCheck Configuration Guide

This guide covers all configuration options available in VizCheck, including setup procedures, configuration file structure, environment variables, and best practices.

## Table of Contents

1. [Configuration Overview](#configuration-overview)
2. [Configuration File (config.ini)](#configuration-file-configini)
3. [Environment Variables](#environment-variables)
4. [Agent Settings](#agent-settings)
5. [Browser Configuration](#browser-configuration)
6. [Logging Configuration](#logging-configuration)
7. [Telemetry Settings](#telemetry-settings)
8. [Build Configuration](#build-configuration)
9. [Best Practices](#best-practices)
10. [Troubleshooting](#troubleshooting)

## Configuration Overview

VizCheck uses a hierarchical configuration system with the following priority order:

1. **Environment Variables** (highest priority)
2. **Configuration File** (`config/config.ini`)
3. **Default Values** (lowest priority)

The configuration system is managed by the `Config` class, which implements a singleton pattern to ensure consistent settings throughout the application.

## Configuration File (config.ini)

### File Location
- **Development**: `config/config.ini`
- **Runtime**: `runtime_assets/config/config.ini` (for built applications)

### File Structure

```ini
[App]
app_name = VizCheck
version = 2.0.0
description = A browser automation app.
debug = false
email_service = false

[settings]
start_url = https://example.com
max_num_of_steps = 45
preview_image_path = ./assets/images/white.jpg
load_in_browser = true
output_dir = ./outputs
```

### Configuration Sections

#### [App] Section
Application metadata and core settings:

| Setting         | Type    | Default                   | Description              |
|-----------------|---------|---------------------------|--------------------------|
| `app_name`      | string  | VizCheck                  | Application name         |
| `version`       | string  | 2.0.0                     | Application version      |
| `description`   | string  | No description provided.  | Application description  |
| `debug`         | boolean | false                     | Enable debug mode        |
| `email_service` | boolean | false                     | Enable email service     |

#### [settings] Section
Runtime behavior settings:

| Setting               | Type    | Default                       | Description               |
|-----------------------|---------|-------------------------------|---------------------------|
| `start_url`           | string  | https://example.com           | Default starting URL      |
| `max_num_of_steps`    | integer | 45                            | Maximum automation steps  |
| `preview_image_path`  | string  | ./assets/images/white.jpg     | Preview image path        |
| `load_in_browser`     | boolean | true                          | Load content in browser   |
| `output_dir`          | string  | ./outputs                     | Output directory path     |

## Environment Variables

Environment variables override configuration file settings and provide secure credential management.

### Required Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `AZURE_API_KEY` | Azure OpenAI API key | `sk-...` |

### Optional Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `AZURE_DEPLOYMENT` | gpt-4o | Azure OpenAI deployment name |
| `AZURE_ENDPOINT` | - | Azure OpenAI endpoint URL |
| `OPENAI_API_VERSION` | 2024-05-01-preview | OpenAI API version |
| `LOGGING_LEVEL` | INFO | Application logging level |
| `ACS_END_POINT` | - | Azure Communication Service endpoint |
| `ACS_ACCESS_KEY` | - | Azure Communication Service access key |
| `ANONYMIZED_TELEMETRY` | true | Enable/disable telemetry |
| `WIN_FONT_DIR` | C:\Windows\Fonts | Windows font directory |

### Setting Environment Variables

#### Windows
```cmd
set AZURE_API_KEY=your_api_key_here
set LOGGING_LEVEL=DEBUG
```

#### Linux/macOS
```bash
export AZURE_API_KEY=your_api_key_here
export LOGGING_LEVEL=DEBUG
```

#### .env File
Create a `.env` file in the project root:
```env
AZURE_API_KEY=your_api_key_here
AZURE_DEPLOYMENT=gpt-4o
AZURE_ENDPOINT=https://your-endpoint.openai.azure.com/
OPENAI_API_VERSION=2024-05-01-preview
LOGGING_LEVEL=INFO
```

## Agent Settings

Agent behavior is configured through the `AgentSettings` class:

```python
class AgentSettings(BaseModel):
    use_vision: bool = True
    use_vision_for_planner: bool = False
    save_conversation_path: Optional[str] = None
    save_conversation_path_encoding: Optional[str] = 'utf-8'
    max_failures: int = 3
    retry_delay: int = 10
    max_input_tokens: int = 128000
    validate_output: bool = False
    message_context: Optional[str] = None
    generate_gif: bool | str = False
    available_file_paths: Optional[list[str]] = None
    include_attributes: list[str] = [
        'title', 'type', 'name', 'role', 'tabindex',
        'aria-label', 'placeholder', 'value', 'alt', 'aria-expanded'
    ]
```

### Key Agent Settings

| Setting           | Type            | Default | Description                       |
|-------------------|-----------------|---------|-----------------------------------|
| `use_vision`      | boolean         | true    | Enable vision capabilities        |
| `max_failures`    | integer         | 3       | Maximum retry attempts            |
| `retry_delay`     | integer         | 10      | Delay between retries (seconds)   |
| `max_input_tokens`| integer         | 128000  | Maximum input token limit         |
| `generate_gif`    | boolean/string  | false   | Generate GIF of automation        |

## Browser Configuration

Browser settings are configured through Chrome configuration:

```python
chrome_config = ChromeConfig(
    headless=False,
    disable_security=True,
    window_width=1280,
    window_height=720
)
```

## Logging Configuration

### Logging Levels
- `DEBUG`: Detailed diagnostic information
- `INFO`: General information messages
- `WARNING`: Warning messages
- `ERROR`: Error messages
- `CRITICAL`: Critical error messages
- `RESULT`: Custom level for results (level 35)

### Configuration
Set via environment variable:
```bash
export LOGGING_LEVEL=DEBUG
```

Or in code:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Telemetry Settings

VizCheck includes anonymized telemetry for product improvement.

### Disable Telemetry
```bash
export ANONYMIZED_TELEMETRY=false
```

### Telemetry Configuration
- **User ID Path**: `~/.cache/browser_use/telemetry_user_id`
- **Default State**: Enabled
- **Data Collected**: Anonymized usage patterns

## Build Configuration

Build process configuration is managed through:

### Version Management
Versions are automatically managed in `config/config.ini`:
```ini
[App]
version = 2.0.0
```

### Build Scripts
Located in `build_scripts/` directory with multiple spec files for different configurations.

## Best Practices

### 1. Security
- **Never commit API keys** to version control
- Use `.env` files for sensitive credentials
- Set appropriate file permissions on config files

### 2. Environment Management
- Use different config files for development/production
- Validate configuration on startup
- Provide meaningful defaults

### 3. Logging
- Use appropriate logging levels
- Configure log rotation for production
- Include context in log messages

### 4. Performance
- Adjust `max_num_of_steps` based on task complexity
- Monitor resource usage with debug mode
- Use appropriate retry settings

### 5. Maintenance
- Regularly update API versions
- Monitor deprecated settings
- Document custom configurations

## Troubleshooting

### Common Issues

#### Configuration File Not Found
```
Warning: Config file 'config/config.ini' not found. Using default values.
```
**Solution**: Create the config file or check the file path.

#### Invalid Configuration Values
```
Error parsing INI file 'config/config.ini': ...
```
**Solution**: Validate INI file syntax and data types.

#### Missing API Keys
```
Azure API key not found
```
**Solution**: Set the `AZURE_API_KEY` environment variable.

#### Permission Issues
```
Permission denied accessing config file
```
**Solution**: Check file permissions and ownership.

### Validation Commands

#### Check Configuration
```python
from config import Config
config = Config()
print(f"App Name: {config.get_app_name()}")
print(f"Version: {config.get_version()}")
print(f"Debug Mode: {config.get_debug_mode()}")
```

#### Verify Environment Variables
```python
import os
print(f"Azure API Key: {'Set' if os.getenv('AZURE_API_KEY') else 'Not Set'}")
print(f"Logging Level: {os.getenv('LOGGING_LEVEL', 'INFO')}")
```

### Configuration Reset

To reset to default configuration:
1. Delete or rename `config/config.ini`
2. Clear relevant environment variables
3. Restart the application

The application will use default values and create a new configuration file if needed.

---

**Next Steps**: After configuring VizCheck, see the [Deployment Guide](Deployment_Guide.md) for production setup instructions.
