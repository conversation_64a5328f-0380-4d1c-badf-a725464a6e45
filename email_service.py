from azure.communication.email import EmailClient
from config import Config
from logger_config import setup_logger  

logging = setup_logger(__name__)
config = Config()

class EmailService:
    def __init__(self):
        """ 
        Initializes the EmailService class, setting up the Azure Communication Service client.
            Raises: ValueError if the Azure Communication Service credentials are not set.
        """
        self.client = None
        self._initialize()

    def _initialize(self):
        """
        Initializes the EmailService by setting up the Azure Communication Service client.
            Raises: ValueError if the Azure Communication Service credentials are not set.
        """ 
        acs_access_key = config.get_acs_access_key()
        acs_end_point = config.get_acs_end_point()
        if not acs_access_key or not acs_end_point:
            raise ValueError("Azure Communication Service credentials are not set")
        self.client = EmailClient.from_connection_string(f"endpoint={acs_end_point};accesskey={acs_access_key}")    

    def sent_details(self, recipient_emails, cc_recipient_emails, subject, body, files):
        """
        Sends an email with the provided details using Azure Communication Service. 
        Args:
            recipient_emails (list or str): List of recipient email addresses or a single email address.
            cc_recipient_emails (list or str): List of CC recipient email addresses or a single email address.
            subject (str): Subject of the email.
            body (str): Body content of the email.
            files (list): List of file paths to be attached to the email.
        Returns:
            dict: A dictionary containing the result of the email sending operation.    
        Raises:
            ValueError: If the email client is not initialized, or if no recipients are provided, or if files are not a list.
        """
        try:
            if self.client is None:
                logging.error("Email client is not initialized")
                raise ValueError("Email client is not initialized")
            if not recipient_emails:
                logging.error("Error: Recipients are required")
                raise ValueError("Recipients are required")

            if not isinstance(files, list):
                logging.warning("Files should be a list")
                # raise ValueError("Files should be a list")
            
            recipients_list = []
            if isinstance(recipient_emails, list):
                recipients_list = [{"address": _} for _ in recipient_emails if _ ]
            elif isinstance(recipient_emails, str):
                recipients_list = [{"address": recipient_emails}]
            if recipients_list == []:
                logging.error("Error: No valid recipient emails provided")
                raise ValueError("No valid recipient emails provided")
            
            recipients_dict = {
                    "to": recipients_list,
                }
            
            if isinstance(cc_recipient_emails, list):
                recipients_dict["cc"] = [{"address": _} for _ in cc_recipient_emails if _]
            elif isinstance(cc_recipient_emails, str) and cc_recipient_emails:
                recipients_dict["cc"] = [{"address": cc_recipient_emails}]
            
            pdf_base64 = ""
            message = {
                "senderAddress": "<EMAIL>",
                "recipients": recipients_dict,
                "content": {
                    "subject": subject,
                    "plainText": body,
                },
                "attachments" : [
                    {
                        "name": "Loan_Details.pdf",
                        "contentInBase64": pdf_base64,
                        "contentType": "application/pdf"
                    }
                ]
            }
            poller = self.client.begin_send(message)
            result = poller.result()
            return {"message": "Email sent successfully", "result": str(result)}
        except Exception as e:
            raise ValueError(f"Failed to send email: {str(e)}") from e

        