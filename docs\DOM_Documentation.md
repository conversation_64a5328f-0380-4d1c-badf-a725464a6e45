# DOM Documentation

## Table of Contents
1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Core Components](#core-components)
4. [DOM Tree Building](#dom-tree-building)
5. [Data Models](#data-models)
6. [JavaScript Integration](#javascript-integration)
7. [Usage Examples](#usage-examples)
8. [API Reference](#api-reference)
9. [Best Practices](#best-practices)
10. [Troubleshooting](#troubleshooting)

## Overview

The DOM (Document Object Model) module is the foundation of VizCheck's web page analysis and interaction capabilities. It provides comprehensive DOM tree extraction, element identification, and interaction support for browser automation. The module combines Python-based processing with JavaScript execution to create a rich, interactive representation of web pages.

### Key Features
- **Complete DOM Tree Extraction**: Full page structure analysis with element relationships
- **Interactive Element Identification**: Automatic detection of clickable and interactive elements
- **Visual Element Highlighting**: Real-time element highlighting with index-based identification
- **Viewport-Aware Processing**: Intelligent handling of visible and viewport-specific elements
- **Shadow DOM Support**: Complete support for Shadow DOM and iframe content
- **Performance Optimization**: Caching mechanisms and optimized tree traversal
- **Coordinate Tracking**: Precise element positioning in both page and viewport coordinates

### Use Cases
- **Web Automation**: Identify and interact with page elements
- **Testing Frameworks**: Automated testing with reliable element identification
- **Content Extraction**: Extract structured data from web pages
- **UI Analysis**: Analyze page structure and element relationships
- **Accessibility Testing**: Identify interactive elements and their properties

## Architecture

The DOM module follows a hybrid architecture combining Python orchestration with JavaScript execution for optimal performance and browser compatibility.

### High-Level Architecture

```
┌──────────────────────────────────────────────────────────────┐
│                    VizCheck Browser Automation               │
├──────────────────────────────────────────────────────────────┤
│                      Browser Context                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐   │
│  │   Playwright    │  │   Page Context  │  │   Session   │   │
│  │   Integration   │  │                 │  │  Management │   │
│  └─────────────────┘  └─────────────────┘  └─────────────┘   │
├──────────────────────────────────────────────────────────────┤
│                        DOM Module                            │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                   DomService                            │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐  │ │
│  │  │    DOM      │  │   Element   │  │    Tree         │  │ │
│  │  │ Extraction  │  │ Processing  │  │  Construction   │  │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘  │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                JavaScript Engine                        │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐  │ │
│  │  │    DOM      │  │  Element    │  │   Performance   │  │ │
│  │  │  Analysis   │  │ Highlighting│  │   Monitoring    │  │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘  │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                   Data Models                           │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐  │ │
│  │  │ DOM Element │  │  DOM Text   │  │    DOM State    │  │ │
│  │  │    Node     │  │    Node     │  │                 │  │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘  │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              History Tree Processor                     │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐  │ │
│  │  │  Element    │  │   Hashing   │  │     Element     │  │ │
│  │  │  Tracking   │  │   System    │  │   Comparison    │  │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘  │ │
│  └─────────────────────────────────────────────────────────┘ │
└──────────────────────────────────────────────────────────────┘
```

### Component Architecture

#### 1. DomService (Python Layer)
The main orchestration layer that coordinates DOM operations:

```
DomService
├── DOM Tree Building
│   ├── get_clickable_elements()
│   ├── _build_dom_tree()
│   └── _construct_dom_tree()
├── JavaScript Integration
│   ├── JavaScript Code Loading
│   ├── Browser Evaluation
│   └── Result Processing
├── Node Processing
│   ├── _parse_node()
│   ├── Element Creation
│   └── Tree Construction
└── Performance Management
    ├── Garbage Collection
    ├── Memory Optimization
    └── Cache Management
```

#### 2. JavaScript Engine (Browser Layer)
High-performance DOM analysis executed in the browser:

```
buildDomTree.js
├── DOM Traversal
│   ├── Element Discovery
│   ├── Visibility Detection
│   └── Interaction Analysis
├── Element Highlighting
│   ├── Visual Overlays
│   ├── Index Assignment
│   └── Color Coding
├── Performance Monitoring
│   ├── Timing Metrics
│   ├── Cache Statistics
│   └── Operation Counting
├── Viewport Management
│   ├── Viewport Detection
│   ├── Scroll Handling
│   └── Coordinate Calculation
└── Caching System
    ├── Bounding Rect Cache
    ├── Computed Style Cache
    └── Cache Hit Rate Tracking
```

### Data Flow Architecture

```
Page Load → JavaScript Injection → DOM Analysis → Tree Construction → Element Processing → Result Return
    ↓              ↓                    ↓               ↓                  ↓               ↓
Browser Page → buildDomTree.js → Element Discovery → Python Processing → Node Creation → DOMState
    ↓              ↓                    ↓               ↓                  ↓               ↓
HTML Content → DOM Traversal → Interactive Elements → Tree Building → Selector Map → Final Result
```

**Processing Pipeline:**
1. **JavaScript Injection**: Load and execute DOM analysis JavaScript
2. **DOM Traversal**: Recursively analyze all page elements
3. **Element Classification**: Identify interactive, visible, and viewport elements
4. **Highlighting**: Apply visual highlights with index assignment
5. **Data Collection**: Gather element properties, coordinates, and relationships
6. **Python Processing**: Convert JavaScript results to Python data structures
7. **Tree Construction**: Build hierarchical DOM tree with parent-child relationships
8. **Selector Mapping**: Create index-to-element mapping for quick access

### Performance Architecture

#### 1. Caching Strategy
```
Cache Layers
├── JavaScript Layer
│   ├── Bounding Rect Cache (WeakMap)
│   ├── Computed Style Cache (WeakMap)
│   └── Performance Metrics Cache
├── Python Layer
│   ├── XPath Cache
│   ├── Node Map Cache
│   └── Selector Map Cache
└── Memory Management
    ├── Garbage Collection
    ├── Cache Clearing
    └── Memory Optimization
```

#### 2. Optimization Techniques
- **WeakMap Caching**: Automatic memory management for DOM element caches
- **Lazy Evaluation**: Compute properties only when needed
- **Batch Processing**: Process multiple elements together
- **Early Termination**: Stop processing when conditions are met
- **Memory Cleanup**: Explicit garbage collection and cache clearing

## Core Components

### 1. DomService (`service.py`)

The main service class that orchestrates DOM operations:

#### Key Responsibilities
- **JavaScript Integration**: Load and execute DOM analysis JavaScript
- **Tree Construction**: Build Python DOM tree from JavaScript results
- **Element Processing**: Parse and create DOM node objects
- **Performance Management**: Handle caching and memory optimization
- **Error Handling**: Robust error handling for JavaScript execution

#### Core Methods
- `get_clickable_elements()`: Main entry point for DOM extraction
- `_build_dom_tree()`: Execute JavaScript and get raw DOM data
- `_construct_dom_tree()`: Convert JavaScript results to Python objects
- `_parse_node()`: Parse individual DOM nodes from JavaScript data

### 2. DOM Data Models (`views.py`)

#### DOMBaseNode
Base class for all DOM nodes with common properties:
- Visibility tracking
- Parent-child relationships
- Type identification

#### DOMElementNode
Represents HTML elements with comprehensive metadata:
- Tag name and attributes
- XPath and CSS selectors
- Interactive element detection
- Viewport and coordinate information
- Highlight index for automation
- Shadow DOM support

#### DOMTextNode
Represents text content within elements:
- Text content extraction
- Parent relationship tracking
- Visibility inheritance
- Highlight index awareness

#### DOMState
Container for complete DOM analysis results:
- Complete element tree
- Selector map for quick element access
- Performance metrics (debug mode)

### 3. JavaScript Engine (`buildDomTree.js`)

High-performance browser-side DOM analysis:

#### Core Functions
- **Element Discovery**: Find all interactive and visible elements
- **Visibility Detection**: Determine element visibility and viewport presence
- **Highlighting System**: Visual element highlighting with index assignment
- **Performance Monitoring**: Comprehensive timing and cache metrics
- **Coordinate Calculation**: Precise element positioning
- **Shadow DOM Handling**: Complete Shadow DOM and iframe support

#### Performance Features
- **Caching System**: WeakMap-based caching for expensive operations
- **Timing Metrics**: Detailed performance measurement
- **Memory Management**: Automatic cache cleanup
- **Batch Operations**: Optimized bulk processing

### 4. History Tree Processor Integration

The DOM module integrates with the History Tree Processor for element tracking:
- **Element Conversion**: Convert DOM elements to history format
- **Hash Generation**: Create unique element fingerprints
- **Element Comparison**: Track element changes across page updates
- **Persistence**: Maintain element references across navigation

## DOM Tree Building

### Building Process

#### 1. JavaScript Execution
```python
# Execute JavaScript in browser context
eval_page = await self.page.evaluate(self.js_code, args)
```

**JavaScript Arguments:**
- `doHighlightElements`: Enable/disable visual highlighting
- `focusHighlightIndex`: Focus on specific element index
- `viewportExpansion`: Expand viewport detection area
- `debugMode`: Enable performance monitoring

#### 2. Element Discovery
The JavaScript engine traverses the DOM and identifies:
- **Interactive Elements**: Buttons, links, inputs, etc.
- **Visible Elements**: Elements visible in viewport
- **Top Elements**: Primary interactive elements
- **Shadow DOM Elements**: Elements within Shadow DOM
- **Iframe Elements**: Elements within iframes

#### 3. Element Classification
Each element is classified based on:
```javascript
// Element properties determined by JavaScript
{
  tagName: string,
  xpath: string,
  attributes: object,
  isVisible: boolean,
  isInteractive: boolean,
  isTopElement: boolean,
  isInViewport: boolean,
  highlightIndex: number,
  shadowRoot: boolean,
  children: array
}
```

#### 4. Tree Construction
Python processes JavaScript results to build the DOM tree:
```python
# Convert JavaScript data to Python objects
for id, node_data in js_node_map.items():
    node, children_ids = self._parse_node(node_data)
    if node is None:
        continue
    
    node_map[id] = node
    
    # Build parent-child relationships
    if isinstance(node, DOMElementNode):
        for child_id in children_ids:
            child_node = node_map[child_id]
            child_node.parent = node
            node.children.append(child_node)
```

### Element Highlighting

#### Visual Highlighting System
The JavaScript engine provides real-time element highlighting:

```javascript
// Highlight element with colored overlay
function highlightElement(element, index) {
    // Create highlight container
    const container = document.getElementById("playwright-highlight-container");
    
    // Create colored overlay
    const overlay = document.createElement("div");
    overlay.style.border = `2px solid ${baseColor}`;
    overlay.style.backgroundColor = backgroundColor;
    
    // Position overlay over element
    const rect = element.getBoundingClientRect();
    overlay.style.top = `${rect.top}px`;
    overlay.style.left = `${rect.left}px`;
    overlay.style.width = `${rect.width}px`;
    overlay.style.height = `${rect.height}px`;
    
    // Add index label
    const label = document.createElement("div");
    label.textContent = index;
    
    container.appendChild(overlay);
    container.appendChild(label);
}
```

#### Highlighting Features
- **Color Coding**: Different colors for different element types
- **Index Labels**: Numeric labels for element identification
- **Responsive Positioning**: Automatic label positioning based on element size
- **Scroll Tracking**: Highlights follow elements during scrolling
- **Focus Highlighting**: Special highlighting for focused elements

## Data Models

### DOMElementNode

```python
@dataclass(frozen=False)
class DOMElementNode(DOMBaseNode):
    tag_name: str                                    # HTML tag name
    xpath: str                                       # Element XPath
    attributes: Dict[str, str]                       # Element attributes
    children: List[DOMBaseNode]                      # Child nodes
    is_interactive: bool = False                     # Interactive element flag
    is_top_element: bool = False                     # Top-level element flag
    is_in_viewport: bool = False                     # Viewport visibility
    shadow_root: bool = False                        # Shadow DOM indicator
    highlight_index: Optional[int] = None            # Automation index
    viewport_coordinates: Optional[CoordinateSet] = None    # Viewport position
    page_coordinates: Optional[CoordinateSet] = None        # Page position
    viewport_info: Optional[ViewportInfo] = None            # Viewport context
```

#### Key Methods
- `get_all_text_till_next_clickable_element()`: Extract text content
- `clickable_elements_to_string()`: Convert to string representation
- `get_file_upload_element()`: Find file upload elements
- `hash`: Cached hash property for element identification

### DOMTextNode

```python
@dataclass(frozen=False)
class DOMTextNode(DOMBaseNode):
    text: str                                        # Text content
    type: str = 'TEXT_NODE'                         # Node type identifier
```

#### Key Methods
- `has_parent_with_highlight_index()`: Check for highlighted parent
- `is_parent_in_viewport()`: Check parent viewport status
- `is_parent_top_element()`: Check parent top element status

### DOMState

```python
@dataclass
class DOMState:
    element_tree: DOMElementNode                     # Complete DOM tree
    selector_map: SelectorMap                        # Index-to-element mapping
```

**SelectorMap**: `dict[int, DOMElementNode]` - Quick access to elements by highlight index

### Coordinate Models

#### CoordinateSet
```python
class CoordinateSet(BaseModel):
    top_left: Coordinates                            # Top-left corner
    top_right: Coordinates                           # Top-right corner
    bottom_left: Coordinates                         # Bottom-left corner
    bottom_right: Coordinates                        # Bottom-right corner
    center: Coordinates                              # Center point
    width: int                                       # Element width
    height: int                                      # Element height
```

#### ViewportInfo
```python
class ViewportInfo(BaseModel):
    scroll_x: int                                    # Horizontal scroll
    scroll_y: int                                    # Vertical scroll
    width: int                                       # Viewport width
    height: int                                      # Viewport height
```

## JavaScript Integration

### buildDomTree.js Architecture

The JavaScript engine is a sophisticated DOM analysis system that runs directly in the browser for optimal performance.

#### Core Functions

##### Main Entry Point
```javascript
(args = {
  doHighlightElements: true,
  focusHighlightIndex: -1,
  viewportExpansion: 0,
  debugMode: false
}) => {
  // DOM analysis implementation
}
```

##### Performance Monitoring
```javascript
const PERF_METRICS = {
  buildDomTreeCalls: 0,
  timings: {
    buildDomTree: 0,
    highlightElement: 0,
    isInteractiveElement: 0,
    isElementVisible: 0,
    // ... more timing metrics
  },
  cacheMetrics: {
    boundingRectCacheHits: 0,
    boundingRectCacheMisses: 0,
    computedStyleCacheHits: 0,
    computedStyleCacheMisses: 0,
    // ... cache statistics
  }
}
```

##### Caching System
```javascript
const DOM_CACHE = {
  boundingRects: new WeakMap(),
  computedStyles: new WeakMap(),
  clearCache: () => {
    DOM_CACHE.boundingRects = new WeakMap();
    DOM_CACHE.computedStyles = new WeakMap();
  }
};

function getCachedBoundingRect(element) {
  if (DOM_CACHE.boundingRects.has(element)) {
    return DOM_CACHE.boundingRects.get(element);
  }

  const rect = element.getBoundingClientRect();
  DOM_CACHE.boundingRects.set(element, rect);
  return rect;
}
```

#### Element Analysis Functions

##### Interactive Element Detection
```javascript
function isInteractiveElement(element) {
  const interactiveTags = ['button', 'a', 'input', 'select', 'textarea'];
  const interactiveRoles = ['button', 'link', 'menuitem', 'option'];

  return interactiveTags.includes(element.tagName.toLowerCase()) ||
         interactiveRoles.includes(element.getAttribute('role')) ||
         element.hasAttribute('onclick') ||
         element.style.cursor === 'pointer';
}
```

##### Visibility Detection
```javascript
function isElementVisible(element) {
  const style = getCachedComputedStyle(element);
  const rect = getCachedBoundingRect(element);

  return style.display !== 'none' &&
         style.visibility !== 'hidden' &&
         style.opacity !== '0' &&
         rect.width > 0 &&
         rect.height > 0;
}
```

##### Viewport Detection
```javascript
function isInExpandedViewport(element, expansion = 0) {
  const rect = getCachedBoundingRect(element);
  const viewport = {
    width: window.innerWidth,
    height: window.innerHeight
  };

  return rect.left >= -expansion &&
         rect.top >= -expansion &&
         rect.right <= viewport.width + expansion &&
         rect.bottom <= viewport.height + expansion;
}
```

### JavaScript-Python Integration

#### Data Transfer Format
```javascript
// JavaScript returns this structure
{
  map: {
    "elementId": {
      tagName: "button",
      xpath: "/html/body/div[1]/button",
      attributes: { "class": "btn", "id": "submit" },
      isVisible: true,
      isInteractive: true,
      isTopElement: true,
      isInViewport: true,
      highlightIndex: 1,
      children: ["childId1", "childId2"]
    }
  },
  rootId: "rootElementId",
  perfMetrics: { /* performance data */ }
}
```

#### Python Processing
```python
async def _construct_dom_tree(self, eval_page: dict) -> tuple[DOMElementNode, SelectorMap]:
    js_node_map = eval_page['map']
    js_root_id = eval_page['rootId']

    selector_map = {}
    node_map = {}

    # Process each node from JavaScript
    for id, node_data in js_node_map.items():
        node, children_ids = self._parse_node(node_data)
        if node is None:
            continue

        node_map[id] = node

        # Build selector map for quick access
        if isinstance(node, DOMElementNode) and node.highlight_index is not None:
            selector_map[node.highlight_index] = node

    return html_to_dict, selector_map
```

## Usage Examples

### Basic DOM Extraction

```python
from browser_use.dom.service import DomService
from playwright.async_api import async_playwright

async def extract_dom():
    async with async_playwright() as p:
        browser = await p.chromium.launch()
        page = await browser.new_page()
        await page.goto("https://example.com")

        # Create DOM service
        dom_service = DomService(page)

        # Extract clickable elements
        dom_state = await dom_service.get_clickable_elements(
            highlight_elements=True,
            focus_element=-1,
            viewport_expansion=0
        )

        # Access the DOM tree
        root_element = dom_state.element_tree
        selector_map = dom_state.selector_map

        print(f"Found {len(selector_map)} interactive elements")

        # Iterate through interactive elements
        for index, element in selector_map.items():
            print(f"Element {index}: {element.tag_name} - {element.get_all_text_till_next_clickable_element()}")

        await browser.close()
```

### Element Interaction

```python
async def interact_with_elements():
    dom_service = DomService(page)
    dom_state = await dom_service.get_clickable_elements(highlight_elements=True)

    # Find specific element by index
    submit_button = dom_state.selector_map.get(5)  # Element with index 5
    if submit_button:
        print(f"Submit button: {submit_button.tag_name}")
        print(f"XPath: {submit_button.xpath}")
        print(f"Attributes: {submit_button.attributes}")

        # Click the element using Playwright
        await page.click(f"xpath={submit_button.xpath}")
```

### Tree Traversal

```python
def traverse_dom_tree(node: DOMElementNode, depth: int = 0):
    """Recursively traverse the DOM tree"""
    indent = "  " * depth

    if isinstance(node, DOMElementNode):
        print(f"{indent}<{node.tag_name}>")
        if node.highlight_index is not None:
            print(f"{indent}  [Index: {node.highlight_index}]")

        # Traverse children
        for child in node.children:
            if isinstance(child, DOMElementNode):
                traverse_dom_tree(child, depth + 1)
            elif isinstance(child, DOMTextNode):
                if child.text.strip():
                    print(f"{indent}  Text: {child.text.strip()}")

# Usage
dom_state = await dom_service.get_clickable_elements()
traverse_dom_tree(dom_state.element_tree)
```

### Element Search and Filtering

```python
def find_elements_by_tag(root: DOMElementNode, tag_name: str) -> List[DOMElementNode]:
    """Find all elements with specific tag name"""
    results = []

    def search_recursive(node: DOMElementNode):
        if node.tag_name.lower() == tag_name.lower():
            results.append(node)

        for child in node.children:
            if isinstance(child, DOMElementNode):
                search_recursive(child)

    search_recursive(root)
    return results

def find_interactive_elements(root: DOMElementNode) -> List[DOMElementNode]:
    """Find all interactive elements"""
    results = []

    def search_recursive(node: DOMElementNode):
        if node.is_interactive and node.highlight_index is not None:
            results.append(node)

        for child in node.children:
            if isinstance(child, DOMElementNode):
                search_recursive(child)

    search_recursive(root)
    return results

# Usage
dom_state = await dom_service.get_clickable_elements()

# Find all buttons
buttons = find_elements_by_tag(dom_state.element_tree, "button")
print(f"Found {len(buttons)} buttons")

# Find all interactive elements
interactive = find_interactive_elements(dom_state.element_tree)
print(f"Found {len(interactive)} interactive elements")
```

### Performance Monitoring

```python
async def extract_with_performance_monitoring():
    dom_service = DomService(page)

    # Enable debug mode for performance metrics
    import logging
    logging.getLogger('browser_use.dom.service').setLevel(logging.DEBUG)

    # Extract DOM with performance monitoring
    dom_state = await dom_service.get_clickable_elements(
        highlight_elements=True,
        viewport_expansion=100
    )

    # Performance metrics are logged automatically in debug mode
    print(f"Extracted {len(dom_state.selector_map)} elements")
```

### Custom Element Processing

```python
class CustomDOMProcessor:
    def __init__(self, dom_service: DomService):
        self.dom_service = dom_service

    async def extract_form_elements(self) -> Dict[str, List[DOMElementNode]]:
        """Extract and categorize form elements"""
        dom_state = await self.dom_service.get_clickable_elements()

        form_elements = {
            'inputs': [],
            'buttons': [],
            'selects': [],
            'textareas': []
        }

        def categorize_element(node: DOMElementNode):
            if node.tag_name.lower() == 'input':
                form_elements['inputs'].append(node)
            elif node.tag_name.lower() == 'button':
                form_elements['buttons'].append(node)
            elif node.tag_name.lower() == 'select':
                form_elements['selects'].append(node)
            elif node.tag_name.lower() == 'textarea':
                form_elements['textareas'].append(node)

            for child in node.children:
                if isinstance(child, DOMElementNode):
                    categorize_element(child)

        categorize_element(dom_state.element_tree)
        return form_elements

    async def extract_links_with_text(self) -> List[Dict[str, str]]:
        """Extract all links with their text content"""
        dom_state = await self.dom_service.get_clickable_elements()
        links = []

        def extract_links(node: DOMElementNode):
            if node.tag_name.lower() == 'a' and 'href' in node.attributes:
                text = node.get_all_text_till_next_clickable_element()
                links.append({
                    'href': node.attributes['href'],
                    'text': text.strip(),
                    'index': node.highlight_index
                })

            for child in node.children:
                if isinstance(child, DOMElementNode):
                    extract_links(child)

        extract_links(dom_state.element_tree)
        return links

# Usage
processor = CustomDOMProcessor(dom_service)
form_elements = await processor.extract_form_elements()
links = await processor.extract_links_with_text()

print(f"Found {len(form_elements['inputs'])} input fields")
print(f"Found {len(links)} links")
```

## API Reference

### DomService Class

#### Constructor
```python
DomService(page: 'Page')
```

**Parameters:**
- `page`: Playwright Page instance for DOM operations

#### Methods

##### `get_clickable_elements(highlight_elements: bool = True, focus_element: int = -1, viewport_expansion: int = 0) -> DOMState`
Main entry point for DOM extraction and analysis.

**Parameters:**
- `highlight_elements`: Enable visual element highlighting
- `focus_element`: Specific element index to focus on (-1 for all)
- `viewport_expansion`: Expand viewport detection area (pixels)

**Returns:** `DOMState` containing complete DOM tree and selector map

**Usage:**
```python
dom_state = await dom_service.get_clickable_elements(
    highlight_elements=True,
    viewport_expansion=100
)
```

##### `_build_dom_tree(highlight_elements: bool, focus_element: int, viewport_expansion: int) -> tuple[DOMElementNode, SelectorMap]`
Internal method that executes JavaScript and processes results.

##### `_construct_dom_tree(eval_page: dict) -> tuple[DOMElementNode, SelectorMap]`
Internal method that converts JavaScript results to Python objects.

##### `_parse_node(node_data: dict) -> tuple[Optional[DOMBaseNode], list[int]]`
Internal method that parses individual DOM nodes from JavaScript data.

### DOMElementNode API

#### Properties
- `tag_name: str` - HTML tag name
- `xpath: str` - Element XPath
- `attributes: Dict[str, str]` - Element attributes
- `children: List[DOMBaseNode]` - Child nodes
- `is_interactive: bool` - Interactive element flag
- `is_top_element: bool` - Top-level element flag
- `is_in_viewport: bool` - Viewport visibility
- `shadow_root: bool` - Shadow DOM indicator
- `highlight_index: Optional[int]` - Automation index
- `viewport_coordinates: Optional[CoordinateSet]` - Viewport position
- `page_coordinates: Optional[CoordinateSet]` - Page position
- `viewport_info: Optional[ViewportInfo]` - Viewport context

#### Methods

##### `get_all_text_till_next_clickable_element(max_depth: int = -1) -> str`
Extract text content until the next clickable element.

**Parameters:**
- `max_depth`: Maximum traversal depth (-1 for unlimited)

**Returns:** Combined text content as string

##### `clickable_elements_to_string(include_attributes: list[str] = []) -> str`
Convert DOM tree to string representation with clickable elements.

**Parameters:**
- `include_attributes`: List of attributes to include in output

**Returns:** Formatted string representation

##### `get_file_upload_element(check_siblings: bool = True) -> Optional['DOMElementNode']`
Find file upload elements within the element tree.

**Parameters:**
- `check_siblings`: Whether to check sibling elements

**Returns:** File upload element if found, None otherwise

##### `hash: HashedDomElement` (Property)
Cached hash property for element identification using History Tree Processor.

### DOMTextNode API

#### Properties
- `text: str` - Text content
- `type: str` - Node type identifier ('TEXT_NODE')

#### Methods

##### `has_parent_with_highlight_index() -> bool`
Check if any parent element has a highlight index.

##### `is_parent_in_viewport() -> bool`
Check if parent element is in viewport.

##### `is_parent_top_element() -> bool`
Check if parent element is a top element.

### DOMState API

#### Properties
- `element_tree: DOMElementNode` - Complete DOM tree root
- `selector_map: SelectorMap` - Index-to-element mapping

**SelectorMap Type:** `dict[int, DOMElementNode]`

### JavaScript API (buildDomTree.js)

#### Main Function
```javascript
(args = {
  doHighlightElements: true,
  focusHighlightIndex: -1,
  viewportExpansion: 0,
  debugMode: false
}) => { /* implementation */ }
```

#### Key Functions
- `highlightElement(element, index)` - Highlight element with visual overlay
- `isInteractiveElement(element)` - Determine if element is interactive
- `isElementVisible(element)` - Check element visibility
- `isInExpandedViewport(element, expansion)` - Check viewport presence
- `getCachedBoundingRect(element)` - Get cached bounding rectangle
- `getCachedComputedStyle(element)` - Get cached computed style

## Best Practices

### DOM Extraction

1. **Use Appropriate Highlighting**: Enable highlighting for debugging, disable for production
2. **Viewport Expansion**: Use viewport expansion for elements near viewport edges
3. **Performance Monitoring**: Enable debug mode for performance analysis
4. **Memory Management**: Allow garbage collection after large DOM operations

### Element Interaction

1. **Index Validation**: Always check if element index exists in selector map
2. **XPath Usage**: Use XPath for reliable element targeting
3. **Attribute Checking**: Verify element attributes before interaction
4. **Visibility Verification**: Ensure elements are visible before interaction

### Tree Traversal

1. **Depth Limiting**: Use max_depth parameter to prevent deep recursion
2. **Type Checking**: Always check node types before casting
3. **Null Checking**: Handle None values in parent-child relationships
4. **Early Termination**: Stop traversal when target is found

### Performance Optimization

1. **Caching**: Leverage built-in caching mechanisms
2. **Batch Operations**: Process multiple elements together
3. **Selective Extraction**: Use focus_element for targeted extraction
4. **Memory Cleanup**: Clear caches and collect garbage for large pages

### Error Handling

1. **JavaScript Errors**: Handle JavaScript execution failures gracefully
2. **Invalid Elements**: Check for None returns from element operations
3. **Timeout Handling**: Set appropriate timeouts for DOM operations
4. **Fallback Strategies**: Implement alternative element finding methods

## Troubleshooting

### Common Issues

#### JavaScript Execution Failures
**Problem**: JavaScript code fails to execute in browser
**Causes:**
- Page not fully loaded
- JavaScript disabled
- Content Security Policy restrictions
- Browser compatibility issues

**Solutions:**
```python
# Verify JavaScript execution capability
try:
    result = await page.evaluate('1+1')
    if result != 2:
        raise ValueError('JavaScript execution not working')
except Exception as e:
    logger.error(f'JavaScript execution failed: {e}')
    # Implement fallback strategy
```

#### Elements Not Found
**Problem**: Expected elements not appearing in selector map
**Causes:**
- Elements not interactive
- Elements not visible
- Elements outside viewport
- Shadow DOM or iframe content

**Solutions:**
```python
# Expand viewport detection
dom_state = await dom_service.get_clickable_elements(
    viewport_expansion=200  # Expand detection area
)

# Check element properties
for index, element in dom_state.selector_map.items():
    print(f"Element {index}: visible={element.is_visible}, "
          f"interactive={element.is_interactive}, "
          f"in_viewport={element.is_in_viewport}")
```

#### Performance Issues
**Problem**: Slow DOM extraction on large pages
**Solutions:**
```python
# Use focused extraction
dom_state = await dom_service.get_clickable_elements(
    highlight_elements=False,  # Disable highlighting
    focus_element=specific_index  # Focus on specific element
)

# Monitor performance
import logging
logging.getLogger('browser_use.dom.service').setLevel(logging.DEBUG)
```

#### Memory Issues
**Problem**: High memory usage during DOM processing
**Solutions:**
```python
# Explicit cleanup
dom_state = await dom_service.get_clickable_elements()
# Process results
del dom_state  # Explicit deletion
gc.collect()   # Force garbage collection
```

#### Highlighting Issues
**Problem**: Element highlights not appearing or mispositioned
**Causes:**
- CSS conflicts
- Z-index issues
- Scroll position changes
- Dynamic content updates

**Solutions:**
```python
# Disable highlighting if not needed
dom_state = await dom_service.get_clickable_elements(
    highlight_elements=False
)

# Use specific focus highlighting
dom_state = await dom_service.get_clickable_elements(
    focus_element=target_index
)
```

### Debugging Tips

1. **Enable Debug Mode**: Use debug mode for detailed performance metrics
2. **Check JavaScript Console**: Monitor browser console for JavaScript errors
3. **Validate Element Properties**: Print element properties to understand classification
4. **Tree Visualization**: Use tree traversal to understand DOM structure
5. **Performance Profiling**: Monitor timing metrics for optimization opportunities

### Error Codes and Messages

| Error Type                    | Description                              | Solution                                        |
|-------------------------------|------------------------------------------|-------------------------------------------------|
| `JavaScriptExecutionError`    | JavaScript code failed to execute        | Check page state and JavaScript capability      |
| `DOMParsingError`             | Failed to parse DOM tree                 | Verify page content and structure               |
| `ElementNotFoundError`        | Expected element not in selector map     | Check element properties and visibility         |
| `MemoryError`                 | Insufficient memory for DOM processing   | Implement memory management strategies          |
| `TimeoutError`                | DOM extraction timed out                 | Increase timeout or optimize extraction         |

### Performance Optimization Strategies

#### 1. Selective Extraction
```python
# Extract only specific element types
async def extract_buttons_only():
    dom_state = await dom_service.get_clickable_elements()
    buttons = [elem for elem in dom_state.selector_map.values()
               if elem.tag_name.lower() == 'button']
    return buttons
```

#### 2. Caching Strategy
```python
# Implement DOM state caching
class CachedDOMService:
    def __init__(self, dom_service: DomService):
        self.dom_service = dom_service
        self.cache = {}

    async def get_cached_elements(self, cache_key: str):
        if cache_key not in self.cache:
            self.cache[cache_key] = await self.dom_service.get_clickable_elements()
        return self.cache[cache_key]
```

#### 3. Memory Management
```python
# Implement memory-conscious processing
async def process_large_page():
    dom_state = await dom_service.get_clickable_elements(
        highlight_elements=False  # Reduce memory usage
    )

    # Process in chunks
    chunk_size = 100
    for i in range(0, len(dom_state.selector_map), chunk_size):
        chunk = dict(list(dom_state.selector_map.items())[i:i+chunk_size])
        # Process chunk
        del chunk  # Explicit cleanup

    del dom_state
    gc.collect()
```

---

**Documentation Version**: 1.0
**Last Updated**: July 2025
**Maintained by**: VizCheck Development Team
