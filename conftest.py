# conftest.py - Pytest configuration for VizCheck
# This file makes all tests discoverable and fixes import issues

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Add browser_use to Python path for its internal imports
browser_use_path = project_root / "browser_use"
if browser_use_path.exists():
    sys.path.insert(0, str(browser_use_path.parent))

# Set up environment for tests
os.environ.setdefault("PYTEST_RUNNING", "1")

# Optional: Set test environment variables
os.environ.setdefault("TESTING", "true")
os.environ.setdefault("LOG_LEVEL", "INFO")
