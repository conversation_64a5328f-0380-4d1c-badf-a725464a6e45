import subprocess
import os
from playwright.sync_api import sync_playwright
import time

def is_chrome_running():
    tasklist_output = subprocess.run('tasklist /FI "IMAGENAME eq chrome.exe"', shell=True, capture_output=True, text=True)
    return "chrome.exe" in tasklist_output.stdout

def close_chrome():
    try:
        with sync_playwright() as p:
            browser = p.chromium.connect_over_cdp("http://localhost:9222")

            context = browser.contexts[0]
            pages = context.pages

            for page in pages:
                page.close()

            browser.close()
    except Exception as e:
        print(f"Error:{e}")

    if is_chrome_running():
        print("Chrome is running. Killing...")
        user_data_dir = "C:/temp/chrome-profile"
        find_command = f'wmic process where "CommandLine like \'%%{user_data_dir}%%\' and name=\'chrome.exe\'" get ProcessId'
        result = subprocess.check_output(find_command, shell=True).decode()
        pids = [line.strip() for line in result.splitlines() if line.strip().isdigit()]
        for pid in pids:
            print(f"Killing Chrome PID {pid}...")
            subprocess.call(f"taskkill /F /T /PID {pid}", shell=True)
        print("Chrome has been terminated.")
    else:
        print("Chrome is not running.")

def launch_chrome():
    chrome_command = r'"C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir="C:/temp/chrome-profile"'
    chrome_process = subprocess.Popen(chrome_command, shell=True)
    print(chrome_process.pid)

def start_android_emulator(avd_name="Pixel_9_Pro"):
    emulator_path = os.path.expanduser("~\\AppData\\Local\\Android\\Sdk\\emulator\\emulator.exe")
    
    if not os.path.exists(emulator_path):
        raise FileNotFoundError("emulator.exe not found. Please check your Android SDK path.")
    
    args = [
        emulator_path,
        "-avd", avd_name,
        "-gpu", "host"
    ]
    
    subprocess.Popen(args, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
    print(f"Emulator '{avd_name}' is starting...")

    print("Waiting for emulator to boot...")
    while True:
        try:
            output = subprocess.check_output(["adb", "shell", "getprop", "sys.boot_completed"], timeout=5)
            if output.strip() == b"1":
                break
        except subprocess.SubprocessError:
            pass
        time.sleep(2)

def launch_chrome_on_emulator():
    print("Launching Chrome on the emulator...")
    subprocess.run([
        "adb", "shell", "am", "start",
        "-n", "com.android.chrome/com.google.android.apps.chrome.Main"
    ], check=True)


def forward_remote_debugging_port():
    print("Forwarding port 9222 for remote debugging...")
    subprocess.run([
        "adb", "forward", "tcp:9222", "localabstract:chrome_devtools_remote"
    ], check=True)
    print("Remote debugging available at http://localhost:9222")

def setup_emulator_chrome_debugging():
    start_android_emulator()
    launch_chrome_on_emulator()
    time.sleep(5)
    forward_remote_debugging_port()