from azure.devops.connection import Connection
from msrest.authentication import BasicAuthentication
from azure.devops.v7_1.work_item_tracking.models import JsonPatchOperation
import os

class ADOWorkItemManager:
    """
    Manages interactions with Azure DevOps work items.
    Requires AZURE_DEVOPS_PAT environment variable to be set.
    """
    def __init__(self, organization_url: str="", project_name: str=""):
        """
        Initializes the WorkItemManager.

        Args:
            organization_url (str): The URL of your Azure DevOps organization
                                    (e.g., "https://dev.azure.com/your-organization-name").
            project_name (str): The name of the Azure DevOps project.
        """
        self.organization_url = organization_url
        self.project_name = project_name
        self.personal_access_token = "CnWzapX0Q5gfgLWYPZVpEXmyto7AjQKZGrgP43C9ZxHwHXqyhayMJQQJ99BFACAAAAASf5FgAAASAZDO3KPH"

        if not self.personal_access_token:
            raise ValueError(
                "AZURE_DEVOPS_PAT environment variable not set. "
                "Please set your Azure DevOps Personal Access Token."
            )

        self.connection = self._establish_connection()
        self.work_item_tracking_client = self.connection.clients.get_work_item_tracking_client()
        print(f"Connected to Azure DevOps organization: {self.organization_url}, project: {self.project_name}")

    def _establish_connection(self):
        """Establishes and returns a connection to Azure DevOps."""
        try:
            credentials = BasicAuthentication('', self.personal_access_token)
            return Connection(base_url=self.organization_url, creds=credentials)
        except Exception as e:
            raise ConnectionError(f"Failed to connect to Azure DevOps: {e}")

    def create_work_item(self, work_item_type: str, title: str, description: str = None, fields: dict = None):
        """
        Creates a new work item.

        Args:
            work_item_type (str): The type of work item (e.g., "Bug", "User Story", "Task").
            title (str): The title of the work item.
            description (str, optional): The description of the work item. Defaults to None.
            fields (dict, optional): A dictionary of additional fields to set.
                                     Keys should be the field reference names (e.g., "System.AssignedTo").
                                     Defaults to None.

        Returns:
            WorkItem: The created WorkItem object, or None if creation fails.
        """
        patch_document = [
            JsonPatchOperation(op="add", path="/fields/System.Title", value=title),
            JsonPatchOperation(op="add", path="/fields/System.Description", value=description or "")
        ]

        if fields:
            for field_name, field_value in fields.items():
                patch_document.append(
                    JsonPatchOperation(op="add", path=f"/fields/{field_name}", value=field_value)
                )

        try:
            new_work_item = self.work_item_tracking_client.create_work_item(
                document=patch_document,
                project=self.project_name,
                type=work_item_type
            )
            print(f"Created {work_item_type} '{title}' with ID: {new_work_item.id}")
            return new_work_item
        except Exception as e:
            print(f"Error creating work item: {e}")
            return None

    def get_work_item(self, work_item_id: int, expand_fields: bool = False):
        """
        Retrieves a work item by its ID.

        Args:
            work_item_id (int): The ID of the work item.
            expand_fields (bool): If True, expands all fields. Defaults to False.

        Returns:
            WorkItem: The WorkItem object, or None if not found.
        """
        try:
            # The 'expand' parameter can be used to control what fields are returned.
            # WorkItemExpand.Fields to get all fields.
            work_item = self.work_item_tracking_client.get_work_item(
                id=work_item_id,
                project=self.project_name,
                expand='Fields' if expand_fields else None
            )
            print(f"Retrieved Work Item ID: {work_item.id}, Title: {work_item.fields.get('System.Title')}")
            return work_item
        except Exception as e:
            print(f"Error retrieving work item {work_item_id}: {e}")
            return None

    def update_work_item(self, work_item_id: int, fields_to_update: dict):
        """
        Updates an existing work item.

        Args:
            work_item_id (int): The ID of the work item to update.
            fields_to_update (dict): A dictionary where keys are field reference names
                                     (e.g., "System.State", "System.AssignedTo") and values are the new values.

        Returns:
            WorkItem: The updated WorkItem object, or None if update fails.
        """
        patch_document = []
        for field_name, field_value in fields_to_update.items():
            patch_document.append(
                JsonPatchOperation(op="replace", path=f"/fields/{field_name}", value=field_value)
            )

        try:
            updated_work_item = self.work_item_tracking_client.update_work_item(
                document=patch_document,
                id=work_item_id,
                project=self.project_name
            )
            print(f"Updated Work Item ID: {work_item_id}, Title: {updated_work_item.fields.get('System.Title')}")
            return updated_work_item
        except Exception as e:
            print(f"Error updating work item {work_item_id}: {e}")
            return None

    def query_work_items_by_wiql(self, wiql_query: str):
        """
        Queries work items using a Work Item Query Language (WIQL) string.

        Args:
            wiql_query (str): The WIQL query string (e.g., "SELECT [System.Id], [System.Title] FROM WorkItems WHERE [System.State] = 'New'").

        Returns:
            list[WorkItemReference]: A list of WorkItemReference objects, or None if query fails.
        """
        try:
            query_result = self.work_item_tracking_client.query_by_wiql(
                wiql={"query": wiql_query}
            )
            if query_result and query_result.work_items:
                print(f"Found {len(query_result.work_items)} work items matching the query.")
                return query_result.work_items
            else:
                print("No work items found for the given query.")
                return []
        except Exception as e:
            print(f"Error executing WIQL query: {e}")
            return None

# --- Example Usage ---
if __name__ == "__main__":
    # --- Configuration ---
    # Replace with your actual organization URL and project name
    my_organization_url = "https://dev.azure.com/ascendionava" # e.g., "https://dev.azure.com/fabrikam"
    my_project_name = "PixelShiftStudio" # e.g., "MyAgileProject"

    # Ensure AZURE_DEVOPS_PAT environment variable is set before running
    # On Linux/macOS: export AZURE_DEVOPS_PAT="your_pat_here"
    # On Windows: set AZURE_DEVOPS_PAT=your_pat_here

    try:
        # 1. Initialize the manager
        ado_manager = ADOWorkItemManager(my_organization_url, my_project_name)

        # 2. Create a new Bug
        print("\n--- Creating a new Bug ---")
        new_bug_title = "Login button sometimes unresponsive"
        new_bug_description = "Users report that the login button occasionally does not respond on first click."
        bug_fields = {
            "System.AssignedTo": "<EMAIL>", # Optional: Assign to a user
            "Microsoft.VSTS.Common.Severity": "3 - Medium"
        }
        created_bug = ado_manager.create_work_item(
            work_item_type="Task",
            title=new_bug_title,
            description=new_bug_description,
            fields=bug_fields
        )

        if created_bug:
            bug_id = created_bug.id
            print(f"New Bug created with ID: {bug_id}")

            # 3. Get the created Bug
            print(f"\n--- Retrieving Work Item {bug_id} ---")
            retrieved_bug = ado_manager.get_work_item(bug_id, expand_fields=True)
            if retrieved_bug:
                print(f"Title: {retrieved_bug.fields.get('System.Title')}")
                print(f"State: {retrieved_bug.fields.get('System.State')}")
                print(f"Assigned To: {retrieved_bug.fields.get('System.AssignedTo', 'Unassigned')}")

            # 4. Update the Bug's state and assigned person
            print(f"\n--- Updating Work Item {bug_id} ---")
            update_fields = {
                "System.State": "Doing",
                "System.AssignedTo": "<EMAIL>"
            }
            updated_bug = ado_manager.update_work_item(bug_id, update_fields)
            if updated_bug:
                print(f"Updated State: {updated_bug.fields.get('System.State')}")
                print(f"Updated Assigned To: {updated_bug.fields.get('System.AssignedTo', 'Unassigned')}")

            # 5. Query work items
            print("\n--- Querying Work Items ---")
            wiql_query = f"SELECT [System.Id], [System.Title], [System.State] FROM WorkItems WHERE [System.WorkItemType] = 'Task' AND [System.State] = 'Doing' AND [System.AreaPath] = 'PixelShiftStudio' ORDER BY [System.Id] DESC"
            active_bugs = ado_manager.query_work_items_by_wiql(wiql_query)
            if active_bugs:
                print("Active Tasks:")
                for wi_ref in active_bugs:
                    # Note: query_by_wiql returns WorkItemReference, not full WorkItem.
                    # You might need to get_work_item for full details.
                    print(f"  - ID: {wi_ref.id}, URL: {wi_ref.url}")
                    full_wi = ado_manager.get_work_item(wi_ref.id, expand_fields=True)
                    if full_wi:
                        print(f"    Title: {full_wi.fields.get('System.Title')}, State: {full_wi.fields.get('System.State')}")

            # 6. Query all work items
            print("\n--- Querying All Task Work Items ---")
            wiql_query = f"SELECT [System.Id], [System.Title], [System.State] FROM WorkItems WHERE [System.WorkItemType] = 'Task' AND [System.AreaPath] = 'PixelShiftStudio' ORDER BY [System.Id] DESC"
            active_bugs = ado_manager.query_work_items_by_wiql(wiql_query)
            if active_bugs:
                print("Active Tasks:")
                for wi_ref in active_bugs:
                    # Note: query_by_wiql returns WorkItemReference, not full WorkItem.
                    # You might need to get_work_item for full details.
                    print(f"  - ID: {wi_ref.id}, URL: {wi_ref.url}")
                    full_wi = ado_manager.get_work_item(wi_ref.id, expand_fields=True)
                    if full_wi:
                        print(f"    Title: {full_wi.fields.get('System.Title')}, State: {full_wi.fields.get('System.State')}")

    except (ValueError, ConnectionError) as e:
        print(f"Setup Error: {e}")
    except Exception as e:
        print(f"An unexpected error occurred during execution: {e}")
