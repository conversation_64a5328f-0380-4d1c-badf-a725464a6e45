[tool:pytest]
# Pytest configuration for VizCheck

# Test discovery patterns
python_files = test_*.py *_test.py tests.py
python_classes = Test* *Tests
python_functions = test_*

# Test directories to search
testpaths = tests browser_use

# Minimum version
minversion = 6.0

# Add options
addopts = -v --tb=short --strict-markers --disable-warnings --color=yes

# Markers for test categorization
markers =
    unit: Unit tests
    integration: Integration tests
    browser: Browser-related tests
    slow: Slow running tests
    api: API tests
    core: Core functionality tests

# Filter warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:gradio.*
    ignore::UserWarning:langchain.*
