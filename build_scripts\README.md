# WizCheck Build Scripts

This folder contains automated build scripts for WizCheck that handle the complete build process.

## 📁 Build Scripts Organization

All build-related files are now organized in the `build_scripts/` directory:

```
build_scripts/
├── build_wizcheck.py       # Main Python build script
├── build_wizcheck.bat      # Windows batch script
├── build_wizcheck.ps1      # PowerShell script
├── build_wizcheck.sh       # Unix/Linux shell script
├── specs/                  # 📁 PyInstaller spec files
│   ├── app.spec           #   Default spec file
│   ├── console.spec       #   Console version
│   └── debug.spec         #   Debug version
├── runtime_assets/        # Runtime assets to copy
└── README.md              # This file
```

**Benefits**: All build configuration and scripts in one organized location.

## 📋 Multiple Spec File Support

The build system supports multiple PyInstaller spec files for different build configurations:

### **Available Spec Files:**
- **`app.spec`** - Default build configuration
- **`console.spec`** - Console-optimized version
- **`debug.spec`** - Debug build with symbols

### **Spec File Selection:**
```bash
# List available spec files
python build_wizcheck.py --list-specs

# Use specific spec file
python build_wizcheck.py --spec console
python build_wizcheck.py --spec debug.spec  # .spec extension optional
```

### **Creating Custom Spec Files:**
1. Copy an existing spec file in `specs/` directory
2. Modify the configuration as needed
3. Change the `name=` parameter for different executable names
4. Use with `--spec your_custom_spec`

## Quick Start

### Windows Users
```cmd
cd build_scripts
build_wizcheck.bat                    # Build with default spec (app.spec)
build_wizcheck.bat patch              # Increment patch version
build_wizcheck.bat console            # Use console.spec
build_wizcheck.bat minor debug        # Increment minor, use debug.spec
build_wizcheck.bat list               # List available spec files
```

### PowerShell Users (Recommended for Windows)
```powershell
cd build_scripts
.\build_wizcheck.ps1                        # Build with default spec
.\build_wizcheck.ps1 -Increment patch       # Increment patch version
.\build_wizcheck.ps1 -Spec console          # Use console.spec
.\build_wizcheck.ps1 -Spec debug -Increment minor  # Use debug.spec, increment minor
.\build_wizcheck.ps1 -ListSpecs             # List available spec files
```

### Unix/Linux/macOS Users
```bash
cd build_scripts
chmod +x build_wizcheck.sh
./build_wizcheck.sh                   # Build with default spec
./build_wizcheck.sh patch             # Increment patch version
./build_wizcheck.sh --spec console    # Use console.spec
./build_wizcheck.sh --spec debug minor # Use debug.spec, increment minor
./build_wizcheck.sh list              # List available spec files
```

### Cross-Platform (Python)
```bash
cd build_scripts
python build_wizcheck.py                        # Build with default spec
python build_wizcheck.py --increment patch      # Increment patch version
python build_wizcheck.py --spec console         # Use console.spec
python build_wizcheck.py --spec debug --increment minor  # Use debug.spec, increment minor
python build_wizcheck.py --list-specs           # List available spec files
```

## What These Scripts Do

1. ✅ Check existing files in the `dist` folder
2. ✅ **Create organized output directories** (`builds/`, `archives/`, `backups/`)
3. ✅ **Backup existing builds** with runtime assets to `backups/` folder
4. ✅ **Smart app.spec updates** with proper version handling
5. ✅ Run PyInstaller with modified app.spec
6. ✅ Verify successful build creation (versioned exe in dist)
7. ✅ Read current version from `config/config.ini`
8. ✅ **Move existing versioned folders** to `backups/` (with timestamp if needed)
9. ✅ Create versioned folders in `builds/` directory
10. ✅ Copy executable + runtime assets to versioned folder
10. ✅ Create zip archives in `archives/` directory
11. ✅ **Remove main executable** from dist after copying
12. ✅ **Restore original app.spec** file
13. ✅ Clean up old builds and archives (keeps 3 most recent)
14. ✅ **Optionally increment version in config.ini for next build**
15. ✅ Provide comprehensive logging and error handling

## Enhanced Backup System

**Before building**, the script preserves existing builds:

### 1. Executable Backup
If an executable exists:
1. ✅ Creates backup folder in `backups/`: `WizCheck_backup` (or `WizCheck_backup_YYYYMMDD_HHMMSS` if exists)
2. ✅ Copies existing executable to backup folder
3. ✅ Copies all runtime assets to backup folder

### 2. Versioned Folder Backup
If a versioned folder already exists:
1. ✅ **Moves existing folder** to `backups/`: `WizCheck_0.1.0` → `backups/WizCheck_0.1.0`
2. ✅ **Handles conflicts**: If backup exists, appends timestamp: `WizCheck_0.1.0_20250111_143022`
3. ✅ **Preserves complete builds**: No data loss, everything moved to backup

**Benefits**: No deletion, complete preservation, organized backup structure

## Smart app.spec Version Handling

The build script intelligently handles version updates in `app.spec`:

### ✅ **Correct Behavior:**
```
Build 1: WizCheck → WizCheck_0.1.0
Build 2: WizCheck_0.1.0 → WizCheck_0.1.1  (extracts base name)
Build 3: WizCheck_0.1.1 → WizCheck_0.2.0  (extracts base name)
```

### ❌ **Avoided Problem:**
```
Build 1: WizCheck → WizCheck_0.1.0
Build 2: WizCheck_0.1.0 → WizCheck_0.1.0_0.1.1  (WRONG!)
```

**How it works:**
1. **Extract base name**: `WizCheck_0.1.0` → `WizCheck`
2. **Apply new version**: `WizCheck` + `0.1.1` → `WizCheck_0.1.1`
3. **Always consistent**: Base name never gets corrupted with multiple versions

## Version Increment Behavior

**Important**: Version increment updates the config file but uses the **current** version for the build:

- **Current version in config**: `0.1.0`
- **Run with `--increment patch`**:
  - ✅ Build creates: `WizCheck_0.1.0/` and `WizCheck_0.1.0.zip`
  - ✅ Executable built as: `WizCheck_0.1.0.exe`
  - ✅ Config updated to: `0.1.1` (for next build)
- **Next build** will use version `0.1.1`

## Prerequisites

- Python installed and in PATH
- PyInstaller installed: `pip install pyinstaller`
- Run from the `build_scripts` directory
- Ensure spec files exist in `build_scripts/specs/` directory
- Ensure `config/config.ini` exists in parent directory

## Organized Output Structure

After successful build, outputs are organized into separate directories:

```
../dist/
├── builds/                      # 📁 Versioned Build Folders
│   ├── WizCheck_0.1.0/          #   Current build folder
│   │   ├── WizCheck_0.1.0.exe   #   - Versioned executable
│   │   ├── assets/              #   - Runtime assets
│   │   │   ├── html/            #   - HTML files
│   │   │   └── images/          #   - Image files
│   │   └── config/              #   - Configuration files
│   │       └── config.ini       #   - Config file
│   ├── WizCheck_0.0.9/          #   Previous build
│   └── WizCheck_0.0.8/          #   Older build
├── archives/                    # 📦 Zip Archives
│   ├── WizCheck_0.1.0.zip       #   Current build archive
│   ├── WizCheck_0.0.9.zip       #   Previous archive
│   └── WizCheck_0.0.8.zip       #   Older archive
└── backups/                     # 🔄 Previous Build Backups
    ├── WizCheck_backup/         #   Previous executable backup
    │   ├── WizCheck.exe         #   - Previous executable
    │   ├── assets/              #   - Previous runtime assets
    │   └── config/              #   - Previous config
    ├── WizCheck_0.1.0/          #   Moved versioned folder
    │   ├── WizCheck_0.1.0.exe   #   - Previous versioned build
    │   ├── assets/              #   - Previous runtime assets
    │   └── config/              #   - Previous config
    └── WizCheck_0.1.0_20250111_143022/  # Timestamped moved folder
```

## Benefits of Organized Structure

✅ **Clean Separation**: Each type of output in its own directory
✅ **Easy Navigation**: Find builds, archives, or backups quickly
✅ **Better Management**: Cleanup operates on each directory independently
✅ **Scalable**: Handles many builds without cluttering
✅ **Clear Purpose**: Directory names indicate content type

**Note**: Main executable is removed from dist root after copying to versioned folder.

## Troubleshooting

If you get errors:
1. Make sure you're in the `build_scripts` directory
2. Check that Python and PyInstaller are installed
3. Verify spec files exist: `python build_wizcheck.py --list-specs`
4. Verify `../config/config.ini` exists in parent directory
5. Check the detailed logs for specific error messages

### Spec File Issues:
```bash
# List available spec files
python build_wizcheck.py --list-specs

# If no spec files found, create the specs directory
mkdir specs

# Copy your .spec files to the specs/ directory
```

For more detailed documentation, see `../BUILD_README.md`.
