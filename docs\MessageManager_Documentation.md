# MessageManager Documentation

## Overview

The MessageManager is a core component of the VizCheck browser automation system that handles conversation flow between the AI agent and the language model. It manages message history, token counting, memory optimization, and conversation state persistence.

## Table of Contents

1. [Architecture](#architecture)
2. [Core Components](#core-components)
3. [Key Features](#key-features)
4. [Usage Examples](#usage-examples)
5. [Configuration](#configuration)
6. [API Reference](#api-reference)
7. [Best Practices](#best-practices)
8. [Troubleshooting](#troubleshooting)

## Architecture

### Component Structure
```
MessageManager/
├── service.py         # Core MessageManager implementation
├── views.py           # Data models and message structures
├── utils.py           # Utility functions for message processing
└── tests.py           # Comprehensive test suite
```

### Message Flow
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Browser       │────│  MessageManager  │────│   LLM Model     │
│   State         │    │                  │    │   (GPT-4/etc)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │  Message History │
                       │  & Token Mgmt    │
                       └──────────────────┘
```

### High-Level Architecture

The MessageManager implements a sophisticated conversation management architecture that serves as the intelligent communication layer between AI agents and language models, providing advanced features like token optimization, memory management, and conversation persistence.

```
┌──────────────────────────────────────────────────────────────────────────────────┐
│                          VizCheck MessageManager System                          │
├──────────────────────────────────────────────────────────────────────────────────┤
│                              Agent Interface Layer                               │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │     Agent       │  │   Controller    │  │   Browser       │  │   External  │  │
│  │   Service       │  │    Actions      │  │    Context      │  │   Systems   │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────┘  │
├──────────────────────────────────────────────────────────────────────────────────┤
│                           Message Management Layer                               │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                         MessageManager Core                                 │ │
│  │  ┌──────────────┐  ┌─────────────┐  ┌─────────────┐  ┌────────────────────┐ │ │
│  │  │ Conversation │  │   Message   │  │    Token    │  │     Context        │ │ │
│  │  │ Orchestration│  │ Processing  │  │ Management  │  │   Management       │ │ │
│  │  └──────────────┘  └─────────────┘  └─────────────┘  └────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                        Message Processing                                   │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │   Message   │  │  Sensitive  │  │   Format    │  │     History         │ │ │
│  │  │ Validation  │  │    Data     │  │ Conversion  │  │   Optimization      │ │ │
│  │  │             │  │  Filtering  │  │             │  │                     │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
├──────────────────────────────────────────────────────────────────────────────────┤
│                            Memory Management Layer                               │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │     Token       │  │     Memory      │  │    History      │  │   State     │  │
│  │   Counting      │  │  Optimization   │  │   Trimming      │  │ Persistence │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────┘  │
├──────────────────────────────────────────────────────────────────────────────────┤
│                              LLM Interface Layer                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │     OpenAI      │  │   Anthropic     │  │     Google      │  │   Custom    │  │
│  │   (GPT-4/3.5)   │  │    (Claude)     │  │   (Gemini)      │  │   Models    │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────┘  │
├──────────────────────────────────────────────────────────────────────────────────┤
│                            Storage & Persistence Layer                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │   Conversation  │  │     Message     │  │     Token       │  │   Session   │  │
│  │    Storage      │  │     Cache       │  │    Metrics      │  │   State     │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────┘  │
└──────────────────────────────────────────────────────────────────────────────────┘
```

#### Architectural Principles

##### 1. Conversation-Centric Design
The architecture is built around conversation management:
- **Message Lifecycle**: Complete message lifecycle from creation to persistence
- **Context Preservation**: Maintains conversation context across interactions
- **History Management**: Intelligent history trimming and optimization
- **State Consistency**: Ensures consistent conversation state

##### 2. Token-Aware Architecture
Every component considers token limitations:
- **Real-time Counting**: Continuous token tracking for all messages
- **Predictive Management**: Anticipates token limits before they're reached
- **Intelligent Trimming**: Smart content reduction while preserving meaning
- **Optimization Strategies**: Multiple approaches to stay within limits

##### 3. Multi-Model Compatibility
Designed to work with various LLM providers:
- **Provider Abstraction**: Unified interface for different LLM providers
- **Format Adaptation**: Automatic message format conversion
- **Token Calculation**: Provider-specific token counting methods
- **Feature Support**: Adapts to different model capabilities

##### 4. Memory-Efficient Design
Optimized for long-running conversations:
- **Lazy Loading**: Load conversation history only when needed
- **Memory Cleanup**: Automatic cleanup of unused message data
- **Compression**: Efficient storage of conversation history
- **Garbage Collection**: Proactive memory management

#### Message Flow Architecture

```
Input Message → Validation → Processing → Token Analysis → History Update → LLM Preparation
     ↓             ↓           ↓             ↓              ↓              ↓
Message Object → Format Check → Content Filter → Token Count → History Append → Model Format
     ↓             ↓           ↓             ↓              ↓              ↓
Metadata → Structure Validation → Sensitive Data → Memory Check → State Update → API Call
```

**Detailed Flow:**
1. **Message Reception**: Incoming messages validated and structured
2. **Content Processing**: Sensitive data filtered and content optimized
3. **Token Analysis**: Token count calculated and memory impact assessed
4. **History Management**: Message added to history with intelligent trimming
5. **Format Preparation**: Messages formatted for specific LLM provider
6. **State Persistence**: Conversation state updated and optionally saved

#### Token Management Architecture

```
Token Tracking System
├── Real-time Counting
│   ├── Message Token Count
│   ├── History Token Sum
│   └── Projected Token Usage
├── Memory Optimization
│   ├── History Trimming
│   ├── Content Compression
│   └── Priority Preservation
├── Limit Management
│   ├── Soft Limits (Warning)
│   ├── Hard Limits (Action)
│   └── Emergency Limits (Truncation)
└── Provider Adaptation
    ├── OpenAI Token Counting
    ├── Anthropic Token Counting
    └── Custom Token Counting
```

##### Token Optimization Strategies

```
Token Limit Approach → Strategy → Implementation
        ↓                ↓           ↓
Soft Limit Reached → Warning → Log Token Usage
        ↓                ↓           ↓
Hard Limit Approaching → Trimming → Remove Oldest Messages
        ↓                ↓           ↓
Emergency Limit → Truncation → Aggressive Content Reduction
        ↓                ↓           ↓
Critical Limit → Reset → Preserve System + Recent Messages
```

#### Memory Management Architecture

```
Memory Hierarchy
├── Active Memory (Current Conversation)
│   ├── System Messages (Always Preserved)
│   ├── Task Context (High Priority)
│   ├── Recent Messages (Medium Priority)
│   └── Historical Messages (Low Priority)
├── Working Memory (Processing Buffer)
│   ├── Incoming Messages
│   ├── Processing Queue
│   └── Output Buffer
├── Persistent Memory (Storage)
│   ├── Conversation Files
│   ├── State Snapshots
│   └── Metadata Cache
└── Cache Memory (Performance)
    ├── Token Count Cache
    ├── Format Conversion Cache
    └── Provider Response Cache
```

##### Memory Optimization Flow

```
Memory Pressure Detection → Analysis → Strategy Selection → Implementation → Validation
         ↓                     ↓           ↓                ↓               ↓
    Token Count Check → Priority Analysis → Trimming Method → Execute Trim → Verify Limits
         ↓                     ↓           ↓                ↓               ↓
    Usage Monitoring → Content Scoring → Removal Strategy → Update History → Check Consistency
```

#### Data Flow Architecture

```
Agent Request → Message Creation → Content Processing → Token Management → LLM Communication → Response Processing
     ↓               ↓                   ↓                  ↓                    ↓                   ↓
Task Context → Message Object → Sensitive Filter → Token Count → Provider Format → Response Parse
     ↓               ↓                   ↓                  ↓                    ↓                   ↓
Browser State → Metadata → Content Validation → Memory Check → API Call → Result Extraction
```

**Processing Pipeline:**
1. **Input Processing**: Agent requests converted to message objects
2. **Content Validation**: Messages validated and sensitive data filtered
3. **Token Management**: Token counts calculated and memory optimized
4. **Provider Preparation**: Messages formatted for specific LLM provider
5. **LLM Communication**: Messages sent to language model
6. **Response Processing**: LLM responses parsed and integrated into conversation
7. **State Management**: Conversation state updated and persisted

#### Integration Architecture

##### Agent Integration
```
Agent Service
├── Task Definition → MessageManager.initialize_conversation()
├── Step Execution → MessageManager.add_message()
├── Action Results → MessageManager.add_browser_state()
├── Error Handling → MessageManager.add_error_context()
└── Completion → MessageManager.save_conversation()
```

##### LLM Provider Integration
```
MessageManager
├── OpenAI Integration
│   ├── GPT-4 Token Counting
│   ├── Message Format Conversion
│   └── Response Processing
├── Anthropic Integration
│   ├── Claude Token Counting
│   ├── Message Adaptation
│   └── Response Handling
├── Google Integration
│   ├── Gemini Token Counting
│   ├── Format Conversion
│   └── Response Processing
└── Custom Provider Support
    ├── Generic Token Interface
    ├── Configurable Formats
    └── Extensible Response Handling
```

##### Browser Context Integration
```
Browser State → Message Context → LLM Input
     ↓               ↓              ↓
DOM Elements → Structured Data → Context Injection
     ↓               ↓              ↓
Page State → Message Metadata → Conversation History
     ↓               ↓              ↓
Action Results → Response Context → Next Step Planning
```

## Core Components

### 1. MessageManager (service.py)

**Purpose**: Central orchestrator for conversation management

**Key Responsibilities**:
- Initialize conversation with system prompts and task context
- Manage message history with token tracking
- Handle token overflow and memory optimization
- Filter sensitive data from messages
- Coordinate between browser state and AI model

### 2. Message Models (views.py)

#### MessageMetadata
```python
class MessageMetadata(BaseModel):
    tokens: int = 0  # Token count for the message
```

#### ManagedMessage
```python
class ManagedMessage(BaseModel):
    message: BaseMessage      # LangChain message object
    metadata: MessageMetadata # Associated metadata
```

#### MessageHistory
```python
class MessageHistory(BaseModel):
    messages: list[ManagedMessage] = []
    current_tokens: int = 0
```

#### MessageManagerState
```python
class MessageManagerState(BaseModel):
    history: MessageHistory
    tool_id: int = 1  # Incremental ID for tool calls
```

### 3. Utility Functions (utils.py)

**Key Functions**:
- `extract_json_from_model_output()`: Parse JSON from AI responses
- `convert_input_messages()`: Adapt messages for different LLM models
- `save_conversation()`: Persist conversation history to files

## Key Features

### 1. **Intelligent Token Management**
- Real-time token counting for all messages
- Automatic message trimming when approaching token limits
- Proportional content reduction to stay within limits
- Preservation of critical system and task messages

### 2. **Sensitive Data Protection**
- Automatic detection and masking of sensitive information
- Placeholder replacement system (`<secret>key</secret>`)
- Configurable sensitive data patterns

### 3. **Memory Optimization**
- Selective message retention based on importance
- Automatic removal of redundant state messages
- Image content removal when token limits are exceeded

### 4. **Multi-Model Support**
- Compatible with OpenAI GPT models
- Support for Azure OpenAI deployments
- Anthropic Claude integration
- Model-specific message format adaptation

### 5. **Conversation Persistence**
- Save/load conversation history
- JSON serialization with LangChain compatibility
- Metadata preservation across sessions

## Usage Examples

### Basic Initialization
```python
from browser_use.agent.message_manager.service import MessageManager, MessageManagerSettings
from langchain_core.messages import SystemMessage

# Configure settings
settings = MessageManagerSettings(
    max_input_tokens=128000,
    estimated_characters_per_token=3,
    image_tokens=800,
    sensitive_data={"password": "secret123"}
)

# Initialize manager
manager = MessageManager(
    task="Navigate to Google and search for 'AI automation'",
    system_message=SystemMessage(content="You are a browser automation agent..."),
    settings=settings
)
```

### Adding Browser State
```python
from browser_use.browser.views import BrowserState
from browser_use.agent.views import ActionResult

# Create browser state
state = BrowserState(
    url="https://google.com",
    title="Google",
    element_tree=dom_tree,
    selector_map=selectors,
    tabs=tab_info
)

# Add state to conversation
manager.add_state_message(state, use_vision=True)

# Add with action results
results = [ActionResult(extracted_content="Search completed", include_in_memory=True)]
manager.add_state_message(state, result=results)
```

### Handling Model Output
```python
from browser_use.agent.views import AgentOutput, AgentBrain

# Create agent output
output = AgentOutput(
    current_state=AgentBrain(
        evaluation_previous_goal="Success - navigated to Google",
        memory="Completed navigation step 1/5",
        next_goal="Enter search query"
    ),
    action=[{"input_text": {"index": 1, "text": "AI automation"}}]
)

# Add to conversation
manager.add_model_output(output)
```

### Token Management
```python
# Check current token usage
messages = manager.get_messages()
current_tokens = manager.state.history.current_tokens
max_tokens = manager.settings.max_input_tokens

print(f"Using {current_tokens}/{max_tokens} tokens")

# Automatic trimming when limits exceeded
if current_tokens > max_tokens:
    manager.cut_messages()  # Automatically trims content
```

## Configuration

### MessageManagerSettings

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `max_input_tokens` | int | 128000 | Maximum tokens allowed in conversation |
| `estimated_characters_per_token` | int | 3 | Character-to-token ratio estimate |
| `image_tokens` | int | 800 | Token cost for images |
| `include_attributes` | list[str] | [] | HTML attributes to include in DOM |
| `message_context` | str | None | Additional context for tasks |
| `sensitive_data` | dict | None | Key-value pairs of sensitive data |
| `available_file_paths` | list[str] | None | Available file paths for uploads |

### Environment Integration
```python
# Integration with VizCheck config
from config import Config

config = Config()
settings = MessageManagerSettings(
    max_input_tokens=config.get_max_tokens(),
    sensitive_data=config.get_sensitive_data()
)
```

## API Reference

### MessageManager Methods

#### Core Methods
- `__init__(task, system_message, settings, state)`: Initialize manager
- `get_messages() -> List[BaseMessage]`: Get current message list
- `add_state_message(state, result, step_info, use_vision)`: Add browser state
- `add_model_output(model_output)`: Add AI model response
- `add_new_task(new_task)`: Update task during execution

#### Utility Methods
- `add_plan(plan, position)`: Add planning information
- `add_tool_message(content)`: Add tool execution results
- `cut_messages()`: Trim messages to fit token limits
- `_filter_sensitive_data(message)`: Remove sensitive information
- `_count_tokens(message)`: Calculate message token count

### MessageHistory Methods
- `add_message(message, metadata, position)`: Add message with metadata
- `get_messages() -> list[BaseMessage]`: Retrieve all messages
- `get_total_tokens() -> int`: Get total token count
- `remove_oldest_message()`: Remove oldest non-system message
- `remove_last_state_message()`: Remove last state message

## Best Practices

### 1. **Token Management**
```python
# Monitor token usage regularly
def check_token_usage(manager):
    usage = manager.state.history.current_tokens
    limit = manager.settings.max_input_tokens
    if usage > limit * 0.8:  # 80% threshold
        logger.warning(f"Approaching token limit: {usage}/{limit}")
```

### 2. **Sensitive Data Handling**
```python
# Configure sensitive data patterns
sensitive_data = {
    "api_key": os.getenv("API_KEY"),
    "password": os.getenv("PASSWORD"),
    "email": "<EMAIL>"
}

settings = MessageManagerSettings(sensitive_data=sensitive_data)
```

### 3. **Memory Optimization**
```python
# Use include_in_memory strategically
important_result = ActionResult(
    extracted_content="Critical data found",
    include_in_memory=True  # Keep in conversation history
)

temporary_result = ActionResult(
    extracted_content="Navigation step completed",
    include_in_memory=False  # Don't clutter history
)
```

### 4. **Error Handling**
```python
try:
    manager.add_state_message(state)
except ValueError as e:
    if "Max token limit reached" in str(e):
        # Handle token overflow
        manager.cut_messages()
        manager.add_state_message(state)
    else:
        raise
```

## Troubleshooting

### Common Issues

#### 1. Token Limit Exceeded
**Symptoms**: `ValueError: Max token limit reached`

**Solutions**:
- Increase `max_input_tokens` in settings
- Reduce `include_attributes` list
- Use `include_in_memory=False` for non-critical results
- Implement more aggressive message trimming

#### 2. Memory Leaks
**Symptoms**: Continuously growing memory usage

**Solutions**:
- Regular cleanup of old messages
- Monitor `current_tokens` count
- Use `remove_oldest_message()` periodically

#### 3. Sensitive Data Exposure
**Symptoms**: Sensitive information in logs/outputs

**Solutions**:
- Configure `sensitive_data` properly
- Test filtering with sample data
- Review conversation exports

#### 4. Message Format Errors
**Symptoms**: LLM parsing failures

**Solutions**:
- Validate message structure before adding
- Use proper LangChain message types
- Check JSON serialization compatibility

### Debug Mode
```python
import logging
logging.getLogger('browser_use.agent.message_manager').setLevel(logging.DEBUG)

# Enable detailed token counting logs
manager.get_messages()  # Will log token details
```

### Performance Monitoring
```python
# Track message manager performance
from browser_use.utils import time_execution_sync

@time_execution_sync('message_processing')
def process_messages(manager, state):
    manager.add_state_message(state)
    return manager.get_messages()
```

---

**Document Version**: 1.0  
**Last Updated**: July 2025  
**Component Version**: VizCheck 2.0.0
