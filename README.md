# VizCheck - AI-Powered Browser Automation Platform

[![Python 3.11+](https://img.shields.io/badge/python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Version](https://img.shields.io/badge/version-2.0.0-orange.svg)](https://dev.azure.com/ascendionava/AWE/_git/vizcheck)

VizCheck is an intelligent browser automation platform designed for Xbox Cloud Gaming testing and general web automation tasks. It combines the power of Azure OpenAI GPT-4 with Playwright browser automation to execute complex tasks using natural language instructions.

## 🚀 Features

- **🤖 AI-Driven Automation**: Use natural language to describe complex browser tasks
- **🎮 Xbox Cloud Gaming Specialized**: Optimized for gaming platform testing and interaction
- **📊 Visual Reporting**: Generate comprehensive PDF reports with screenshots and execution details
- **🎬 Session Recording**: Create animated GIFs of automation sessions
- **🌐 Web Interface**: User-friendly Gradio-based interface
- **⚙️ Configurable**: Flexible configuration system with environment variable support
- **🔄 Real-time Monitoring**: Live feedback during task execution

## 📋 Table of Contents

- [Installation](#installation)
- [Quick Start](#quick-start)
- [Configuration](#configuration)
- [Usage](#usage)
- [Examples](#examples)
- [Project Structure](#project-structure)
- [Development](#development)
- [Troubleshooting](#troubleshooting)
- [Contributing](#contributing)
- [License](#license)

## 🛠️ Installation

### Prerequisites

- Python 3.11 or higher
- Google Chrome or Chromium browser
- Azure OpenAI account with API access

### Step 1: Clone the Repository

```bash
git clone https://dev.azure.com/ascendionava/AWE/_git/vizcheck
cd vizcheck
```

### Step 2: Create Virtual Environment

```bash
python -m venv venv

# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate
```

### Step 3: Install Dependencies

```bash
pip install -r requirements.txt
playwright install chromium
```

### Step 4: Configure Environment

1. Copy the example environment file:
```bash
cp example.env .env
```

2. Edit `.env` with your Azure OpenAI credentials:
```bash
AZURE_API_KEY=your_azure_openai_api_key
AZURE_DEPLOYMENT=gpt-4o
AZURE_ENDPOINT=https://your-endpoint.openai.azure.com/
OPENAI_API_VERSION=2024-05-01-preview
```

## ⚡ Quick Start

1. **Start the application**:
```bash
python app.py
```

2. **Open your browser** and navigate to the displayed URL (typically `http://localhost:7860`)

3. **Enter a task** in natural language, for example:
   - "Navigate to Google and search for 'Xbox Cloud Gaming'"
   - "Go to Xbox.com and find the latest games"
   - "Test the login functionality on the gaming portal"

4. **Click "Execute Task"** and watch the AI automate your browser

5. **View results** in the generated PDF report

## ⚙️ Configuration

### Environment Variables (.env)

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `AZURE_API_KEY` | Azure OpenAI API key | Yes | - |
| `AZURE_DEPLOYMENT` | Azure OpenAI deployment name | No | gpt-4o |
| `AZURE_ENDPOINT` | Azure OpenAI endpoint URL | No | - |
| `OPENAI_API_VERSION` | OpenAI API version | No | 2024-05-01-preview |
| `LOGGING_LEVEL` | Application logging level | No | INFO |

### Configuration File (config/config.ini)

```ini
[settings]
max_num_of_steps = 45          # Maximum automation steps
start_url = https://google.com  # Default starting URL

[App]
app_name = VizCheck
version = 2.0.0
description = A browser automation app.
```

## 📖 Usage

### Basic Task Execution

1. **Simple Navigation**:
   ```
   Go to YouTube and search for "Xbox gameplay videos"
   ```

2. **Form Interaction**:
   ```
   Navigate to the contact form and fill it with test data
   ```

3. **Gaming-Specific Tasks**:
   ```
   Go to Xbox Cloud Gaming, find Forza Horizon, and start playing for 5 minutes
   ```

### Advanced Features

- **Multi-step Tasks**: Describe complex workflows in natural language
- **Data Extraction**: Ask the AI to extract specific information from pages
- **Gaming Controls**: Use Xbox controller simulation for cloud gaming
- **Report Generation**: Automatic PDF reports with execution details

### Task Examples

#### Web Testing
```
Navigate to the e-commerce site, add 3 items to cart, and proceed to checkout
```

#### Gaming Automation
```
Launch Minecraft from Xbox Cloud Gaming and play for 10 minutes using keyboard controls
```

#### Data Collection
```
Go to the news website and extract the titles of the top 5 articles
```

## 📁 Project Structure

```
vizcheck/
├── app.py                    # Main application entry point
├── config.py                 # Configuration management
├── requirements.txt          # Python dependencies
├── README.md                 # This file
├── config/
│   └── config.ini           # Application settings
├── browser_use/             # Browser automation engine
│   ├── agent/               # AI agent implementation
│   ├── browser/             # Browser management
│   ├── controller/          # Action controllers
│   └── dom/                 # DOM manipulation
├── assets/
│   ├── html/                # Report templates
│   └── images/              # Static assets
├── outputs/                 # Generated reports and recordings
├── docs/                    # Complete documentation
└── dist/                    # Built executables
```

## 🔧 Development

### Setting Up Development Environment

1. **Install development dependencies**:
```bash
pip install -r requirements.txt
pip install pytest black flake8  # Additional dev tools
```

2. **Run tests**:
```bash
pytest browser_use/agent/tests.py
```

3. **Code formatting**:
```bash
black app.py config.py
```

### Building Executable

```bash
pyinstaller app.spec
```

The executable will be created in the `dist/` directory.

### Key Development Files

- `app.py`: Main application logic and Gradio interface
- `config.py`: Configuration management with singleton pattern
- `browser_use/agent/service.py`: Core AI agent implementation
- `browser_use/browser/browser.py`: Browser automation wrapper
- `browser_use/controller/service.py`: Action execution engine

## 🐛 Troubleshooting

### Common Issues

#### Chrome Connection Failed
```bash
Error: Could not connect to Chrome on port 9222
```
**Solution**: Ensure Chrome is installed and port 9222 is available.

#### Azure OpenAI Authentication Error
```bash
Error: Invalid API key or endpoint
```
**Solution**: Verify your `.env` file contains correct Azure OpenAI credentials.

#### Task Execution Timeout
```bash
Error: Agent exceeded maximum steps
```
**Solution**: Increase `max_num_of_steps` in `config/config.ini`.

### Debug Mode

Enable detailed logging by setting in `config/config.ini`:
```ini
[App]
debug = true
```

### Getting Help

1. Check the [Complete Documentation](docs/) for detailed guides
2. Review the [Troubleshooting Guide](docs/VizCheck_Document.md#troubleshooting)
3. Review application logs in the console
4. Ensure all prerequisites are properly installed
5. Verify network connectivity for Azure OpenAI API calls

## 🤝 Contributing

We welcome contributions! Please follow these steps:

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow PEP 8 style guidelines
- Add tests for new features
- Update documentation as needed
- Ensure all tests pass before submitting PR

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Playwright](https://playwright.dev/) for browser automation
- [LangChain](https://langchain.com/) for AI integration
- [Gradio](https://gradio.app/) for the web interface
- [Azure OpenAI](https://azure.microsoft.com/en-us/products/ai-services/openai-service) for AI capabilities

## 📞 Support

For support and questions:
- 📖 **Complete Documentation**: [Documentation Folder](docs/)
- 📋 **Project Document**: [VizCheck Document](docs/VizCheck_Document.md)
- 🤖 **Agent System**: [Agent Documentation](docs/Agent_Documentation.md)
- 💬 **Message Management**: [MessageManager Documentation](docs/MessageManager_Documentation.md)
- 🐛 **Issues**: [Azure DevOps Repository](https://dev.azure.com/ascendionava/AWE/_git/vizcheck)

---
