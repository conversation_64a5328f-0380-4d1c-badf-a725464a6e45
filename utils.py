import os
from PIL import Image
import re   
import subprocess
from langchain_openai import AzureChatOpenAI

def singleton(cls):
    instances = {}

    def get_instance(*args, **kwargs):
        if cls not in instances:
            instances[cls] = cls(*args, **kwargs)
        return instances[cls]

    return get_instance

def ensure_folder_exists(folder_path):
    """
    Ensure the specified folder exists. Create it if it doesn't.
    """
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)
        print(f"Folder '{folder_path}' created.")
    else:
        print(f"Folder '{folder_path}' already exists.")

def open_pdf(file_name: str):
    if not os.path.exists(file_name):
        print(f"PDF '{file_name}' does not exist.")
        return
    pdf_file_path = os.path.abspath(file_name)
    if os.path.exists(pdf_file_path):
        pdf_file_url = f"file:///{pdf_file_path.replace(os.sep, '/')}"  
        print(f"Opening: {pdf_file_url}")
        edge_path = r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe"
        chrome_path = r"C:\Program Files\Google\Chrome\Application\chrome.exe"
        
        try:
            subprocess.run([edge_path, pdf_file_path])
        except FileNotFoundError:
            print("Microsoft Edge not found, trying Google Chrome.")
            try:
                subprocess.run([chrome_path, pdf_file_path])
            except FileNotFoundError:
                print("Google Chrome not found, please install one of the browsers.")
    else:
        print("PDF file does not exist!")


def modify_gif():
    input_gif =  os.path.join("outputs", "agent_history.gif")
    output_gif =  os.path.join("outputs", "output.gif")
    
    if not os.path.exists(input_gif):
        return

    gif = Image.open(input_gif)

    frames = []

    try:
        gif.seek(1) 
        while True:
            frame = gif.copy()
            frames.append(frame)
            gif.seek(gif.tell() + 1)
    except EOFError:
        pass

    if frames:
        frames[0].save(output_gif, save_all=True, append_images=frames[1:], duration=gif.info['duration'], loop=gif.info['loop'])
        print(f"New GIF created without the first frame: {output_gif}")
        try:
            gif.close()
            os.remove(input_gif)
        except Exception as e:
            pass
    else:
        print("No frames found after the first one.")

def extract_country(text):
    try:
        country_list = [
            "Argentina", "Australia", "Austria", "Belgium", "Brazil", "Canada", "Czechia",
            "Denmark", "Finland", "France", "Germany", "Hungary", "Ireland", "Italy",
            "Japan", "Korea", "Mexico", "Netherlands", "New Zealand", "Norway", "Poland",
            "Portugal", "Slovakia", "Spain", "Sweden", "Switzerland", "United Kingdom", "United States"
        ] 

        pattern = re.compile(r'\b(?:' + '|'.join(re.escape(country) for country in country_list) + r')\b', re.IGNORECASE)

        match = pattern.search(text)
        if match:
            for country in country_list:
                if country.lower() == match.group(0).lower():
                    return country
        else:
            azure_deployment = os.getenv("AZURE_DEPLOYMENT")
            openai_api_version = os.getenv("OPENAI_API_VERSION")
            azure_endpoint = os.getenv("AZURE_ENDPOINT")
            azure_deployment = "gpt-4o" if azure_deployment is None else azure_deployment
            openai_api_version = "2024-05-01-preview" if openai_api_version is None else azure_deployment
            azure_endpoint = "https://michelangelovision.openai.azure.com/" if azure_endpoint is None else azure_endpoint
            llm=AzureChatOpenAI(
                azure_deployment=azure_deployment,
                openai_api_version=openai_api_version,
                azure_endpoint=azure_endpoint,
                api_key=os.getenv("AZURE_API_KEY"),
            )
            system_prompt = f"""
    You are an assistant that determines which country the user wants to use to open a webpage or play a game.

    You will be given:
    - A user's message (free-form input)
    - A list of available country names

    Your job:
    1. Carefully analyze the user's intent.
    2. From the list of available countries, determine which one the user wants to use to open the webpage or launch the game.
    3. Match must be based on **intent**, not just the first country mentioned.
    4. Matching is case-insensitive and based on **exact country names**.
    5. Return the country name exactly as it appears in the list.
    6. If no country is clearly indicated or the country is not in the available list, return `None`.

    Available countries: {', '.join(country_list)}
    """
            response = llm.invoke([("system", system_prompt), ("human", text)])
            if hasattr(response, "content") and response.content:
                match = pattern.search(text)
                if match:
                    for country in country_list:
                        if country.lower() == match.group(0).lower():
                            return country
    except Exception as e:
        pass
    return "United States"

