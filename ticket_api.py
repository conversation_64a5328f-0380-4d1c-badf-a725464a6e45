from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from pydantic import BaseModel
from typing import Optional, Dict, List

from ado_workitem import ADOWorkItemManager  # Your class file

# Initialize FastAPI app
app = FastAPI(title="Azure DevOps Ticket API")

# Initialize the ADO manager (Replace with your actual details)
ADO_MANAGER = ADOWorkItemManager(
    organization_url="https://dev.azure.com/ascendionava",
    project_name="PixelShiftStudio"
)

# ----------- Models for Request/Response -----------

class CreateWorkItemRequest(BaseModel):
    work_item_type: str
    title: str
    description: Optional[str] = None
    fields: Optional[Dict[str, str]] = None

class UpdateWorkItemRequest(BaseModel):
    fields_to_update: Dict[str, str]

class WIQLQueryRequest(BaseModel):
    query: str

# ----------- API Endpoints -----------

@app.post("/workitems/create")
def create_work_item(payload: CreateWorkItemRequest):
    item = ADO_MANAGER.create_work_item(
        work_item_type=payload.work_item_type,
        title=payload.title,
        description=payload.description,
        fields=payload.fields
    )
    if not item:
        raise HTTPException(status_code=500, detail="Failed to create work item")
    return {"id": item.id, "url": item.url}

@app.get("/workitems/{work_item_id}")
def get_work_item(work_item_id: int, expand: Optional[bool] = False):
    item = ADO_MANAGER.get_work_item(work_item_id, expand_fields=expand)
    if not item:
        raise HTTPException(status_code=404, detail="Work item not found")
    return {
        "id": item.id,
        "title": item.fields.get("System.Title"),
        "state": item.fields.get("System.State"),
        "assigned_to": item.fields.get("System.AssignedTo", "Unassigned")
    }

@app.put("/workitems/{work_item_id}")
def update_work_item(work_item_id: int, payload: UpdateWorkItemRequest):
    item = ADO_MANAGER.update_work_item(work_item_id, payload.fields_to_update)
    if not item:
        raise HTTPException(status_code=500, detail="Failed to update work item")
    return {
        "id": item.id,
        "updated_fields": payload.fields_to_update
    }

@app.post("/workitems/query")
def query_work_items(payload: WIQLQueryRequest):
    references = ADO_MANAGER.query_work_items_by_wiql(payload.query)
    if references is None:
        raise HTTPException(status_code=500, detail="Query failed")

    items = []
    for ref in references:
        item = ADO_MANAGER.get_work_item(ref.id)
        if item:
            items.append({
                "id": item.id,
                "title": item.fields.get("System.Title"),
                "state": item.fields.get("System.State"),
                "assigned_to": item.fields.get("System.AssignedTo", "Unassigned")
            })

    return {"count": len(items), "items": items}
