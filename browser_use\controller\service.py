import asyncio
import json
import logging
from typing import Dict, Generic, Optional, Type, TypeVar

from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.prompts import PromptTemplate

# from lmnr.sdk.laminar import Laminar
from pydantic import BaseModel
from pyKey import pressKey, release<PERSON><PERSON>, key_dict
from browser_use.agent.views import ActionModel, ActionResult
from browser_use.browser.context import BrowserContext
from browser_use.controller.registry.service import Registry
from browser_use.controller.views import (
	ClickElementAction,
	DoneAction,
	GoToUrlAction,
	InputTextAction,
	NoParamsAction,
	OpenTabAction,
	ScrollAction,
	SearchGoogleAction,
	SendKeysAction,
	SwitchTabAction,
	KeyGameControlAction,
	XboxGameControlAction,
	ConnectXboxController,
)
import pyautogui
import time
from browser_use.utils import time_execution_sync
import pydirectinput
import random
from pyvjoystick.vigem import VX360Gamepad, XUSB_BUTTON

logger = logging.getLogger(__name__)


Context = TypeVar('Context')


class Controller(Generic[Context]):
	def __init__(
		self,
		exclude_actions: list[str] = [],
		output_model: Optional[Type[BaseModel]] = None,
	):
		self.registry = Registry[Context](exclude_actions)
		self.start_time = time.time()
		self.gamepad: VX360Gamepad = VX360Gamepad()
		self.BUTTON_MAP = {
            "a": XUSB_BUTTON.XUSB_GAMEPAD_A,
            "b": XUSB_BUTTON.XUSB_GAMEPAD_B,
            "x": XUSB_BUTTON.XUSB_GAMEPAD_X,
            "y": XUSB_BUTTON.XUSB_GAMEPAD_Y,
            "back": XUSB_BUTTON.XUSB_GAMEPAD_BACK,
            "start": XUSB_BUTTON.XUSB_GAMEPAD_START,
            "guide": XUSB_BUTTON.XUSB_GAMEPAD_GUIDE,
            "left shoulder": XUSB_BUTTON.XUSB_GAMEPAD_LEFT_SHOULDER,
            "right shoulder": XUSB_BUTTON.XUSB_GAMEPAD_RIGHT_SHOULDER,
            "left thumb": XUSB_BUTTON.XUSB_GAMEPAD_LEFT_THUMB,
            "right thumb": XUSB_BUTTON.XUSB_GAMEPAD_RIGHT_THUMB
        }

		"""Register all default browser actions"""

		if output_model is not None:
			# Create a new model that extends the output model with success parameter
			class ExtendedOutputModel(output_model):  # type: ignore
				success: bool = True

			@self.registry.action(
				'Complete task - with return text and if the task is finished (success=True) or not yet  completly finished (success=False), because last step is reached',
				param_model=ExtendedOutputModel,
			)
			async def done(params: ExtendedOutputModel):
				# Exclude success from the output JSON since it's an internal parameter
				output_dict = params.model_dump(exclude={'success'})
				return ActionResult(is_done=True, success=params.success, extracted_content=json.dumps(output_dict))
		else:

			@self.registry.action(
				'Complete task - with return text and if the task is finished (success=True) or not yet  completly finished (success=False), because last step is reached',
				param_model=DoneAction,
			)
			async def done(params: DoneAction):
				return ActionResult(is_done=True, success=params.success, extracted_content=params.text)

		# Basic Navigation Actions
		@self.registry.action(
			'Search the query in Google in the current tab, the query should be a search query like humans search in Google, concrete and not vague or super long. More the single most important items. ',
			param_model=SearchGoogleAction,
		)
		async def search_google(params: SearchGoogleAction, browser: BrowserContext):
			page = await browser.get_current_page()
			await page.goto(f'https://www.google.com/search?q={params.query}&udm=14')
			await page.wait_for_load_state(timeout=60)
			msg = f'🔍  Searched for "{params.query}" in Google'
			logger.info(msg)
			return ActionResult(extracted_content=msg, include_in_memory=True)

		@self.registry.action('Navigate to URL in the current tab', param_model=GoToUrlAction)
		async def go_to_url(params: GoToUrlAction, browser: BrowserContext):
			page = await browser.get_current_page()
			await page.goto(params.url, timeout=120000)  
			await page.wait_for_load_state(timeout=60)
			msg = f'🔗  Navigated to {params.url}'
			logger.info(msg)
			return ActionResult(extracted_content=msg, include_in_memory=True)

		@self.registry.action('Go back', param_model=NoParamsAction)
		async def go_back(_: NoParamsAction, browser: BrowserContext):
			await browser.go_back()
			msg = '🔙  Navigated back'
			logger.info(msg)
			return ActionResult(extracted_content=msg, include_in_memory=True)

		# wait for x seconds
		@self.registry.action('Wait for x seconds default 3')
		async def wait(seconds: int = 3):
			msg = f'🕒  Waiting for {seconds} seconds'
			logger.info(msg)
			await asyncio.sleep(seconds)
			return ActionResult(extracted_content=msg, include_in_memory=True)

		# Element Interaction Actions
		@self.registry.action('Click element', param_model=ClickElementAction)
		async def click_element(params: ClickElementAction, browser: BrowserContext):
			session = await browser.get_session()

			if params.index not in await browser.get_selector_map():
				raise Exception(f'Element with index {params.index} does not exist - retry or use alternative actions')

			element_node = await browser.get_dom_element_by_index(params.index)
			initial_pages = len(session.context.pages)

			# if element has file uploader then dont click
			if await browser.is_file_uploader(element_node):
				msg = f'Index {params.index} - has an element which opens file upload dialog. To upload files please use a specific function to upload files '
				logger.info(msg)
				return ActionResult(extracted_content=msg, include_in_memory=True)

			msg = None

			try:
				download_path = await browser._click_element_node(element_node)
				if download_path:
					msg = f'💾  Downloaded file to {download_path}'
				else:
					msg = f'🖱️  Clicked button with index {params.index}: {element_node.get_all_text_till_next_clickable_element(max_depth=2)}'

				logger.info(msg)
				logger.debug(f'Element xpath: {element_node.xpath}')
				if len(session.context.pages) > initial_pages:
					new_tab_msg = 'New tab opened - switching to it'
					msg += f' - {new_tab_msg}'
					logger.info(new_tab_msg)
					await browser.switch_to_tab(-1)
				return ActionResult(extracted_content=msg, include_in_memory=True)
			except Exception as e:
				logger.warning(f'Element not clickable with index {params.index} - most likely the page changed')
				return ActionResult(error=str(e))

		@self.registry.action(
			'Input text into a input interactive element',
			param_model=InputTextAction,
		)
		async def input_text(params: InputTextAction, browser: BrowserContext, has_sensitive_data: bool = False):
			if params.index not in await browser.get_selector_map():
				raise Exception(f'Element index {params.index} does not exist - retry or use alternative actions')

			element_node = await browser.get_dom_element_by_index(params.index)
			await browser._input_text_element_node(element_node, params.text)
			if not has_sensitive_data:
				msg = f'⌨️  Input {params.text} into index {params.index}'
			else:
				msg = f'⌨️  Input sensitive data into index {params.index}'
			logger.info(msg)
			logger.debug(f'Element xpath: {element_node.xpath}')
			return ActionResult(extracted_content=msg, include_in_memory=True)

		# Tab Management Actions
		@self.registry.action('Switch tab', param_model=SwitchTabAction)
		async def switch_tab(params: SwitchTabAction, browser: BrowserContext):
			await browser.switch_to_tab(params.page_id)
			# Wait for tab to be ready
			page = await browser.get_current_page()
			await page.wait_for_load_state(timeout=60)
			msg = f'🔄  Switched to tab {params.page_id}'
			logger.info(msg)
			return ActionResult(extracted_content=msg, include_in_memory=True)

		@self.registry.action('Open url in new tab', param_model=OpenTabAction)
		async def open_tab(params: OpenTabAction, browser: BrowserContext):
			await browser.create_new_tab(params.url)
			msg = f'🔗  Opened new tab with {params.url}'
			logger.info(msg)
			return ActionResult(extracted_content=msg, include_in_memory=True)

		# Content Actions
		@self.registry.action(
			'Extract page content to retrieve specific information from the page, e.g. all company names, a specifc description, all information about, links with companies in structured format or simply links',
		)
		async def extract_content(goal: str, browser: BrowserContext, page_extraction_llm: BaseChatModel):
			page = await browser.get_current_page()
			import markdownify

			content = markdownify.markdownify(await page.content())

			prompt = 'Your task is to extract the content of the page. You will be given a page and a goal and you should extract all relevant information around this goal from the page. If the goal is vague, summarize the page. Respond in json format. Extraction goal: {goal}, Page: {page}'
			template = PromptTemplate(input_variables=['goal', 'page'], template=prompt)
			try:
				output = page_extraction_llm.invoke(template.format(goal=goal, page=content))
				msg = f'📄  Extracted from page\n: {output.content}\n'
				logger.info(msg)
				return ActionResult(extracted_content=msg, include_in_memory=True)
			except Exception as e:
				logger.debug(f'Error extracting content: {e}')
				msg = f'📄  Extracted from page\n: {content}\n'
				logger.info(msg)
				return ActionResult(extracted_content=msg)

		@self.registry.action(
			'Scroll down the page by pixel amount - if no amount is specified, scroll down one page',
			param_model=ScrollAction,
		)
		async def scroll_down(params: ScrollAction, browser: BrowserContext):
			page = await browser.get_current_page()
			if params.amount is not None:
				await page.evaluate(f'window.scrollBy(0, {params.amount});')
			else:
				await page.evaluate('window.scrollBy(0, window.innerHeight);')

			amount = f'{params.amount} pixels' if params.amount is not None else 'one page'
			msg = f'🔍  Scrolled down the page by {amount}'
			logger.info(msg)
			return ActionResult(
				extracted_content=msg,
				include_in_memory=True,
			)

		# scroll up
		@self.registry.action(
			'Scroll up the page by pixel amount - if no amount is specified, scroll up one page',
			param_model=ScrollAction,
		)
		async def scroll_up(params: ScrollAction, browser: BrowserContext):
			page = await browser.get_current_page()
			if params.amount is not None:
				await page.evaluate(f'window.scrollBy(0, -{params.amount});')
			else:
				await page.evaluate('window.scrollBy(0, -window.innerHeight);')

			amount = f'{params.amount} pixels' if params.amount is not None else 'one page'
			msg = f'🔍  Scrolled up the page by {amount}'
			logger.info(msg)
			return ActionResult(
				extracted_content=msg,
				include_in_memory=True,
			)

		# send keys
		@self.registry.action(
			'Send strings of special keys like Escape,Backspace, Insert, PageDown, Delete, Enter, Shortcuts such as `Control+o`, `Control+Shift+T` are supported as well. This gets used in keyboard.press.',
			param_model=SendKeysAction,
		)
		async def send_keys(params: SendKeysAction, browser: BrowserContext):
			page = await browser.get_current_page()

			try:
				await page.keyboard.press(params.keys)
			except Exception as e:
				if 'Unknown key' in str(e):
					# loop over the keys and try to send each one
					for key in params.keys:
						try:
							await page.keyboard.press(key)
						except Exception as e:
							logger.debug(f'Error sending key {key}: {str(e)}')
							raise e
				else:
					raise e
			msg = f'⌨️  Sent keys: {params.keys}'
			logger.info(msg)
			return ActionResult(extracted_content=msg, include_in_memory=True)
		
		# send keys with delay
		@self.registry.action(
			'Send strings of Enter, A, W, S, D keys with delay. ',
			param_model=SendKeysAction,
		)
		async def send_keys_with_delay(params: SendKeysAction, browser: BrowserContext):

			try:
				print("Sending Key")
				# pyautogui.keyDown(params.keys)
				pressKey(params.keys.upper())
				time.sleep(1)
				releaseKey(params.keys.upper())
				# pyautogui.keyUp(params.keys)
			except Exception as e:
				if 'Unknown key' in str(e):
					# loop over the keys and try to send each one
					for key in params.keys:
						try:
							# pyautogui.keyDown(params.keys)
							pressKey(params.keys.upper())
							time.sleep(1)
							releaseKey(params.keys.upper())
							# pyautogui.keyUp(params.keys)
						except Exception as e:
							logger.debug(f'Error sending key {key}: {str(e)}')
							raise e
				else:
					raise e
			msg = f'⌨️  Sent keys: {params.keys}'
			logger.info(msg)
			return ActionResult(extracted_content=msg, include_in_memory=True)
		
		# Connect to Xbox controller
		@self.registry.action(
			'Connect to Xbox controller.',
			param_model=ConnectXboxController
		)
		async def connect_xbox_controller(params: ConnectXboxController):
			msg = f'Connected to Xbox controller'
			logger.info(msg)
			return ActionResult(extracted_content=msg, include_in_memory=True)
		
		# In game action move forward
		@self.registry.action(
			'Move the in-game character and control actions like move forward, backward, left, right, and mouse move in Keyboard. Also set the boolean is_play_ten_min to true if user want to play the game for 10 min',
			param_model=KeyGameControlAction,
		)
		async def in_game_action_seq_keyboard(params: KeyGameControlAction, browser: BrowserContext):
			exe_start_time = time.time()
			try:
				for action in params.actions:
					match action:
						case "forward":
							pressKey("w")
							time.sleep(1)
							releaseKey("w")
						case "backward":
							pressKey("s")
							time.sleep(1)
							releaseKey("s")
						case "left":
							pressKey("a")
							time.sleep(1)
							releaseKey("a")
						case "right":
							pressKey("d")
							time.sleep(1)
							releaseKey("d")
						case "mouse_left":
							current_x, current_y = pydirectinput.position()
							print(f"Clicking left mouse button at ({current_x}, {current_y})")
							pydirectinput.moveTo(current_x, current_y)
							pydirectinput.click(button="left")
						case "mouse_right":
							current_x, current_y = pydirectinput.position()
							print(f"Clicking right mouse button at ({current_x}, {current_y})")
							pydirectinput.moveTo(current_x, current_y)
							pydirectinput.click(button="right")
						case "mouse_move_left":
							current_x, current_y = pydirectinput.position()
							pydirectinput.moveTo(current_x - 1000, current_y)  # move left
							pydirectinput.moveTo(current_x + 1000, current_y)  # move right
						case "mouse_move_right":
							current_x, current_y = pydirectinput.position()
							print(f"Moving mouse right from ({current_x}, {current_y})")
							pydirectinput.moveTo(current_x + 1000, current_y)  # move right
							pydirectinput.moveTo(current_x - 1000, current_y)  # move left
						case _:
							print(f"Unknown action: {action}") 

					await asyncio.sleep(0.5)
				allowed_actions = [
            "forward", "backward", "left", "right", "mouse_move_left", "mouse_move_right"
        ]
				duration = 10 * 60  # 10 minutes in seconds
				action_seq = None
				while params.is_play_ten_min and (time.time() - self.start_time) < duration:
					action = random.choice(allowed_actions)
					match action:
						case "forward":
							pressKey("w")
							time.sleep(1)
							releaseKey("w")
						case "backward":
							pressKey("s")
							time.sleep(1)
							releaseKey("s")
						case "left":
							pressKey("a")
							time.sleep(1)
							releaseKey("a")
						case "right":
							pressKey("d")
							time.sleep(1)
							releaseKey("d")
						case "mouse_left":
							current_x, current_y = pydirectinput.position()
							print(f"Clicking left mouse button at ({current_x}, {current_y})")
							pydirectinput.moveTo(current_x, current_y)
							pydirectinput.click(button="left")
						case "mouse_right":
							current_x, current_y = pydirectinput.position()
							print(f"Clicking right mouse button at ({current_x}, {current_y})")
							pydirectinput.moveTo(current_x, current_y)
							pydirectinput.click(button="right")
						case "mouse_move_left":
							current_x, current_y = pydirectinput.position()
							pydirectinput.moveTo(current_x - 1000, current_y)  # move left
							pydirectinput.moveTo(current_x + 1000, current_y)  # move right
						case "mouse_move_right":
							current_x, current_y = pydirectinput.position()
							print(f"Moving mouse right from ({current_x}, {current_y})")
							pydirectinput.moveTo(current_x + 1000, current_y)  # move right
							pydirectinput.moveTo(current_x - 1000, current_y)  # move left
						case _:
							print(f"Unknown action: {action}") 

					await asyncio.sleep(0.5)
			except Exception as e:
				print(f"Error executing actions: {e}")
			msg = f'⌨️  Executing: {params.actions}. Execution time {time.time() - exe_start_time} seconds.'
			logger.info(msg)
			return ActionResult(extracted_content=msg, include_in_memory=True)

		@self.registry.action(
			'Change xbox controller to keyboard and move the in-game character and control actions like move forward, backward, left, right, a, b, x, y, view_left, and view_right in Keyboard.',
			param_model=KeyGameControlAction,
		)
		async def in_game_action_seq_change_keyboard(params: KeyGameControlAction, browser: BrowserContext):
			exe_start_time = time.time()
			try:
				# self.handle_action("tap", "back")
				self.handle_action("tap", "back")
				time.sleep(5)
				pressKey("s")
				time.sleep(5)
				pressKey("ESC")
				for action in params.actions:
					match action:
						case "forward":
							pressKey("w")
							time.sleep(1)
							releaseKey("w")
						case "backward":
							pressKey("s")
							time.sleep(1)
							releaseKey("s")
						case "left":
							pressKey("a")
							time.sleep(1)
							releaseKey("a")
						case "right":
							pressKey("d")
							time.sleep(1)
							releaseKey("d")
						case "mouse_left":
							current_x, current_y = pydirectinput.position()
							print(f"Clicking left mouse button at ({current_x}, {current_y})")
							pydirectinput.moveTo(current_x, current_y)
							pydirectinput.click(button="left")
						case "mouse_right":
							current_x, current_y = pydirectinput.position()
							print(f"Clicking right mouse button at ({current_x}, {current_y})")
							pydirectinput.moveTo(current_x, current_y)
							pydirectinput.click(button="right")
						case "mouse_move_left":
							current_x, current_y = pydirectinput.position()
							pydirectinput.moveTo(current_x - 1000, current_y)  # move left
							pydirectinput.moveTo(current_x + 1000, current_y)  # move right
						case "mouse_move_right":
							current_x, current_y = pydirectinput.position()
							print(f"Moving mouse right from ({current_x}, {current_y})")
							pydirectinput.moveTo(current_x + 1000, current_y)  # move right
							pydirectinput.moveTo(current_x - 1000, current_y)  # move left
						case _:
							print(f"Unknown action: {action}") 

					await asyncio.sleep(0.5)
			except Exception as e:
				print(f"Error executing actions: {e}")
			msg = f'⌨️  Executing: {params.actions}. Execution time {time.time() - exe_start_time} seconds.'
			logger.info(msg)
			return ActionResult(extracted_content=msg, include_in_memory=True)
		
		@self.registry.action(
			'Change keyboard to xbox controller and move the in-game character and control actions like move forward, backward, left, right, a, b, x, y, view_left, and view_right in Xbox.',
			param_model=XboxGameControlAction,
		)
		async def in_game_action_seq_change_xbox(params: XboxGameControlAction, browser: BrowserContext):
			exe_start_time = time.time()
			try:
				# self.handle_action("tap", "back")
				pressKey("ESC")
				time.sleep(5)
				self.handle_action("down", "left_thumb")
				time.sleep(5)
				self.handle_action("tap", "b")
				for action in params.actions:
					match action:
						case "forward":
							self.handle_action("up", "left_thumb")
						case "backward":
							self.handle_action("down", "left_thumb")
						case "left":
							self.handle_action("left", "left_thumb")
						case "right":
							self.handle_action("right", "left_thumb")
						case "view_left":
							self.handle_action("left", "right_thumb")
						case "view_right":
							self.handle_action("right", "right_thumb")
						case "a":
							self.handle_action("tap", "a")
						case "b":
							self.handle_action("tap", "b")
						case "x":
							self.handle_action("tap", "x")
						case "y":
							self.handle_action("tap", "y")
						case _:
							print(f"Unknown action: {action}") 

					await asyncio.sleep(0.5)
			except Exception as e:
				print(f"Error executing actions: {e}")
			msg = f'⌨️  Executing: {params.actions}. Execution time {time.time() - exe_start_time} seconds.'
			logger.info(msg)
			return ActionResult(extracted_content=msg, include_in_memory=True)
		
		@self.registry.action(
			'Move the in-game character and control actions like move forward, backward, left, right, a, b, x, y, view_left, and view_right in Xbox. Also set the boolean is_play_ten_min to true if user want to play the game for 10 min',
			param_model=XboxGameControlAction,
		)
		async def in_game_action_seq_xbox(params: XboxGameControlAction, browser: BrowserContext):
			exe_start_time = time.time()
			try:
				for action in params.actions:
					match action:
						case "forward":
							self.handle_action("up", "left_thumb")
						case "backward":
							self.handle_action("down", "left_thumb")
						case "left":
							self.handle_action("left", "left_thumb")
						case "right":
							self.handle_action("right", "left_thumb")
						case "view_left":
							self.handle_action("left", "right_thumb")
						case "view_right":
							self.handle_action("right", "right_thumb")
						case "a":
							self.handle_action("tap", "a")
						case "b":
							self.handle_action("tap", "b")
						case "x":
							self.handle_action("tap", "x")
						case "y":
							self.handle_action("tap", "y")
						case _:
							print(f"Unknown action: {action}") 

					await asyncio.sleep(0.5)
				allowed_actions = [
            "forward", "backward", "left", "right", "view_left", "view_right", "a", "b", "x", "y"
        ]
				duration = 10 * 60  # 10 minutes in seconds
				action_seq = None
				while params.is_play_ten_min and (time.time() - self.start_time) < duration:
					action = random.choice(allowed_actions)
					match action:
						case "forward":
							self.handle_action("up", "left_thumb")
						case "backward":
							self.handle_action("down", "left_thumb")
						case "left":
							self.handle_action("left", "left_thumb")
						case "right":
							self.handle_action("right", "left_thumb")
						case "view_left":
							self.handle_action("left", "right_thumb")
						case "view_right":
							self.handle_action("right", "right_thumb")
						case "a":
							self.handle_action("tap", "a")
						case "b":
							self.handle_action("tap", "b")
						case "x":
							self.handle_action("tap", "x")
						case "y":
							self.handle_action("tap", "y")
						case _:
							print(f"Unknown action: {action}") 

					await asyncio.sleep(0.5)
			except Exception as e:
				print(f"Error executing actions: {e}")
			msg = f'⌨️  Executing: {params.actions}. Execution time {time.time() - exe_start_time} seconds.'
			logger.info(msg)
			return ActionResult(extracted_content=msg, include_in_memory=True)

		@self.registry.action(
			description='If you dont find something which you want to interact with, scroll to it',
		)
		async def scroll_to_text(text: str, browser: BrowserContext):  # type: ignore
			page = await browser.get_current_page()
			try:
				# Try different locator strategies
				locators = [
					page.get_by_text(text, exact=False),
					page.locator(f'text={text}'),
					page.locator(f"//*[contains(text(), '{text}')]"),
				]

				for locator in locators:
					try:
						# First check if element exists and is visible
						if await locator.count() > 0 and await locator.first.is_visible():
							await locator.first.scroll_into_view_if_needed()
							await asyncio.sleep(0.5)  # Wait for scroll to complete
							msg = f'🔍  Scrolled to text: {text}'
							logger.info(msg)
							return ActionResult(extracted_content=msg, include_in_memory=True)
					except Exception as e:
						logger.debug(f'Locator attempt failed: {str(e)}')
						continue

				msg = f"Text '{text}' not found or not visible on page"
				logger.info(msg)
				return ActionResult(extracted_content=msg, include_in_memory=True)

			except Exception as e:
				msg = f"Failed to scroll to text '{text}': {str(e)}"
				logger.error(msg)
				return ActionResult(error=msg, include_in_memory=True)

		@self.registry.action(
			description='Get all options from a native dropdown',
		)
		async def get_dropdown_options(index: int, browser: BrowserContext) -> ActionResult:
			"""Get all options from a native dropdown"""
			page = await browser.get_current_page()
			selector_map = await browser.get_selector_map()
			dom_element = selector_map[index]

			try:
				# Frame-aware approach since we know it works
				all_options = []
				frame_index = 0

				for frame in page.frames:
					try:
						options = await frame.evaluate(
							"""
							(xpath) => {
								const select = document.evaluate(xpath, document, null,
									XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
								if (!select) return null;

								return {
									options: Array.from(select.options).map(opt => ({
										text: opt.text, //do not trim, because we are doing exact match in select_dropdown_option
										value: opt.value,
										index: opt.index
									})),
									id: select.id,
									name: select.name
								};
							}
						""",
							dom_element.xpath,
						)

						if options:
							logger.debug(f'Found dropdown in frame {frame_index}')
							logger.debug(f'Dropdown ID: {options["id"]}, Name: {options["name"]}')

							formatted_options = []
							for opt in options['options']:
								# encoding ensures AI uses the exact string in select_dropdown_option
								encoded_text = json.dumps(opt['text'])
								formatted_options.append(f'{opt["index"]}: text={encoded_text}')

							all_options.extend(formatted_options)

					except Exception as frame_e:
						logger.debug(f'Frame {frame_index} evaluation failed: {str(frame_e)}')

					frame_index += 1

				if all_options:
					msg = '\n'.join(all_options)
					msg += '\nUse the exact text string in select_dropdown_option'
					logger.info(msg)
					return ActionResult(extracted_content=msg, include_in_memory=True)
				else:
					msg = 'No options found in any frame for dropdown'
					logger.info(msg)
					return ActionResult(extracted_content=msg, include_in_memory=True)

			except Exception as e:
				logger.error(f'Failed to get dropdown options: {str(e)}')
				msg = f'Error getting options: {str(e)}'
				logger.info(msg)
				return ActionResult(extracted_content=msg, include_in_memory=True)

		@self.registry.action(
			description='Select dropdown option for interactive element index by the text of the option you want to select',
		)
		async def select_dropdown_option(
			index: int,
			text: str,
			browser: BrowserContext,
		) -> ActionResult:
			"""Select dropdown option by the text of the option you want to select"""
			page = await browser.get_current_page()
			selector_map = await browser.get_selector_map()
			dom_element = selector_map[index]

			# Validate that we're working with a select element
			if dom_element.tag_name != 'select':
				logger.error(f'Element is not a select! Tag: {dom_element.tag_name}, Attributes: {dom_element.attributes}')
				msg = f'Cannot select option: Element with index {index} is a {dom_element.tag_name}, not a select'
				return ActionResult(extracted_content=msg, include_in_memory=True)

			logger.debug(f"Attempting to select '{text}' using xpath: {dom_element.xpath}")
			logger.debug(f'Element attributes: {dom_element.attributes}')
			logger.debug(f'Element tag: {dom_element.tag_name}')

			xpath = '//' + dom_element.xpath

			try:
				frame_index = 0
				for frame in page.frames:
					try:
						logger.debug(f'Trying frame {frame_index} URL: {frame.url}')

						# First verify we can find the dropdown in this frame
						find_dropdown_js = """
							(xpath) => {
								try {
									const select = document.evaluate(xpath, document, null,
										XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
									if (!select) return null;
									if (select.tagName.toLowerCase() !== 'select') {
										return {
											error: `Found element but it's a ${select.tagName}, not a SELECT`,
											found: false
										};
									}
									return {
										id: select.id,
										name: select.name,
										found: true,
										tagName: select.tagName,
										optionCount: select.options.length,
										currentValue: select.value,
										availableOptions: Array.from(select.options).map(o => o.text.trim())
									};
								} catch (e) {
									return {error: e.toString(), found: false};
								}
							}
						"""

						dropdown_info = await frame.evaluate(find_dropdown_js, dom_element.xpath)

						if dropdown_info:
							if not dropdown_info.get('found'):
								logger.error(f'Frame {frame_index} error: {dropdown_info.get("error")}')
								continue

							logger.debug(f'Found dropdown in frame {frame_index}: {dropdown_info}')

							# "label" because we are selecting by text
							# nth(0) to disable error thrown by strict mode
							# timeout=1000 because we are already waiting for all network events, therefore ideally we don't need to wait a lot here (default 30s)
							selected_option_values = (
								await frame.locator('//' + dom_element.xpath).nth(0).select_option(label=text, timeout=1000)
							)

							msg = f'selected option {text} with value {selected_option_values}'
							logger.info(msg + f' in frame {frame_index}')

							return ActionResult(extracted_content=msg, include_in_memory=True)

					except Exception as frame_e:
						logger.error(f'Frame {frame_index} attempt failed: {str(frame_e)}')
						logger.error(f'Frame type: {type(frame)}')
						logger.error(f'Frame URL: {frame.url}')

					frame_index += 1

				msg = f"Could not select option '{text}' in any frame"
				logger.info(msg)
				return ActionResult(extracted_content=msg, include_in_memory=True)

			except Exception as e:
				msg = f'Selection failed: {str(e)}'
				logger.error(msg)
				return ActionResult(error=msg, include_in_memory=True)
			
	def press_button(self, button):
		self.gamepad.press_button(button=button)
		self.gamepad.update()

	def release_button(self, button):
		self.gamepad.release_button(button=button)
		self.gamepad.update()

	def tap_button(self, button, delay=1):
		self.press_button(button=button)
		time.sleep(delay)
		self.release_button(button=button)

	def move_thumb(self, direction: str, thumb: str):
		if thumb == "left_thumb":
			if direction == "left":
				self.gamepad.left_joystick(x_value=-32768, y_value=0)
			elif direction == "right":
				self.gamepad.left_joystick(x_value=32767, y_value=0)
			elif direction == "up":
				self.gamepad.left_joystick(x_value=0, y_value=-32768)
			elif direction == "down":
				self.gamepad.left_joystick(x_value=0, y_value=32767)
			elif direction == "center":
				self.gamepad.left_joystick(x_value=0, y_value=0)

		elif thumb == "right_thumb":
			if direction == "left":
				self.gamepad.right_joystick(x_value=-32768, y_value=0)
			elif direction == "right":
				self.gamepad.right_joystick(x_value=32767, y_value=0)
			elif direction == "up":
				self.gamepad.right_joystick(x_value=0, y_value=-32768)
			elif direction == "down":
				self.gamepad.right_joystick(x_value=0, y_value=32767)
			elif direction == "center":
				self.gamepad.right_joystick(x_value=0, y_value=0)

		self.gamepad.update()

	def handle_action(self, action: str, button_label: str, delay: float=1):
		try:
			print(f"Action: {action}   Button: {button_label}")
			action = action.lower().strip()
			button_label = button_label.lower().strip()

			if action == "tap":
				button = self.BUTTON_MAP.get(button_label)
				if button:
					self.tap_button(button, delay)
				else:
					logger.error(f"[ERROR] Unknown button for tap: {button_label}")

			elif action == "press":
				button = self.BUTTON_MAP.get(button_label)
				if button:
					self.press_button(button)
				else:
					logger.error(f"[ERROR] Unknown button for press: {button_label}")

			elif action == "release":
				button = self.BUTTON_MAP.get(button_label)
				if button:
					self.release_button(button)
				else:
					logger.error(f"[ERROR] Unknown button for release: {button_label}")

			elif action in ["left", "right", "up", "down"]:
				if "thumb" in button_label:
					self.move_thumb(action, button_label)
					time.sleep(delay)
					self.move_thumb("center", button_label)
				else:
					logger.error(f"[ERROR] Unknown thumbstick move: {action} {button_label}")

			else:
				logger.error(f"[ERROR] Unknown action: {action}")
		except Exception as e:
			pass

	def set_dpad(self, direction):  # direction: 'UP', 'DOWN', 'LEFT', 'RIGHT', etc.
		return
		from pyvjoystick.vigem.constants import XUSB_DPAD_DIRECTION
		direction_map = {
			'UP': XUSB_DPAD_DIRECTION.UP,
			'DOWN': XUSB_DPAD_DIRECTION.DOWN,
			'LEFT': XUSB_DPAD_DIRECTION.LEFT,
			'RIGHT': XUSB_DPAD_DIRECTION.RIGHT,
			'UP_LEFT': XUSB_DPAD_DIRECTION.UP_LEFT,
			'UP_RIGHT': XUSB_DPAD_DIRECTION.UP_RIGHT,
			'DOWN_LEFT': XUSB_DPAD_DIRECTION.DOWN_LEFT,
			'DOWN_RIGHT': XUSB_DPAD_DIRECTION.DOWN_RIGHT,
			'NONE': XUSB_DPAD_DIRECTION.NONE,
		}
		self.gamepad.dpad(direction_map.get(direction.upper(), XUSB_DPAD_DIRECTION.NONE))
		self.gamepad.update()

	def move_left_thumb(self, x, y):  # x, y in range -32768 to 32767
		self.gamepad.left_joystick(x_value=x, y_value=y)
		self.gamepad.update()

	def move_right_thumb(self, x, y):  # x, y in range -32768 to 32767
		self.gamepad.right_joystick(x_value=x, y_value=y)
		self.gamepad.update()

	def set_triggers(self, left=0, right=0):  # values 0-255
		self.gamepad.left_trigger(value=left)
		self.gamepad.right_trigger(value=right)
		self.gamepad.update()

	def reset_controller(self):
		# set_dpad('NONE')
		self.move_left_thumb(0, 0)
		self.move_right_thumb(0, 0)
		self.set_triggers(0, 0)
		# for btn in [XUSB_BUTTON.A, XUSB_BUTTON.B, XUSB_BUTTON.X, XUSB_BUTTON.Y,
		#             XUSB_BUTTON.BACK, XUSB_BUTTON.START,
		#             XUSB_BUTTON.LEFT_SHOULDER, XUSB_BUTTON.RIGHT_SHOULDER,
		#             XUSB_BUTTON.LEFT_THUMB, XUSB_BUTTON.RIGHT_THUMB,
		#             XUSB_BUTTON.GUIDE]:
		#     release_button(btn)
		self.gamepad.update()


	# Register ---------------------------------------------------------------

	def action(self, description: str, **kwargs):
		"""Decorator for registering custom actions

		@param description: Describe the LLM what the function does (better description == better function calling)
		"""
		return self.registry.action(description, **kwargs)

	# Act --------------------------------------------------------------------

	@time_execution_sync('--act')
	async def act(
		self,
		action: ActionModel,
		browser_context: BrowserContext,
		#
		page_extraction_llm: Optional[BaseChatModel] = None,
		sensitive_data: Optional[Dict[str, str]] = None,
		available_file_paths: Optional[list[str]] = None,
		#
		context: Context | None = None,
	) -> ActionResult:
		"""Execute an action"""

		try:
			for action_name, params in action.model_dump(exclude_unset=True).items():
				if params is not None:
					# with Laminar.start_as_current_span(
					# 	name=action_name,
					# 	input={
					# 		'action': action_name,
					# 		'params': params,
					# 	},
					# 	span_type='TOOL',
					# ):
					result = await self.registry.execute_action(
						action_name,
						params,
						browser=browser_context,
						page_extraction_llm=page_extraction_llm,
						sensitive_data=sensitive_data,
						available_file_paths=available_file_paths,
						context=context,
					)

					# Laminar.set_span_output(result)

					if isinstance(result, str):
						return ActionResult(extracted_content=result)
					elif isinstance(result, ActionResult):
						return result
					elif result is None:
						return ActionResult()
					else:
						raise ValueError(f'Invalid action result type: {type(result)} of {result}')
			return ActionResult()
		except Exception as e:
			raise e
