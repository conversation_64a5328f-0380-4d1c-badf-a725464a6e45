# Browser Module Documentation

## Overview

The Browser module is the core browser automation component of the VizCheck system. It provides a high-level abstraction over Playwright for browser management, context handling, and web page interaction. The module is designed to handle complex browser scenarios including anti-detection measures, multi-tab management, and robust error handling.

## Table of Contents

1. [Architecture](#architecture)
2. [Core Components](#core-components)
3. [Browser Class](#browser-class)
4. [BrowserContext Class](#browsercontext-class)
5. [Data Models](#data-models)
6. [Configuration](#configuration)
7. [Usage Examples](#usage-examples)
8. [Best Practices](#best-practices)
9. [Troubleshooting](#troubleshooting)

## Architecture

### Module Structure
```
browser_use/browser/
├── browser.py          # Core Browser implementation
├── context.py          # BrowserContext implementation
├── views.py           # Data models and structures
└── tests/             # Test suite
    ├── screenshot_test.py
    └── test_clicks.py
```

### High-Level Architecture

The VizCheck Browser module implements a sophisticated multi-layered architecture that provides robust browser automation capabilities with advanced features like anti-detection, multi-context management, and intelligent error handling.

```
┌──────────────────────────────────────────────────────────────────────────────────┐
│                            VizCheck Browser System                               │
├──────────────────────────────────────────────────────────────────────────────────┤
│                              Application Layer                                   │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │     Agent       │  │   Controller    │  │      DOM        │  │   External  │  │
│  │   Service       │  │    Actions      │  │    Service      │  │    APIs     │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────┘  │
├──────────────────────────────────────────────────────────────────────────────────┤
│                            Browser Abstraction Layer                             │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                           Browser Factory                                   │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ Connection  │  │    Anti-    │  │ Lifecycle   │  │    Configuration    │ │ │
│  │  │ Management  │  │ Detection   │  │ Management  │  │    Management       │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                         Context Management                                  │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │   Session   │  │     Tab     │  │    State    │  │      History        │ │ │
│  │  │ Management  │  │ Management  │  │ Tracking    │  │    Management       │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
├──────────────────────────────────────────────────────────────────────────────────┤
│                           Browser Engine Layer                                   │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │   Playwright    │  │     Chrome      │  │    WebSocket    │  │     CDP     │  │
│  │   Integration   │  │   DevTools      │  │   Connection    │  │ Integration │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────┘  │
├──────────────────────────────────────────────────────────────────────────────────┤
│                            Web Platform Layer                                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │   Chromium      │  │      DOM        │  │   JavaScript    │  │   Network   │  │
│  │   Browser       │  │    Engine       │  │    Engine       │  │    Stack    │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────┘  │
├──────────────────────────────────────────────────────────────────────────────────┤
│                           Infrastructure Layer                                   │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │   Operating     │  │    Network      │  │   File System   │  │   Process   │  │
│  │    System       │  │   Resources     │  │   Resources     │  │ Management  │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────┘  │
└──────────────────────────────────────────────────────────────────────────────────┘
```

#### Architectural Principles

##### 1. Layered Abstraction
The browser system follows a strict layered architecture:
- **Application Layer**: High-level components that use browser services
- **Browser Abstraction Layer**: VizCheck's browser management and context handling
- **Browser Engine Layer**: Playwright and Chrome DevTools integration
- **Web Platform Layer**: Core browser technologies (Chromium, DOM, JavaScript)
- **Infrastructure Layer**: Operating system and hardware resources

##### 2. Connection Flexibility
The architecture supports multiple connection modes:
- **Local Browser**: Direct Playwright browser instance
- **CDP Connection**: Connect to existing Chrome instance via Chrome DevTools Protocol
- **WebSocket Connection**: Remote browser connection via WebSocket
- **Hybrid Mode**: Combination of connection types for different contexts

##### 3. Context Isolation
Each browser context operates independently:
- **Session Isolation**: Separate cookies, storage, and authentication
- **Resource Isolation**: Independent memory and process management
- **State Isolation**: Isolated browsing history and cache
- **Security Isolation**: Separate security contexts and permissions

##### 4. Anti-Detection Architecture
Built-in stealth capabilities:
- **User Agent Spoofing**: Dynamic user agent rotation
- **Fingerprint Masking**: Canvas, WebGL, and audio fingerprint protection
- **Behavioral Mimicking**: Human-like interaction patterns
- **Detection Evasion**: Advanced anti-bot detection countermeasures

#### Connection Architecture

```
Application Request → Browser Factory → Connection Manager → Browser Instance
        ↓                   ↓                ↓                    ↓
    Task Definition → Configuration → Connection Type → Active Browser
        ↓                   ↓                ↓                    ↓
   Context Creation → Context Config → Session Setup → Ready Context
```

##### Connection Types Flow

```
┌─────────────────────────────────────────────────────────────────┐
│                    Connection Decision Tree                     │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  CDP URL Provided? ──Yes──→ CDP Connection ──→ Remote Chrome    │
│         │                                                       │
│         No                                                      │
│         ↓                                                       │
│  WSS URL Provided? ──Yes──→ WebSocket Connection ──→ Cloud      │
│         │                                                       │
│         No                                                      │
│         ↓                                                       │
│  Chrome Path Set? ──Yes──→ Local Chrome ──→ Custom Binary       │
│         │                                                       │
│         No                                                      │
│         ↓                                                       │
│  Default Playwright ──→ Bundled Chromium ──→ Local Instance     │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

#### Context Management Architecture

```
Browser Instance
├── Context Pool
│   ├── Context 1 (Session A)
│   │   ├── Page 1 (Tab 1)
│   │   ├── Page 2 (Tab 2)
│   │   └── Page N (Tab N)
│   ├── Context 2 (Session B)
│   │   ├── Page 1 (Tab 1)
│   │   └── Page M (Tab M)
│   └── Context N (Session N)
├── State Management
│   ├── Browser State History
│   ├── DOM Element History
│   └── Action History
└── Resource Management
    ├── Memory Cleanup
    ├── Process Management
    └── Connection Pooling
```

##### Context Lifecycle

```
Context Creation → Configuration → Page Creation → DOM Analysis → Action Execution → State Update → Cleanup
       ↓               ↓              ↓               ↓               ↓              ↓           ↓
   New Session → Apply Settings → Load Page → Extract Elements → Execute Actions → Save State → Release Resources
```

#### Data Flow Architecture

```
User Request → Browser Context → Page Navigation → DOM Extraction → Element Interaction → State Capture
     ↓              ↓                ↓                ↓                   ↓               ↓
Configuration → Session Setup → Page Loading → Element Discovery → Action Execution → Result Processing
     ↓              ↓                ↓                ↓                   ↓               ↓
Browser Config → Context Config → Navigation → DOM Service → Controller → Browser State
```

**Detailed Flow:**
1. **Request Processing**: User request converted to browser operations
2. **Context Selection**: Appropriate browser context selected or created
3. **Page Management**: Target page loaded and prepared for interaction
4. **DOM Analysis**: Page structure analyzed and elements identified
5. **Action Execution**: User actions executed through browser APIs
6. **State Management**: Browser state captured and history updated
7. **Result Processing**: Results formatted and returned to caller

#### Security Architecture

##### Anti-Detection Measures
```
Detection Vector → Countermeasure → Implementation
      ↓                ↓               ↓
User Agent → Dynamic Rotation → Real Browser Headers
      ↓                ↓               ↓
Canvas Fingerprint → Noise Injection → Modified Canvas API
      ↓                ↓               ↓
WebGL Fingerprint → Parameter Spoofing → Modified WebGL Context
      ↓                ↓               ↓
Audio Fingerprint → Audio Masking → Modified Audio Context
      ↓                ↓               ↓
Behavioral Patterns → Human Simulation → Realistic Timing
```

##### Security Isolation
```
Browser Process
├── Context 1 (Isolated)
│   ├── Cookies & Storage
│   ├── Authentication State
│   └── Security Permissions
├── Context 2 (Isolated)
│   ├── Separate Cookies & Storage
│   ├── Independent Auth State
│   └── Isolated Permissions
└── Shared Resources
    ├── Browser Binary
    ├── Network Stack
    └── Process Management
```

#### Performance Architecture

##### Resource Management
```
Resource Pool
├── Browser Instances
│   ├── Active Browsers
│   ├── Idle Browsers
│   └── Browser Recycling
├── Context Pool
│   ├── Active Contexts
│   ├── Cached Contexts
│   └── Context Cleanup
└── Memory Management
    ├── Garbage Collection
    ├── Memory Monitoring
    └── Resource Limits
```

##### Optimization Strategies
- **Connection Reuse**: Reuse browser instances across sessions
- **Context Pooling**: Pool contexts for faster initialization
- **Lazy Loading**: Load resources only when needed
- **Memory Management**: Proactive cleanup and garbage collection
- **Caching**: Cache frequently accessed resources and configurations

### Component Interaction Flow
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Application   │────│     Browser      │────│   Playwright    │
│   (app.py)      │    │   (browser.py)   │    │   Browser       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   DOM Service   │────│ BrowserContext   │────│   Page/Tab      │
│                 │    │   (context.py)   │    │   Management    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │   State & Views  │
                       │   (views.py)     │
                       └──────────────────┘
```

## Core Components

### 1. Browser Class (browser.py)

**Purpose**: Main browser factory and lifecycle management

**Key Responsibilities**:
- Browser instance creation and configuration
- Connection management (CDP, WSS, local Chrome)
- Anti-detection measures implementation
- Resource cleanup and memory management

#### Browser Configuration

```python
@dataclass
class BrowserConfig:
    headless: bool = False                      # Run in headless mode
    disable_security: bool = True               # Disable security features
    extra_chromium_args: list[str] = []         # Additional Chrome arguments
    chrome_instance_path: str | None = None     # Path to Chrome executable
    wss_url: str | None = None                  # WebSocket connection URL
    cdp_url: str | None = None                  # Chrome DevTools Protocol URL
    proxy: ProxySettings | None = None          # Proxy configuration
    new_context_config: BrowserContextConfig   # Default context configuration
```

#### Key Methods

- **`__init__(config: BrowserConfig)`**: Initialize browser with configuration
- **`new_context(config: BrowserContextConfig) -> BrowserContext`**: Create new browser context
- **`get_playwright_browser() -> PlaywrightBrowser`**: Get underlying Playwright browser
- **`close()`**: Clean shutdown of browser instance

#### Connection Types

##### 1. Standard Browser
```python
# Default local browser instance
browser = Browser(BrowserConfig(headless=False))
```

##### 2. CDP Connection
```python
# Connect to existing Chrome instance via CDP
config = BrowserConfig(cdp_url="http://localhost:9222")
browser = Browser(config)
```

##### 3. WebSocket Connection
```python
# Connect via WebSocket (remote browser)
config = BrowserConfig(wss_url="ws://remote-browser:3000")
browser = Browser(config)
```

##### 4. Chrome Instance Path
```python
# Use specific Chrome installation
config = BrowserConfig(
    chrome_instance_path="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
)
browser = Browser(config)
```

### 2. BrowserContext Class (context.py)

**Purpose**: Browser context management and page interaction

**Key Responsibilities**:
- Page lifecycle management
- DOM interaction and element location
- State management and caching
- Navigation and tab management
- Screenshot and recording capabilities

#### BrowserContext Configuration

```python
@dataclass
class BrowserContextConfig:
    # Page Load Settings
    minimum_wait_page_load_time: float = 0.25           # Minimum wait time
    wait_for_network_idle_page_load_time: float = 0.5   # Network idle wait
    maximum_wait_page_load_time: float = 5              # Maximum wait timeout
    wait_between_actions: float = 0.5                   # Action delay
    
    # Browser Window Settings
    browser_window_size: dict = {'width': 1280, 'height': 1100}
    no_viewport: bool = None                            # Disable viewport
    user_agent: str = "Mozilla/5.0..."                 # Custom user agent
    locale: str = None                                  # Browser locale
    
    # Feature Settings
    highlight_elements: bool = True                     # Highlight DOM elements
    viewport_expansion: int = 500                       # Viewport expansion pixels
    include_dynamic_attributes: bool = True             # Include dynamic CSS attributes
    
    # Storage Settings
    cookies_file: str = None                           # Cookie persistence file
    save_recording_path: str = None                    # Video recording path
    save_downloads_path: str = None                    # Download directory
    trace_path: str = None                             # Playwright trace path
    
    # Security Settings
    disable_security: bool = True                      # Disable browser security
    allowed_domains: list[str] = None                  # Domain whitelist
```

#### Key Methods

##### Navigation & Page Management
- **`navigate_to(url: str)`**: Navigate to URL with validation
- **`refresh_page()`**: Reload current page
- **`go_back()` / `go_forward()`**: Browser history navigation
- **`get_page_html() -> str`**: Get current page HTML content
- **`execute_javascript(script: str)`**: Execute JavaScript code

##### State Management
- **`get_state() -> BrowserState`**: Get current browser state
- **`take_screenshot(full_page: bool = False) -> str`**: Capture screenshot
- **`get_tabs_info() -> list[TabInfo]`**: Get information about all tabs

##### Tab Management
- **`switch_to_tab(page_id: int)`**: Switch to specific tab
- **`create_new_tab(url: str = None)`**: Create new tab
- **`close_current_tab()`**: Close active tab

##### Element Interaction
- **`get_locate_element(element: DOMElementNode) -> ElementHandle`**: Locate DOM element
- **`_click_element_node(element: DOMElementNode)`**: Click element
- **`_input_text_element_node(element: DOMElementNode, text: str)`**: Input text

### 3. Data Models (views.py)

#### TabInfo
```python
class TabInfo(BaseModel):
    page_id: int        # Unique tab identifier
    url: str           # Tab URL
    title: str         # Tab title
```

#### BrowserState
```python
@dataclass
class BrowserState(DOMState):
    url: str                           # Current page URL
    title: str                         # Page title
    tabs: list[TabInfo]                # All open tabs
    screenshot: Optional[str] = None   # Base64 encoded screenshot
    pixels_above: int = 0              # Pixels above viewport
    pixels_below: int = 0              # Pixels below viewport
    browser_errors: list[str] = []     # Error messages
```

#### BrowserStateHistory
```python
@dataclass
class BrowserStateHistory:
    url: str                                    # Historical URL
    title: str                                  # Historical title
    tabs: list[TabInfo]                         # Tab state at time
    interacted_element: list[DOMHistoryElement] # Interaction history
    screenshot: Optional[str] = None            # Historical screenshot
```

#### Exception Classes
```python
class BrowserError(Exception):
    """Base class for all browser errors"""

class URLNotAllowedError(BrowserError):
    """Error raised when a URL is not allowed"""
```

## Configuration

### Environment Integration
```python
# Integration with VizCheck config
from config import Config
from browser_use.browser.browser import Browser, BrowserConfig
from browser_use.browser.context import BrowserContextConfig

config = Config()

# Browser configuration
browser_config = BrowserConfig(
    headless=config.get_headless_mode(),
    cdp_url=config.get_cdp_url(),
    disable_security=True
)

# Context configuration
context_config = BrowserContextConfig(
    browser_window_size={'width': 1280, 'height': 1100},
    save_downloads_path=config.get_downloads_path(),
    cookies_file=config.get_cookies_file(),
    allowed_domains=config.get_allowed_domains()
)

browser = Browser(browser_config)
context = await browser.new_context(context_config)
```

### Anti-Detection Features

The browser module includes comprehensive anti-detection measures:

```javascript
// Injected anti-detection script
Object.defineProperty(navigator, 'webdriver', {
    get: () => undefined
});

Object.defineProperty(navigator, 'languages', {
    get: () => ['en-US']
});

Object.defineProperty(navigator, 'plugins', {
    get: () => [1, 2, 3, 4, 5]
});

window.chrome = { runtime: {} };
```

### Security Configuration

```python
# Security arguments automatically applied
disable_security_args = [
    '--disable-web-security',
    '--disable-site-isolation-trials',
    '--disable-features=IsolateOrigins,site-per-process'
]

# Standard Chrome arguments for automation
standard_args = [
    '--no-sandbox',
    '--disable-blink-features=AutomationControlled',
    '--disable-infobars',
    '--disable-background-timer-throttling',
    '--disable-popup-blocking',
    '--disable-backgrounding-occluded-windows',
    '--disable-renderer-backgrounding',
    '--disable-window-activation',
    '--disable-focus-on-load',
    '--no-first-run',
    '--no-default-browser-check',
    '--no-startup-window',
    '--window-position=0,0'
]
```

## Usage Examples

### Basic Browser Setup
```python
from browser_use.browser.browser import Browser, BrowserConfig
from browser_use.browser.context import BrowserContextConfig

# Create browser instance
browser_config = BrowserConfig(
    headless=False,
    disable_security=True
)
browser = Browser(browser_config)

# Create context
context_config = BrowserContextConfig(
    browser_window_size={'width': 1280, 'height': 1100},
    highlight_elements=True,
    save_downloads_path="./downloads"
)

async with browser.new_context(context_config) as context:
    # Navigate to page
    await context.navigate_to("https://example.com")
    
    # Get page state
    state = await context.get_state()
    print(f"Page title: {state.title}")
    print(f"Number of tabs: {len(state.tabs)}")
    
    # Take screenshot
    screenshot = await context.take_screenshot()
    
    # Execute JavaScript
    result = await context.execute_javascript("return document.title")
```

### CDP Connection Example
```python
# Connect to existing Chrome instance
config = BrowserConfig(cdp_url="http://localhost:9222")
browser = Browser(config)

async with browser.new_context() as context:
    # Work with existing browser session
    state = await context.get_state()
    print(f"Connected to: {state.url}")
```

### Multi-Tab Management
```python
async with browser.new_context() as context:
    # Create multiple tabs
    await context.create_new_tab("https://google.com")
    await context.create_new_tab("https://github.com")
    
    # Get tab information
    tabs = await context.get_tabs_info()
    for tab in tabs:
        print(f"Tab {tab.page_id}: {tab.title} - {tab.url}")
    
    # Switch between tabs
    await context.switch_to_tab(0)  # Switch to first tab
    state = await context.get_state()
    print(f"Current tab: {state.title}")
```

### Element Interaction
```python
async with browser.new_context() as context:
    await context.navigate_to("https://example.com")
    
    # Get current state with DOM elements
    state = await context.get_state()
    
    # Find and interact with elements
    for index, element in state.selector_map.items():
        if element.tag_name == "button":
            # Click button
            await context._click_element_node(element)
            break
    
    # Input text into form fields
    for index, element in state.selector_map.items():
        if element.tag_name == "input" and element.attributes.get("type") == "text":
            await context._input_text_element_node(element, "Hello World")
            break
```

### Download Handling
```python
context_config = BrowserContextConfig(
    save_downloads_path="./downloads"
)

async with browser.new_context(context_config) as context:
    await context.navigate_to("https://example.com/download-page")
    
    # Click download link - file will be automatically saved
    download_path = await context._click_element_node(download_element)
    if download_path:
        print(f"File downloaded to: {download_path}")
```

### Cookie Management
```python
context_config = BrowserContextConfig(
    cookies_file="./cookies.json"
)

async with browser.new_context(context_config) as context:
    # Cookies are automatically loaded from file
    await context.navigate_to("https://example.com")
    
    # Cookies are automatically saved when context closes
```

## Best Practices

### 1. **Resource Management**
```python
# Always use async context managers
async with browser.new_context() as context:
    # Your automation code here
    pass
# Context is automatically cleaned up

# Or manual cleanup
try:
    context = await browser.new_context()
    # Your code here
finally:
    await context.close()
```

### 2. **Error Handling**
```python
from browser_use.browser.views import BrowserError, URLNotAllowedError

try:
    await context.navigate_to(url)
except URLNotAllowedError as e:
    logger.warning(f"URL not allowed: {e}")
    # Handle restricted URL
except BrowserError as e:
    logger.error(f"Browser error: {e}")
    # Handle browser-specific error
except Exception as e:
    logger.error(f"Unexpected error: {e}")
    # Handle general error
```

### 3. **Performance Optimization**
```python
# Optimize for performance
config = BrowserContextConfig(
    minimum_wait_page_load_time=0.1,        # Reduce wait times
    wait_for_network_idle_page_load_time=0.3,
    highlight_elements=False,                # Disable highlighting
    viewport_expansion=100,                  # Reduce viewport expansion
    include_dynamic_attributes=False         # Disable dynamic attributes
)
```

### 4. **Security Configuration**
```python
# Configure domain restrictions
config = BrowserContextConfig(
    allowed_domains=[
        "example.com",
        "api.example.com",
        "cdn.example.com"
    ]
)

# This will prevent navigation to unauthorized domains
```

## Troubleshooting

### Common Issues

#### 1. Browser Connection Failures
**Symptoms**: `Failed to initialize Playwright browser` or connection timeouts

**Solutions**:
```python
# Check Chrome installation
import subprocess
try:
    result = subprocess.run(['google-chrome', '--version'], capture_output=True, text=True)
    print(f"Chrome version: {result.stdout}")
except FileNotFoundError:
    print("Chrome not found in PATH")

# Test CDP connection
import requests
try:
    response = requests.get('http://localhost:9222/json/version', timeout=2)
    print(f"CDP available: {response.status_code == 200}")
except requests.ConnectionError:
    print("CDP not available")
```

#### 2. Element Location Failures
**Symptoms**: `Element not found` or `Failed to locate element`

**Solutions**:
```python
# Enable element highlighting for debugging
config = BrowserContextConfig(highlight_elements=True)

# Increase viewport expansion
config = BrowserContextConfig(viewport_expansion=1000)

# Include more attributes for better element selection
config = BrowserContextConfig(include_dynamic_attributes=True)

# Debug element selection
async def debug_element_location(context, element):
    try:
        css_selector = context._enhanced_css_selector_for_element(element)
        print(f"CSS Selector: {css_selector}")

        element_handle = await context.get_locate_element(element)
        if element_handle:
            print("Element found successfully")
        else:
            print("Element not found")
    except Exception as e:
        print(f"Error locating element: {e}")
```

#### 3. Page Load Timeouts
**Symptoms**: Pages not loading completely or timing out

**Solutions**:
```python
# Adjust timeout settings
config = BrowserContextConfig(
    minimum_wait_page_load_time=1.0,
    wait_for_network_idle_page_load_time=2.0,
    maximum_wait_page_load_time=10.0
)

# Debug network activity
async def debug_page_load(context):
    page = await context.get_current_page()

    # Monitor network requests
    requests = []
    page.on('request', lambda req: requests.append(req.url))

    await context.navigate_to(url)
    print(f"Total requests: {len(requests)}")
    for req in requests[-5:]:  # Show last 5 requests
        print(f"Request: {req}")
```

#### 4. Memory Leaks
**Symptoms**: Increasing memory usage over time

**Solutions**:
```python
# Proper cleanup
async def automation_with_cleanup():
    browser = None
    context = None
    try:
        browser = Browser(config)
        context = await browser.new_context()

        # Your automation code here

    finally:
        if context:
            await context.close()
        if browser:
            await browser.close()

# Monitor memory usage
import psutil
import os

def monitor_memory():
    process = psutil.Process(os.getpid())
    memory_mb = process.memory_info().rss / 1024 / 1024
    print(f"Memory usage: {memory_mb:.2f} MB")

# Call periodically during automation
```

#### 5. Anti-Detection Issues
**Symptoms**: Websites detecting automation or blocking access

**Solutions**:
```python
# Enhanced anti-detection configuration
config = BrowserConfig(
    headless=False,  # Use non-headless mode
    disable_security=True,
    extra_chromium_args=[
        '--disable-blink-features=AutomationControlled',
        '--exclude-switches=enable-automation',
        '--disable-extensions-except=/path/to/extension',
        '--disable-plugins-discovery',
        '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    ]
)

# Custom user agent and locale
context_config = BrowserContextConfig(
    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    locale="en-US"
)
```

#### 6. Download Issues
**Symptoms**: Downloads not working or files not saved

**Solutions**:
```python
# Ensure download directory exists
import os
download_path = "./downloads"
os.makedirs(download_path, exist_ok=True)

config = BrowserContextConfig(
    save_downloads_path=download_path
)

# Debug download handling
async def debug_download(context, download_element):
    try:
        result = await context._click_element_node(download_element)
        if result:
            print(f"Download successful: {result}")
        else:
            print("No download triggered")
    except Exception as e:
        print(f"Download failed: {e}")
```

### Debug Mode

Enable detailed logging for browser operations:

```python
import logging

# Enable browser module logging
logging.getLogger('browser_use.browser').setLevel(logging.DEBUG)

# Enable Playwright logging
logging.getLogger('playwright').setLevel(logging.DEBUG)

# Custom debug configuration
config = BrowserContextConfig(
    trace_path="./traces",  # Enable Playwright tracing
    save_recording_path="./recordings"  # Enable video recording
)
```

### Performance Monitoring

```python
import time
from browser_use.utils import time_execution_async

@time_execution_async('page_navigation')
async def navigate_with_timing(context, url):
    start_time = time.time()
    await context.navigate_to(url)
    end_time = time.time()
    print(f"Navigation took: {end_time - start_time:.2f} seconds")

# Monitor browser resource usage
async def monitor_browser_resources(context):
    page = await context.get_current_page()

    # Get performance metrics
    metrics = await page.evaluate("""
        () => {
            const navigation = performance.getEntriesByType('navigation')[0];
            return {
                domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                totalTime: navigation.loadEventEnd - navigation.fetchStart
            };
        }
    """)

    print(f"Page performance: {metrics}")
```

### Testing Browser Components

```python
# Test browser initialization
async def test_browser_setup():
    try:
        browser = Browser(BrowserConfig())
        playwright_browser = await browser.get_playwright_browser()
        print(f"Browser initialized: {playwright_browser.version}")
        await browser.close()
        return True
    except Exception as e:
        print(f"Browser setup failed: {e}")
        return False

# Test context creation
async def test_context_creation():
    try:
        browser = Browser(BrowserConfig())
        async with browser.new_context() as context:
            state = await context.get_state()
            print(f"Context created successfully")
            return True
    except Exception as e:
        print(f"Context creation failed: {e}")
        return False

# Run tests
async def run_browser_tests():
    tests = [
        ("Browser Setup", test_browser_setup),
        ("Context Creation", test_context_creation)
    ]

    for test_name, test_func in tests:
        result = await test_func()
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
```

---

**Document Version**: 1.0
**Last Updated**: July 2025
**Component Version**: VizCheck 2.0.0
