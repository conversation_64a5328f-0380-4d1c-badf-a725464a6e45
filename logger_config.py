import logging
from config import Config

def setup_logger(name=None):
    """
    Sets up a logger with the specified name or the module's name if not provided.
    Configures the logger to log messages at the DEBUG level and above, and adds a console handler.
    Args:
        name (str, optional): The name of the logger. Defaults to None, which uses the module's name.
    Returns:
        logging.Logger: The configured logger instance.
    """
    logger = logging.getLogger(name)
    log_type = Config.get_logging_level()
    if not logger.handlers:
        if log_type == 'debug':
            logger.setLevel(logging.DEBUG)
        else:
            logger.setLevel(logging.INFO)

        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)


        formatter = logging.Formatter('[%(asctime)s] %(name)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)

        logger.addHandler(console_handler)

        logger.propagate = False

    return logger