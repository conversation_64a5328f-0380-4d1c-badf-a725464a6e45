# VizCheck Test Suite

Professional testing framework for VizCheck application functionality.

## Quick Start

```bash
# Run all tests
pytest

# Run with verbose output
pytest -v

# Run specific test module
pytest tests/test_all_pytest.py -v

# Run tests by pattern
pytest -k "config" -v
```

## Test Coverage

| Module | Tests | Status |
|--------|-------|--------|
| Core Functions | 14 | ✅ |
| Root Functions | 38 | ✅ |
| Report Generator | 10 | ✅ |
| Ticket API | 19 | ✅ |
| Azure DevOps | 17 | ✅ |
| Browser_Use | 5 | ✅ |

**Total: 103 tests across all modules**

## Test Structure

```
tests/
├── test_all_pytest.py          # Core functionality tests (14 tests)
├── test_root_functions.py      # Comprehensive tests (38 tests)
├── test_report_generator.py    # Report generation tests (10 tests)
├── test_ticket_api.py          # API endpoint tests (19 tests)
├── test_ado_workitem.py        # Azure DevOps tests (17 tests)
├── conftest.py                 # Pytest configuration
├── requirements_test.txt       # Test dependencies
├── test_results/               # Test outputs
├── test_outputs/               # Test artifacts
└── README.md                   # This documentation
```

## Usage Examples

```bash
# Run all tests
pytest

# Run specific test class
pytest tests/test_all_pytest.py::TestConfig -v

# Run tests by pattern
pytest -k "config" -v

# Run with coverage
pytest --cov=. --cov-report=html

# Stop on first failure
pytest -x
```

## Test Categories

### Core Functionality
- **TestConfig**: Configuration management
- **TestUtils**: Utility functions and decorators
- **TestBrowserManager**: Chrome browser management
- **TestConstants**: Application constants
- **TestReportGenerator**: Report generation

### API and Integration
- **TestTicketAPI**: FastAPI endpoint testing
- **TestADOWorkItem**: Azure DevOps integration

## Features

- **Pytest-based**: Modern testing framework
- **Automatic Discovery**: Tests found automatically
- **Fixtures and Mocking**: Isolated test environments
- **Coverage Reporting**: Code coverage analysis
- **Professional Output**: Clean test reporting

## Debugging

```bash
# Check test discovery
pytest --collect-only

# Verbose debugging
pytest -vvv -s

# Run with debugger
pytest --pdb
```

## Environment Setup

```bash
# Activate virtual environment
venv\Scripts\activate.ps1

# Install dependencies
pip install -r requirements.txt
pip install -r tests/requirements_test.txt
```

---

**Framework**: pytest 7.4+ | **Python**: 3.11+ | **Status**: Production Ready
